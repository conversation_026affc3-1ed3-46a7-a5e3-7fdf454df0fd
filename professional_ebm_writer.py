# professional_ebm_writer.py
"""
专业循证医学写作器
基于真实文献数据和新生成的题目进行专业写作
避免硬编码内容，确保语言纯净性
"""

import logging
import re
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class StudyCharacteristics:
    """研究特征数据结构"""
    total_studies: int
    study_designs: Dict[str, int]
    populations: List[str]
    interventions: List[str]
    outcomes: List[str]
    years_range: str
    sample_sizes: Dict[str, int]
    quality_distribution: Dict[str, int]


class ProfessionalEBMWriter:
    """
    专业循证医学写作器
    基于真实文献数据生成高质量的循证医学内容
    """
    
    def __init__(self, llm_manager):
        self.llm = llm_manager
        self.study_cache = {}  # 缓存研究特征，避免重复分析
        
    def generate_professional_title(self, topic: str, studies: List[Dict[str, Any]], 
                                  is_chinese: bool = True) -> str:
        """
        基于真实研究数据生成专业标题
        """
        try:
            characteristics = self._extract_study_characteristics(studies)
            
            # 提取主要干预和人群
            main_intervention = self._get_main_intervention(characteristics)
            main_population = self._get_main_population(characteristics)
            main_outcome = self._get_main_outcome(characteristics)
            
            if is_chinese:
                if characteristics.total_studies >= 10:
                    title = f"{main_intervention}在{main_population}中对{main_outcome}影响的系统评价与Meta分析"
                else:
                    title = f"{main_intervention}在{main_population}中的应用：文献综述"
            else:
                if characteristics.total_studies >= 10:
                    title = f"Systematic Review and Meta-Analysis of {main_intervention} for {main_outcome} in {main_population}"
                else:
                    title = f"{main_intervention} in {main_population}: A Literature Review"
            
            return title
            
        except Exception as e:
            logger.error(f"生成专业标题失败: {e}")
            return topic if topic else ("循证医学研究" if is_chinese else "Evidence-Based Medicine Study")
    
    def generate_structured_abstract(self, topic: str, studies: List[Dict[str, Any]], 
                                   provider: str, model: str, is_chinese: bool = True) -> str:
        """
        生成结构化摘要，基于真实研究数据
        """
        try:
            characteristics = self._extract_study_characteristics(studies)
            
            # 确保语言纯净性
            language_instruction = "请完全使用中文回答，不要包含任何英文内容。" if is_chinese else "Please respond entirely in English, do not include any Chinese content."
            
            if is_chinese:
                prompt = f"""
{language_instruction}

你是循证医学专家，请基于以下真实研究数据为主题"{topic}"撰写结构化摘要：

研究特征：
- 纳入研究：{characteristics.total_studies}项
- 研究设计：{self._format_study_designs(characteristics.study_designs)}
- 研究人群：{', '.join(characteristics.populations[:3])}
- 主要干预：{', '.join(characteristics.interventions[:3])}
- 主要结局：{', '.join(characteristics.outcomes[:3])}
- 发表年份：{characteristics.years_range}
- 总样本量：{characteristics.sample_sizes.get('total', 0)}人

请按以下结构撰写摘要（400-500字）：

**背景：** 简述临床问题的重要性和现有治疗的局限性（80-100字）

**目的：** 明确本研究的主要目标和研究问题（60-80字）

**方法：** 描述检索策略、纳入标准、质量评估和数据分析方法（120-150字）

**结果：** 报告主要发现，包括纳入研究特征和关键结局（100-120字）

**结论：** 基于证据得出的主要结论和临床意义（60-80字）

要求：
1. 完全基于提供的真实数据
2. 使用专业医学术语
3. 避免夸大或虚构结果
4. 确保逻辑清晰、结构完整
"""
            else:
                prompt = f"""
{language_instruction}

You are an evidence-based medicine expert. Write a structured abstract for the topic "{topic}" based on the following real study data:

Study Characteristics:
- Included Studies: {characteristics.total_studies} studies
- Study Designs: {self._format_study_designs(characteristics.study_designs)}
- Populations: {', '.join(characteristics.populations[:3])}
- Main Interventions: {', '.join(characteristics.interventions[:3])}
- Main Outcomes: {', '.join(characteristics.outcomes[:3])}
- Publication Years: {characteristics.years_range}
- Total Sample Size: {characteristics.sample_sizes.get('total', 0)} participants

Write the abstract following this structure (300-400 words):

**Background:** Briefly describe the clinical importance and limitations of current treatments (60-80 words)

**Objective:** Clearly state the main goal and research question of this study (40-60 words)

**Methods:** Describe search strategy, inclusion criteria, quality assessment, and data analysis methods (80-100 words)

**Results:** Report main findings, including characteristics of included studies and key outcomes (80-100 words)

**Conclusions:** State main conclusions and clinical implications based on evidence (40-60 words)

Requirements:
1. Base entirely on the provided real data
2. Use professional medical terminology
3. Avoid exaggeration or fabrication
4. Ensure clear logic and complete structure
"""
            
            response = self.llm.generate_content(prompt, provider, model)
            
            # 验证语言纯净性
            if is_chinese and self._contains_english(response):
                logger.warning("中文摘要包含英文内容，重新生成")
                return self._regenerate_clean_content(prompt, provider, model, is_chinese)
            elif not is_chinese and self._contains_chinese(response):
                logger.warning("英文摘要包含中文内容，重新生成")
                return self._regenerate_clean_content(prompt, provider, model, is_chinese)
            
            return response if response and not response.startswith("ERROR") else "摘要生成失败"
            
        except Exception as e:
            logger.error(f"生成结构化摘要失败: {e}")
            return "摘要生成失败" if is_chinese else "Abstract generation failed"
    
    def generate_introduction(self, topic: str, studies: List[Dict[str, Any]], 
                            provider: str, model: str, is_chinese: bool = True) -> str:
        """
        生成引言部分，基于真实研究背景
        """
        try:
            characteristics = self._extract_study_characteristics(studies)
            
            # 确保语言纯净性
            language_instruction = "请完全使用中文回答，不要包含任何英文内容。" if is_chinese else "Please respond entirely in English, do not include any Chinese content."
            
            if is_chinese:
                prompt = f"""
{language_instruction}

你是循证医学专家，请为主题"{topic}"撰写专业的引言部分。

基于真实研究数据：
- 相关研究：{characteristics.total_studies}项
- 主要研究类型：{self._get_primary_study_types(characteristics.study_designs)}
- 研究时间跨度：{characteristics.years_range}
- 涉及人群：{', '.join(characteristics.populations[:3])}

请按以下结构撰写引言（600-800字）：

**临床背景（200-250字）：**
- 描述{topic}的临床重要性和流行病学特征
- 阐述当前的诊疗现状和挑战
- 说明该领域的临床需求

**研究现状（200-250字）：**
- 总结现有研究的主要发现
- 分析当前证据的质量和局限性
- 指出知识空白和争议点

**研究目的（150-200字）：**
- 明确本研究的主要目标
- 阐述研究的科学意义和临床价值
- 说明预期的贡献和影响

要求：
1. 基于真实的医学背景知识
2. 体现专业的学术水平
3. 逻辑清晰，层次分明
4. 避免重复和冗余
"""
            else:
                prompt = f"""
{language_instruction}

You are an evidence-based medicine expert. Write a professional introduction for the topic "{topic}".

Based on real study data:
- Related Studies: {characteristics.total_studies} studies
- Main Study Types: {self._get_primary_study_types(characteristics.study_designs)}
- Study Time Span: {characteristics.years_range}
- Study Populations: {', '.join(characteristics.populations[:3])}

Write the introduction following this structure (500-700 words):

**Clinical Background (150-200 words):**
- Describe the clinical importance and epidemiological characteristics of {topic}
- Explain current diagnostic and therapeutic status and challenges
- Illustrate clinical needs in this field

**Current Research Status (150-200 words):**
- Summarize main findings of existing studies
- Analyze quality and limitations of current evidence
- Identify knowledge gaps and controversies

**Study Objectives (100-150 words):**
- Clearly state the main goals of this study
- Explain the scientific significance and clinical value
- Describe expected contributions and impact

Requirements:
1. Based on real medical background knowledge
2. Demonstrate professional academic level
3. Clear logic and well-structured
4. Avoid repetition and redundancy
"""
            
            response = self.llm.generate_content(prompt, provider, model)
            
            # 验证语言纯净性
            if is_chinese and self._contains_english(response):
                logger.warning("中文引言包含英文内容，重新生成")
                return self._regenerate_clean_content(prompt, provider, model, is_chinese)
            elif not is_chinese and self._contains_chinese(response):
                logger.warning("英文引言包含中文内容，重新生成")
                return self._regenerate_clean_content(prompt, provider, model, is_chinese)
            
            return response if response and not response.startswith("ERROR") else "引言生成失败"
            
        except Exception as e:
            logger.error(f"生成引言失败: {e}")
            return "引言生成失败" if is_chinese else "Introduction generation failed"

    def _extract_study_characteristics(self, studies: List[Dict[str, Any]]) -> StudyCharacteristics:
        """提取研究特征"""
        if not studies:
            return StudyCharacteristics(0, {}, [], [], [], "N/A", {}, {})

        # 缓存检查
        cache_key = f"studies_{len(studies)}_{hash(str(studies[:3]))}"
        if cache_key in self.study_cache:
            return self.study_cache[cache_key]

        study_designs = {}
        populations = []
        interventions = []
        outcomes = []
        years = []
        sample_sizes = []

        for study in studies:
            # 研究设计
            design = self._extract_study_design(study)
            study_designs[design] = study_designs.get(design, 0) + 1

            # 人群
            population = self._extract_population(study)
            if population and population not in populations:
                populations.append(population)

            # 干预
            intervention = self._extract_intervention(study)
            if intervention and intervention not in interventions:
                interventions.append(intervention)

            # 结局
            outcome = self._extract_outcomes(study)
            if outcome and outcome not in outcomes:
                outcomes.append(outcome)

            # 年份
            year = study.get('year', 'N/A')
            if year != 'N/A':
                try:
                    years.append(int(year))
                except (ValueError, TypeError):
                    pass

            # 样本量
            sample_size = self._extract_sample_size(study)
            if sample_size > 0:
                sample_sizes.append(sample_size)

        # 计算年份范围
        years_range = f"{min(years)}-{max(years)}" if years else "N/A"

        # 计算样本量统计
        sample_stats = {
            'total': sum(sample_sizes),
            'min': min(sample_sizes) if sample_sizes else 0,
            'max': max(sample_sizes) if sample_sizes else 0,
            'median': sorted(sample_sizes)[len(sample_sizes)//2] if sample_sizes else 0
        }

        characteristics = StudyCharacteristics(
            total_studies=len(studies),
            study_designs=study_designs,
            populations=populations[:10],  # 限制数量
            interventions=interventions[:10],
            outcomes=outcomes[:10],
            years_range=years_range,
            sample_sizes=sample_stats,
            quality_distribution={}  # 将在需要时填充
        )

        # 缓存结果
        self.study_cache[cache_key] = characteristics
        return characteristics

    def _extract_study_design(self, study: Dict[str, Any]) -> str:
        """提取研究设计"""
        # 从clinical_data中提取
        clinical_data = study.get('clinical_data', {})
        design = clinical_data.get('study_design', '')

        if design:
            return design

        # 从标题和摘要中推断
        title = study.get('title', '').lower()
        abstract = study.get('abstract', '').lower()
        text = f"{title} {abstract}"

        if any(keyword in text for keyword in ['randomized', 'rct', 'random']):
            return 'Randomized Controlled Trial'
        elif any(keyword in text for keyword in ['cohort', 'prospective']):
            return 'Cohort Study'
        elif any(keyword in text for keyword in ['case-control', 'case control']):
            return 'Case-Control Study'
        elif any(keyword in text for keyword in ['cross-sectional', 'cross sectional']):
            return 'Cross-sectional Study'
        elif any(keyword in text for keyword in ['systematic review', 'meta-analysis']):
            return 'Systematic Review'
        elif any(keyword in text for keyword in ['case report', 'case series']):
            return 'Case Report'
        else:
            return 'Observational Study'

    def _extract_population(self, study: Dict[str, Any]) -> str:
        """提取研究人群"""
        clinical_data = study.get('clinical_data', {})
        population = clinical_data.get('population', '')

        if population:
            return population[:100]  # 限制长度

        # 从摘要中提取
        abstract = study.get('abstract', '')

        # 常见人群模式
        patterns = [
            r'patients?\s+with\s+([^.]+)',
            r'adults?\s+with\s+([^.]+)',
            r'children\s+with\s+([^.]+)',
            r'(\d+)\s+patients?',
            r'participants?\s+with\s+([^.]+)'
        ]

        for pattern in patterns:
            match = re.search(pattern, abstract, re.IGNORECASE)
            if match:
                return match.group(1)[:100] if len(match.groups()) > 0 else match.group(0)[:100]

        return "General population"

    def _extract_intervention(self, study: Dict[str, Any]) -> str:
        """提取干预措施"""
        clinical_data = study.get('clinical_data', {})
        intervention = clinical_data.get('intervention', '')

        if intervention:
            return intervention[:100]

        # 从标题和摘要中提取
        title = study.get('title', '')
        abstract = study.get('abstract', '')
        text = f"{title} {abstract}"

        # 常见干预模式
        patterns = [
            r'treatment\s+with\s+([^.]+)',
            r'therapy\s+with\s+([^.]+)',
            r'administration\s+of\s+([^.]+)',
            r'use\s+of\s+([^.]+)',
            r'management\s+with\s+([^.]+)'
        ]

        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1)[:100]

        return "Standard care"

    def _extract_outcomes(self, study: Dict[str, Any]) -> str:
        """提取结局指标"""
        clinical_data = study.get('clinical_data', {})
        outcomes = clinical_data.get('outcomes', '')

        if outcomes:
            return outcomes[:100]

        # 从摘要中查找常见结局
        abstract = study.get('abstract', '').lower()

        outcome_keywords = [
            'mortality', 'survival', 'death', 'efficacy', 'safety',
            'adverse events', 'complications', 'recovery', 'improvement',
            'response rate', 'progression', 'recurrence'
        ]

        found_outcomes = []
        for keyword in outcome_keywords:
            if keyword in abstract:
                found_outcomes.append(keyword)

        return ', '.join(found_outcomes[:3]) if found_outcomes else "Clinical outcomes"

    def _extract_sample_size(self, study: Dict[str, Any]) -> int:
        """提取样本量"""
        clinical_data = study.get('clinical_data', {})
        sample_size = clinical_data.get('sample_size', 0)

        if isinstance(sample_size, int) and sample_size > 0:
            return sample_size

        # 从摘要中提取
        abstract = study.get('abstract', '')

        # 查找样本量模式
        patterns = [
            r'[Nn]=?\s*(\d+)',
            r'(\d+)\s+patients?',
            r'(\d+)\s+participants?',
            r'(\d+)\s+subjects?'
        ]

        for pattern in patterns:
            matches = re.findall(pattern, abstract)
            if matches:
                try:
                    return int(matches[0])
                except (ValueError, TypeError):
                    continue

        return 0

    def _contains_english(self, text: str) -> bool:
        """检查文本是否包含英文内容"""
        if not text:
            return False

        # 检查是否有英文字母组成的单词
        english_words = re.findall(r'\b[a-zA-Z]+\b', text)

        # 过滤掉常见的医学缩写和专有名词
        allowed_english = {
            'PICO', 'PRISMA', 'ROB', 'GRADE', 'RCT', 'OR', 'CI', 'HR', 'RR', 'MD', 'SMD',
            'COVID', 'HIV', 'AIDS', 'ICU', 'WHO', 'FDA', 'NICE', 'AHA', 'ESC', 'ACC',
            'vs', 'et', 'al', 'p', 'n', 'N', 'I²', 'Q', 'df'
        }

        significant_english = [word for word in english_words if word not in allowed_english and len(word) > 2]

        # 如果英文单词超过总字数的10%，认为包含显著英文内容
        total_chars = len(re.sub(r'\s+', '', text))
        english_chars = sum(len(word) for word in significant_english)

        return english_chars > total_chars * 0.1

    def _contains_chinese(self, text: str) -> bool:
        """检查文本是否包含中文内容"""
        if not text:
            return False

        # 检查是否有中文字符
        chinese_chars = re.findall(r'[\u4e00-\u9fff]', text)

        # 如果中文字符超过5个，认为包含中文内容
        return len(chinese_chars) > 5

    def _regenerate_clean_content(self, prompt: str, provider: str, model: str, is_chinese: bool) -> str:
        """重新生成纯净语言内容"""
        try:
            # 强化语言要求
            if is_chinese:
                clean_prompt = f"""
严格要求：必须完全使用中文回答，绝对不能包含任何英文单词或句子（医学专业缩写除外，如PICO、GRADE等）。

{prompt}

再次强调：回答必须是纯中文内容。
"""
            else:
                clean_prompt = f"""
Strict requirement: Must respond entirely in English, absolutely no Chinese characters or words allowed.

{prompt}

Emphasis again: The response must be pure English content.
"""

            response = self.llm.generate_content(clean_prompt, provider, model)

            # 再次验证
            if is_chinese and self._contains_english(response):
                logger.error("重新生成后仍包含英文内容")
                return "内容生成失败：语言混用问题"
            elif not is_chinese and self._contains_chinese(response):
                logger.error("重新生成后仍包含中文内容")
                return "Content generation failed: Language mixing issue"

            return response

        except Exception as e:
            logger.error(f"重新生成纯净内容失败: {e}")
            return "内容生成失败" if is_chinese else "Content generation failed"

    def _format_study_designs(self, study_designs: Dict[str, int]) -> str:
        """格式化研究设计分布"""
        if not study_designs:
            return "未明确"

        formatted = []
        for design, count in sorted(study_designs.items(), key=lambda x: x[1], reverse=True):
            formatted.append(f"{design}({count}项)")

        return ', '.join(formatted[:3])  # 只显示前3种

    def _get_main_intervention(self, characteristics: StudyCharacteristics) -> str:
        """获取主要干预措施"""
        if not characteristics.interventions:
            return "治疗干预"

        # 选择最常见或最具代表性的干预
        main_intervention = characteristics.interventions[0]

        # 简化长描述
        if len(main_intervention) > 20:
            main_intervention = main_intervention[:20] + "..."

        return main_intervention

    def _get_main_population(self, characteristics: StudyCharacteristics) -> str:
        """获取主要研究人群"""
        if not characteristics.populations:
            return "患者"

        main_population = characteristics.populations[0]

        # 简化长描述
        if len(main_population) > 15:
            main_population = main_population[:15] + "..."

        return main_population

    def _get_main_outcome(self, characteristics: StudyCharacteristics) -> str:
        """获取主要结局指标"""
        if not characteristics.outcomes:
            return "临床结局"

        main_outcome = characteristics.outcomes[0]

        # 简化长描述
        if len(main_outcome) > 15:
            main_outcome = main_outcome[:15] + "..."

        return main_outcome

    def _get_primary_study_types(self, study_designs: Dict[str, int]) -> str:
        """获取主要研究类型"""
        if not study_designs:
            return "观察性研究"

        # 按数量排序，返回前2种
        sorted_designs = sorted(study_designs.items(), key=lambda x: x[1], reverse=True)
        primary_types = [design for design, _ in sorted_designs[:2]]

        return ', '.join(primary_types)

    def generate_methods_section(self, topic: str, studies: List[Dict[str, Any]],
                                provider: str, model: str, is_chinese: bool = True) -> str:
        """
        生成方法部分，针对综述类型简化处理
        """
        try:
            characteristics = self._extract_study_characteristics(studies)

            # 确保语言纯净性
            language_instruction = "请完全使用中文回答，不要包含任何英文内容。" if is_chinese else "Please respond entirely in English, do not include any Chinese content."

            if is_chinese:
                prompt = f"""
{language_instruction}

你是循证医学专家，请为主题"{topic}"的文献综述撰写方法部分。

基于真实数据：
- 纳入文献：{characteristics.total_studies}篇
- 文献类型：{self._format_study_designs(characteristics.study_designs)}
- 时间范围：{characteristics.years_range}

请撰写方法部分（400-500字），包括：

**文献检索策略（150字）：**
- 检索数据库和时间范围
- 检索关键词和策略
- 语言和文献类型限制

**纳入与排除标准（150字）：**
- 研究类型要求
- 研究人群标准
- 干预措施要求
- 结局指标要求

**文献筛选与数据提取（100-150字）：**
- 文献筛选流程
- 数据提取内容
- 质量控制措施

要求：
1. 基于真实的文献特征
2. 适合综述类型的方法学
3. 避免过度复杂的统计分析描述
4. 确保方法的可重复性
"""
            else:
                prompt = f"""
{language_instruction}

You are an evidence-based medicine expert. Write the methods section for a literature review on "{topic}".

Based on real data:
- Included Literature: {characteristics.total_studies} studies
- Study Types: {self._format_study_designs(characteristics.study_designs)}
- Time Range: {characteristics.years_range}

Write the methods section (300-400 words), including:

**Literature Search Strategy (100 words):**
- Search databases and time range
- Search keywords and strategy
- Language and publication type restrictions

**Inclusion and Exclusion Criteria (100 words):**
- Study type requirements
- Population criteria
- Intervention requirements
- Outcome requirements

**Literature Screening and Data Extraction (100 words):**
- Screening process
- Data extraction content
- Quality control measures

Requirements:
1. Based on real literature characteristics
2. Appropriate methodology for review type
3. Avoid overly complex statistical analysis descriptions
4. Ensure reproducibility of methods
"""

            response = self.llm.generate_content(prompt, provider, model)

            # 验证语言纯净性
            if is_chinese and self._contains_english(response):
                logger.warning("中文方法部分包含英文内容，重新生成")
                return self._regenerate_clean_content(prompt, provider, model, is_chinese)
            elif not is_chinese and self._contains_chinese(response):
                logger.warning("英文方法部分包含中文内容，重新生成")
                return self._regenerate_clean_content(prompt, provider, model, is_chinese)

            return response if response and not response.startswith("ERROR") else "方法部分生成失败"

        except Exception as e:
            logger.error(f"生成方法部分失败: {e}")
            return "方法部分生成失败" if is_chinese else "Methods section generation failed"

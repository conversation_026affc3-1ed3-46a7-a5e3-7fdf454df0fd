<!-- ============================================================= -->
<!--  MODULE:    Custom Special Characters Module                  -->
<!--  VERSION:   ANSI/NISO JATS Version 1.3 (Z39.96-2021)          -->
<!--  DATE:      June 2021                                         -->
<!--                                                               -->
<!-- ============================================================= -->

<!-- ============================================================= -->
<!--                    PUBLIC DOCUMENT TYPE DEFINITION            -->
<!--                        TYPICAL INVOCATION                     -->
<!--
"-//NLM//DTD JATS (Z39.96) JATS DTD Suite Custom Special Characters Module v1.3 20210610//EN"
     Delivered as file "JATS-chars1-3.ent"                         -->
<!-- ============================================================= -->

<!-- ============================================================= -->
<!-- SYSTEM:     JATS DTD Suite                                    -->
<!--                                                               -->
<!-- PURPOSE:    XML special character entities                    -->
<!--                                                               -->
<!-- CONTAINS:   1) Definitions of DTD-specific and custom         -->
<!--                special characters (as general entities        -->
<!--                defined as hexadecimal or decimal character    -->
<!--                entities - Unicode numbers)                    -->
<!--                                                               -->
<!-- TAG SET SPONSOR                                               -->
<!--             National Center for Biotechnology                 -->
<!--                Information (NCBI)                             -->
<!--             National Library of Medicine (NLM)                -->
<!--                                                               -->
<!-- CREATED FOR:                                                  -->
<!--             This module was created for the JATS DTD Suite.   -->
<!--             Digital archives and publishers may use the       -->
<!--             DTD as is for markup of journal literature or     -->
<!--             related material for archiving and transferring   -->
<!--             such material between archives or create a        -->
<!--             custom XML DTD from the Suite for                 -->
<!--             these purposes.                                   -->
<!--                                                               -->
<!--             This DTD is in the public domain. An organization -->
<!--             that wishes to create its own DTD from the suite  -->
<!--             may do so without permission from NLM.            -->
<!--                                                               -->
<!--             The suite has been set up to be extended using a  -->
<!--             new DTD file and a new DTD-specific customization -->
<!--             module to redefine the many Parameter Entities.   -->
<!--             Do not modify the suite directly or redistribute  -->
<!--             modified versions of the suite.                   -->
<!--                                                               -->
<!--             In the interest of maintaining consistency and    -->
<!--             clarity for potential users, NLM requests:        -->
<!--                                                               -->
<!--             1. If you create a DTD from the JATS DTD Suite    -->
<!--                and intend to stay compatible with the suite,  -->
<!--                then please include the following statement    -->
<!--                as a comment in all of your DTD modules:       -->
<!--                   "Created from, and fully compatible with,   -->
<!--                    the ANSI/NISO Z39.96 Journal Article Tag   -->
<!--                    Suite (JATS)."                             -->
<!--                                                               -->
<!--             2. If you alter one or more modules of the suite, -->
<!--                then please rename your version and all its    -->
<!--                modules to avoid any confusion with the        -->
<!--                original suite. Also, please include the       -->
<!--                following statement as a comment in all your   -->
<!--                DTD modules:                                   -->
<!--                   "Based in part on, but not fully compatible -->
<!--                    with, the ANSI/NISO Z39.96 Journal Article -->
<!--                    Tag Suite (JATS)."                         -->
<!--                                                               -->
<!-- ORIGINAL CREATION DATE:                                       -->
<!--             December 2002                                     -->
<!--                                                               -->
<!-- CREATED BY: Mulberry Technologies, Inc. for the NISO Z39.96   -->
<!--             Working Group. Mulberry Technologies was          -->
<!--             supported by National Center for Biotechnology    -->
<!--             Information (NCBI), a center of the US National   -->
<!--             Library of Medicine (NLM).                        -->
<!--                                                               -->
<!--             This module is part of the JATS DTD Suite. The    -->
<!--             Suite is a continuation of work done by NCBI,     -->
<!--             Mulberry Technologies, and Inera, Inc. on the NLM -->
<!--             Journal Archiving and Interchange DTD Suite, which-->
<!--             was originally released in December, 2002.        -->
<!--                                                               -->
<!--             NLM thanks the Harvard University Libraries, both -->
<!--             for proposing that a draft archiving NLM DTD for  -->
<!--             life sciences journals be extended to accommodate -->
<!--             journals in all disciplines and for sponsoring    -->
<!--             Inera Inc.'s collaboration with other DTD         -->
<!--             authors in completing NLM Version 1.0. The        -->
<!--             Andrew W. Mellon Foundation provided support for  -->
<!--             these important contributions.                    -->
<!--                                                               -->
<!--             Suggestions for refinements and enhancements to   -->
<!--             this DTD should be sent in email to:              -->
<!--                 <EMAIL>                         -->
<!-- ============================================================= -->


<!-- ============================================================= -->
<!--                    DTD VERSION/CHANGE HISTORY                 -->
<!-- ============================================================= -->
<!--
Version  Reason/Occasion                   (who) vx.x (yyyy-mm-dd)
   ==============================================================
     JATS Version 1.3 (ANSI/NISO Z39.96-2021)
                                  (DAL/BTU) v1.3 (2021-06-10)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by both the NISO and ANSI memberships 
     to be changed. This version of the NISO JATS was approved 
     by ANSI vote on June 10, 2021, so it supersedes all Committee 
     Drafts as well as the draft version JATS 1.3 2021 (the version
     submitted for vote), becoming NISO JATS v1.3 20210610 
     (ANSI/NISO Z39.96-2019).

     The file names and formal public identifiers have been 
     changed in the modules and the catalogs.

 19. JATS 'v1.3d2 20201130' becomes 'v1.3 20210610' 
     (final ANSI vote date)
     'ANSI/NISO Z39.96-2019' becomes 'ANSI/NISO Z39.96-2021'

  ==============================================================
     JATS Version 1.3d2 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.3d2 (2020-11-30)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by the NISO and ANSI memberships 
     to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     of the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting; it is for Standing Committee review and
     testing. The formal public identifiers have been changed 
     in the modules and the catalogs.
 
 18. JATS "v1.3d2 20201130" becomes 'v1.3 20210610' (second 
     committee draft following the final ANSI/NISO 1.2 vote)

   ==============================================================
     JATS Version 1.3d1 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.3.d1 (2019-08-31)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by the NISO and ANSI memberships 
     to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     of the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
  
 17. JATS "1.2" becomes "v1.3d1 20190831" (first committee
     draft following the final ANSI/NISO vote) 

   ==============================================================
     JATS Version 1.2 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.2     (2019-02-08)
    
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the both the NISO and ANSI membership 
     to be changed. This version of the NISO JATS was approved 
     by ANSI vote on Feb 08, 2019, so it supersedes all Committee 
     Drafts as well as the draft version JATS 1.2 2018 (the version
     submitted for vote), becoming NISO JATS v1.2 20190208 
     (ANSI/NISO Z39.96-2021).
  
 16. JATS "1.2" becomes "v1.2 20190208" (final ANSI vote date)
     "ANSI/NISO Z39.96-2015" becomes "ANSI/NISO Z39.96-2019"
     
   ==============================================================
     JATS Version 1.2 (ANSI/NISO Z39.96-2018)
                                   (DAL/BTU) v1.2     (2018-11-30)
    
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the both the NISO and ANSI membership 
     to be changed. This version of the NISO JATS was approved 
     by ANSI and NISO vote, so it supersedes all Committee Drafts
     and becoming NISO JATS 1.2 (ANSI/NISO Z39.96-2018).
  
 15. JATS "1.2d2" and "v1.2d2 20180401" became
     JATS "1.2" and "v1.2 20181130" 

    =============================================================
     JATS Version 1.2d2              (DAL/BTU) v1.2d2  (2018-04-01)

     JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
     BITS is under continuous maintenance, and is modified at the
     discretion of the working group.
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.

 14. BITS "2.0" and "v2.0 20151225" remain unchanged
   
     JATS "1.2d1" and "v1.2d1 20171231" became
     JATS "1.2d2" and "v1.2d2 20180401". 

     No module names were changed.
 
    =============================================================
     JATS Version 1.2d1 (ANSI/NISO Z39.96-2015)
                                   (DAL/BTU) v1.2d1  (2017-12-31)
 
     NISO JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
  
 13. JATS "1.2d1" and "v1.2d1 20170631" became
     JATS "1.2d1" and "v1.2d1 20171231". 

    =============================================================
     JATS Version 1.2d1              (DAL/BTU) v1.2d1  (2017-06-31)

     NISO JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
 
 12. JATS became version "1.2d1" and "v1.2d1 20170631" 

     =============================================================
     JATS Version 1.1                (DAL/BTU) v1.1  (2015-12-15)

     JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
     JATS 1.1 was approved in late 2015, so the formal public 
     identifiers were changed in the modules and the catalogs.
     No model or attribute changes were made at this time. 
 
 11. JATS became version "1.1" and "v1.1 20151215"
     BITS remained version "2.0" but becomes "v2.0 20151225"

     =============================================================
     JATS Version 1.1d3             (DAL/BTU) v1.1d3  (2015-03-01)
   
     The changes in this release are in response to NISO 
     License and Indicators Recommended Practice.

     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the NISO membership to be changed. This
     Committee Draft 1.1d3 will be sent to the NISO voting
     membership, to become (if approved) NISO JATS 1.1.
     
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee.

 10. JATS became version "1.1d3" and "v1.1 20150301//EN"
  
     =============================================================
     JATS Version 1.1d2              (DAL/BTU) v1.1d2 (2014-09-30)
   
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the NISO membership to be changed. This
     Committee Draft 1.1d2 will be sent to the NISO voting
     membership, to become (if approved) NISO JATS 1.1.
     
     This catalog represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee.

   9. JATS became version "1.1d2" and "v1.1d2 20140930//EN"
 
     =============================================================
     NISO JATS Version 1.1d1           (DAL/BTU) v1.1 (2013-11-15)
   
     ANSI/NISO Z39.96-2012 (pre-release for V1.0 BITS; Version 1.1d1) 

     NISO JATS Standing Committee met and answered the requests and
     suggestions from the NISO request forms.

     Details concerning ANSI/NISO Z39.96-2012 JATS-based DTDs,  
     XSDs, RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.3d2/

  8. GLOBAL ATTRIBUTES - Added the new parameter entity 
         %jats-common-atts;
     to every element in this module. This PE adds (for now) the
     @id attribute and the @xml:base attribute to every element,
     whether metadata or narrative.
     Since the @id in this parameter entity is optional, a second
     parameter entity jats-common-atts-id-required was also added.
     The two are kept in sync with the jats-base-atts parameter 
     entity.
 
     =============================================================
     NISO JATS Version 1.0             (DAL/BTU) v1.0 (2012-xx-xx)
   
     ANSI/NISO Z39.96-2012 (Version 1.0) 

     Details concerning ANSI/NISO Z39.96-2012 JATS-based DTDs,  
     XSDs, RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.3d2/
 
  7. Updated the DTD-version attribute to "1.0" and the formal
     public identifier to the date: "v1.0 20120330//EN".

     =============================================================
     Version 0.4                       (DAL/BTU) v0.4 (2011-01-31)
   
     This Tag Set is in the process of becoming a NISO standard.
     The version numbers are starting over from 0.4", as a Trial
     Use Draft, to be made into "Version 1.0" when the Tag Suite 
     becomes a NISO standard. Thus, the version number that would
     have been "NLM Version 3.1 Draft" has become NISO JATS
     "Version 0.4".

     Details on NISO Trial Use Draft Version 0.4 are available at
           http://jats.nlm.nih.gov/JATS-0.4.
   
  6. Updated the DTD-version attribute to "0.4" 
   
  5. Updated the public identifier to "v0.4 20110131", 
     modified the formal public identifier to include "JATS (Z39.96)",
     and the filename as delivered to include "JATS" and the
     new version number "0".

  4. @SPECIFIC-USE and @XML:LANG - Added the @specific-use and
     @xml:lang to the following elements:
       - private-char through private-char-atts (@specific-use)

  3. PEs FOR CONTENT MODELS - Added parameter entity redefinitions
     for the model of the <private-char> element.

  2. PEs FOR ATTLISTS - Added parameter entity redefinitions for the
     attribute lists of the following elements: (no attributes or
     values were changed)
       - glyph-ref         glyph-ref-atts
       - glyph-data        glyph-data-atts
       - private-char      private-char-atts

     =============================================================
     Version 3.0                       (DAL/BTU) v3.0 (2007-10-31)

     Version 3.0 is the first non-backward-compatible release.
     In addition to the usual incremental changes, some
     elements and attributes have been renamed and/or remodeled
     to better meet user needs and to increase tag set consistency.
     All module change histories are available through the Tag Suite
     web site at http://dtd.nlm.nih.gov.

     Details on version 3.0 are available at
           http://dtd.nlm.nih.gov/3.0.

  1. Updated public identifier to "v3.0 20071031//EN"              -->

<!-- ============================================================= -->
<!-- Details concerning 
     ANSI/NISO Z39.96-2015 JATS-based DTDs, XSDs, 
     RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.1/                            -->
<!-- ============================================================= -->
<!--                    DESIGN COMMENT                             -->
<!-- ============================================================= -->
<!--                    This DTD Suite has been designed with Unicode
                        as the basic representation of all special
                        characters. The use of combining characters
                        is supported and encouraged as is the use
                        of entities defined by the STIX project
                        (http://www.ams.org/STIX/). Unicode values
                        in planes other than Plane 0 may be freely
                        used.

                        Use of private publisher entities and Unicode
                        Private Use Area is discouraged, but supported
                        with the <private-char> element, for which a
                        corresponding bitmap must be submitted.

                        In cases where an entity name has been generally
                        accepted with a corresponding Unicode number
                        and the entity has not been added to
                        the ISO standard entity sets, a named entity
                        may be defined below (e.g. &euro;).

                        Because of the potential for conflicts in
                        assignments by different publishers,
                        the JATS DTD Suite does not support assignment 
                        of values in the Unicode Private Use Area.
                        Publishers who have defined characters in the
                        Private Use Area must remap those characters
                        to existing Unicode values (using combining
                        characters for special accented characters
                        where appropriate), or must submit bitmaps of
                        those characters using one of the two methods
                        supported under the <private-char> element.

                        Those custom publisher entities for which
                        corresponding Unicode values have not been
                        determined must be tagged with the
                        <private-char> element. Publishers must submit
                        bitmaps of those characters using one of the
                        two methods supported in the <private-char>
                        element.                                   -->

<!-- ============================================================= -->
<!--                    COMMONLY ACCEPTED ENTITIES FOR UNICODE
                        GLYPHS                                     -->
<!-- ============================================================= -->

<!--                    For each of the following entities a name
                        and a Unicode numerical character reference
                        is given. Where a unique Unicode character
                        could be determined, that character was used.
                        For some of the symbols combining characters
                        have been used. Do not use this space to
                        redefine characters already found in standard
                        ISO entity sets. Do not use this space to
                        define any character that cannot be
                        represented with Unicode.                  -->

<!--                    LATIN SMALL LETTER G WITH CARON            -->
<!ENTITY  gcaron        "&#x01E7;"                                   >


<!--                    LATIN CAPITAL LETTER H WITH MACRON         -->
<!ENTITY  Hmacr         "&#x0048;&#x0304;"                           >


<!--                    EURO CURRENCY                              -->
<!ENTITY  euro          "&#x20AC;"                                   >


<!--                    FRANC CURRENCY                             -->
<!ENTITY  franc         "&#x20A3;"                                   >


<!-- ============================================================= -->
<!--                    ATTRIBUTE LISTS FOR PRIVATE CHARACTERS     -->
<!-- ============================================================= -->


<!--                    GLYPH DATA ATTRIBUTES                      -->
<!--                    Attribute list for the <glyph-data>
                        element                                    -->
<!ENTITY % glyph-data-atts
            "%jats-common-atts;                                       
             fontchar   CDATA                              #IMPLIED
             fontname   CDATA                              #IMPLIED
             format     NMTOKEN                            #IMPLIED
             resolution CDATA                              #IMPLIED
             xml:space  (preserve)                 #FIXED 'preserve'
             x-size     CDATA                              #IMPLIED
             y-size     CDATA                              #IMPLIED" >


<!--                    GLYPH REFERENCE ATTRIBUTES                 -->
<!--                    Attribute list for the <glyph-ref>
                        element                                    -->
<!ENTITY % glyph-ref-atts
            "%jats-common-atts;                                       
             glyph-data IDREF                             #IMPLIED"  >


<!--                    PRIVATE USE AREA AND CUSTOM CHARACTERS
                        ATTRIBUTES                                 -->
<!--                    Attribute list for the <private-char>
                        element                                    -->
<!ENTITY % private-char-atts
            "%jats-common-atts;                                       
             description
                        CDATA                             #IMPLIED
             name       CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED"  >


<!-- ============================================================= -->
<!--                    PRIVATE USE AREA AND CUSTOM CHARACTERS     -->
<!-- ============================================================= -->
<!--
                        Special characters defined by publishers as
                        custom entities or in the Unicode Private Use
                        Area may not be deposited as is. If they
                        cannot be remapped to existing Unicode values,
                        they must be submitted as a bitmap using
                        the <private-char> element. The most
                        repository-friendly technique is <glyph-data>
                        although individual bitmap files may be
                        submitted with inline-graphic.

                        We would like to thank Beacon Publishing and
                        the APS (American Physical Society) for
                        providing us with this technique.          -->


<!--                    PRIVATE CHARACTER MODEL                    -->
<!--                    The content model for the <private-char>
                        element                                    -->
<!ENTITY % private-char-model
                        "((glyph-data | glyph-ref) | inline-graphic*)"
                                                                     >


<!--                    PRIVATE CHARACTER (CUSTOM OR UNICODE)      -->
<!--                    A custom character entity defined by a
                        publisher or a custom character from the
                        Unicode private-use area for which a bitmap
                        is submitted for the glyph.

                        Since there are no completely standard/public
                        agreements on how such characters are to be
                        named and displayed, this technique is to be
                        used instead of a custom general entity
                        reference, to provide complete information
                        on the intended character.
                        A document should contain a <private-char>
                        element at each location where a private
                        character is used within the document. The
                        corresponding image for the glyph may be
                        given in the <glyph-data> element or as an
                        external bitmap file referenced by an
                        <inline-graphic> element.

                        Implementation Note: <inline-graphic> should
                        only be used outside <private-char> when the
                        graphic is something other than a special
                        character.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=private-char
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=private-char
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=private-char
                                                                   -->
<!ELEMENT  private-char %private-char-model;                         >
<!--         description
                        A human-readable description of the
                        character, for example, "Arrow, normal
                        weight, single line, two-headed, Northwest
                        to Southeast".
             name       Unique name for the character in all
                        uppercase ASCII, similar to names found
                        in Unicode standard (e.g., "NORTHWEST
                        SOUTHEAST ARROW"                           -->
<!ATTLIST  private-char
             %private-char-atts;                                     >


<!--                    GLYPH DATA FOR A PRIVATE CHARACTER         -->
<!--                    This element is used when there is known to
                        be no font available to render the private
                        character. The <glyph-data> element can be
                        used to provide information on the actual
                        glyph that is associated with the private-use
                        character. The element includes an inline
                        bitmap of the glyph encoded in plain
                        PBM (Plain Bit Map) format so that it is
                        human-readable.

                        For example:
                        <private-char name="NORTHWEST SOUTHEAST ARROW"
                        description="Arrow, normal weight, single
                        line, two-headed, Northwest to Southeast">
                        <glyph-data format="PBM" resolution="300"
                        x-size="34" y-size="34">
                        0000000000000000000000000000000000
                        0111111111111100000000000000000000
                        0111111111111100000000000000000000
                        0111110000000000000000000000000000
                        0111110000000000000000000000000000
                        0111111000000000000000000000000000
                        0110111100000000000000000000000000
                        0110011110000000000000000000000000
                        0110001111000000000000000000000000
                        0110000111100000000000000000000000
                        0110000011110000000000000000000000
                        0110000001111000000000000000000000
                        0110000000111100000000000000000000
                        0110000000011110000000000000000000
                        0110000000001111000000000000000000
                        0110000000000111100000000000000000
                        0110000000000011110000000000000000
                        0000000000000001111000000000000000
                        0000000000000000111100000000000110
                        0000000000000000011110000000000110
                        0000000000000000001111000000000110
                        0000000000000000000111100000000110
                        0000000000000000000011110000000110
                        0000000000000000000001111000000110
                        0000000000000000000000111100000110
                        0000000000000000000000011110000110
                        0000000000000000000000001111000110
                        0000000000000000000000000111100110
                        0000000000000000000000000011110110
                        0000000000000000000000000001111110
                        0000000000000000000000000001111110
                        0000000000000000011111111111111110
                        0000000000000000011111111111111110
                        0000000000000000000000000000000000
                        </glyph-data></private-char>
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=glyph-data
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=glyph-data
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=glyph-data
                                                                   -->
<!ELEMENT  glyph-data   (#PCDATA)                                    >
<!--         id         Identifier so that the full glyph data need
                        not be repeated every time the character is
                        used. The <glyph-ref> element can be used
                        to point to this ID, to reuse a character
                        in subsequent text.
             fontchar   The offset of the character into a glyph
                        table, such as a Unicode character.
             fontname   The name of the character
             format     Names the image format of the bitmap. Should
                        be "PBM" if the plain bitmap is included
                        inline.
             resolution Resolution of the bitmap in dots per inch,
                        expressed as a decimal integer (e.g. 72, 300)
             xml:space  Preserve whitespace within this element.
             x-size     Number of pixels per row in the bit-mapped
                        glyph
             y-size     Number of rows of the bit-mapped glyph     -->
<!ATTLIST  glyph-data
             %glyph-data-atts;                                       >


<!--                    GLYPH REFERENCE FOR A PRIVATE CHARACTER    -->
<!--                    Once a private character has been declared
                        using a <glyph-data> element, the character
                        can be reused by using this element to
                        point to the full <glyph-data> element.
                        The pointing uses the ID/IDREF mechanism,
                        using the "glyph-data" attribute of this
                        element to point to the "id" attribute of
                        another <glyph-data> element.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=glyph-ref
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=glyph-ref
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=glyph-ref
                                                                   -->
<!ELEMENT  glyph-ref    EMPTY                                        >
<!--         glyph-data An IDREF-type attribute that points to the
                        "id" attribute of a <glyph-data> character.
                        The idea is to use the full glyph data once,
                        then point to an existing character instead
                        of repeating the entire glyph data again.  -->
<!ATTLIST  glyph-ref
             %glyph-ref-atts;                                        >

<!-- ================== End Custom XML Special Characters ======== -->

<!-- ============================================
     ::DATATOOL:: Generated from "docsum.asn"
     ::DATATOOL:: by application DATATOOL version 1.9.0
     ::DATATOOL:: on 04/10/2008 16:04:22
     ============================================ -->

<!-- ============================================ -->
<!-- This section is mapped from module "NSE"
================================================= -->


<!-- Elements used by other modules:
          ExchangeSet,
          Rs,
          Ss,
          MapLoc,
          FxnSet,
          Assembly,
          Assay,
          BaseURL,
          PrimarySequence,
          RsStruct,
          RsLinkout,
          Component -->
<!-- ============================================ -->

<!-- Set of dbSNP refSNP docsums -->
<!ELEMENT ExchangeSet (
        ExchangeSet_setType?, 
        ExchangeSet_setDepth?, 
        ExchangeSet_specVersion?, 
        ExchangeSet_dbSnpBuild?, 
        ExchangeSet_generated?, 
        ExchangeSet_sourceDatabase, 
        ExchangeSet_rs?, 
        ExchangeSet_assay?, 
        ExchangeSet_query?, 
        ExchangeSet_summary, 
        ExchangeSet_baseURL)>

<!-- set-type: full dump; from query; single refSNP -->
<!ELEMENT ExchangeSet_setType (#PCDATA)>

<!-- content depth: brief XML (only refSNP properties and summary subSNP element content); full XML (full refSNP, full subSNP content; all flanking sequences) -->
<!ELEMENT ExchangeSet_setDepth (#PCDATA)>

<!-- version number of docsum.asn/docsum.dtd specification -->
<!ELEMENT ExchangeSet_specVersion (#PCDATA)>

<!-- build number of database for this export -->
<!ELEMENT ExchangeSet_dbSnpBuild (%INTEGER;)>

<!-- Generated date -->
<!ELEMENT ExchangeSet_generated (#PCDATA)>

<!ELEMENT ExchangeSet_sourceDatabase (
        ExchangeSet_sourceDatabase_taxId, 
        ExchangeSet_sourceDatabase_organism, 
        ExchangeSet_sourceDatabase_dbSnpOrgAbbr?, 
        ExchangeSet_sourceDatabase_gpipeOrgAbbr?)>

<!-- NCBI taxonomy ID for variation -->
<!ELEMENT ExchangeSet_sourceDatabase_taxId (%INTEGER;)>

<!-- common name for species used as part of database name. -->
<!ELEMENT ExchangeSet_sourceDatabase_organism (#PCDATA)>

<!-- organism abbreviation used in dbSNP. -->
<!ELEMENT ExchangeSet_sourceDatabase_dbSnpOrgAbbr (#PCDATA)>

<!-- organism abbreviation used within NCBI genome pipeline data dumps. -->
<!ELEMENT ExchangeSet_sourceDatabase_gpipeOrgAbbr (#PCDATA)>

<!ELEMENT ExchangeSet_rs (Rs*)>

<!ELEMENT ExchangeSet_assay (Assay)>

<!ELEMENT ExchangeSet_query (
        ExchangeSet_query_date?, 
        ExchangeSet_query_string?)>

<!-- yyyy-mm-dd -->
<!ELEMENT ExchangeSet_query_date (#PCDATA)>

<!-- Query terms or search constraints -->
<!ELEMENT ExchangeSet_query_string (#PCDATA)>

<!ELEMENT ExchangeSet_summary (
        ExchangeSet_summary_numRsIds?, 
        ExchangeSet_summary_totalSeqLength?, 
        ExchangeSet_summary_numContigHits?, 
        ExchangeSet_summary_numGeneHits?, 
        ExchangeSet_summary_numGiHits?, 
        ExchangeSet_summary_num3dStructs?, 
        ExchangeSet_summary_numAlleleFreqs?, 
        ExchangeSet_summary_numStsHits?, 
        ExchangeSet_summary_numUnigeneCids?)>

<!-- Total number of refsnp-ids in this exchange set -->
<!ELEMENT ExchangeSet_summary_numRsIds (%INTEGER;)>

<!-- Total length of exemplar flanking sequences -->
<!ELEMENT ExchangeSet_summary_totalSeqLength (%INTEGER;)>

<!-- Total number of contig locations from SNPContigLoc -->
<!ELEMENT ExchangeSet_summary_numContigHits (%INTEGER;)>

<!-- Total number of locus ids from SNPContigLocusId -->
<!ELEMENT ExchangeSet_summary_numGeneHits (%INTEGER;)>

<!-- Total number of gi hits from MapLink -->
<!ELEMENT ExchangeSet_summary_numGiHits (%INTEGER;)>

<!-- Total number of 3D structures from SNP3D -->
<!ELEMENT ExchangeSet_summary_num3dStructs (%INTEGER;)>

<!-- Total number of allele frequences from SubPopAllele -->
<!ELEMENT ExchangeSet_summary_numAlleleFreqs (%INTEGER;)>

<!-- Total number of STS hits from SnpInSts -->
<!ELEMENT ExchangeSet_summary_numStsHits (%INTEGER;)>

<!-- Total number of unigene cluster ids from UnigeneSnp -->
<!ELEMENT ExchangeSet_summary_numUnigeneCids (%INTEGER;)>

<!ELEMENT ExchangeSet_baseURL (BaseURL*)>

<!-- defines the docsum structure for refSNP clusters, where a refSNP cluster (rs) is a grouping of individual dbSNP submissions that all refer to the same variation. The refsnp provides a single unified record for annotation of NCBI resources such as reference genome sequence. -->
<!ELEMENT Rs (
        Rs_rsId, 
        Rs_snpClass, 
        Rs_snpType, 
        Rs_molType, 
        Rs_validProbMin?, 
        Rs_validProbMax?, 
        Rs_genotype?, 
        Rs_het?, 
        Rs_validation, 
        Rs_create, 
        Rs_update?, 
        Rs_sequence, 
        Rs_ss, 
        Rs_assembly?, 
        Rs_primarySequence?, 
        Rs_rsStruct?, 
        Rs_rsLinkout?, 
        Rs_mergeHistory?)>

<!-- refSNP (rs) number -->
<!ELEMENT Rs_rsId (%INTEGER;)>

<!ELEMENT Rs_snpClass %ENUM;>
<!ATTLIST Rs_snpClass value (
        snp |
        in-del |
        heterozygous |
        microsatellite |
        named-locus |
        no-variation |
        mixed |
        multinucleotide-polymorphism
        ) #REQUIRED >


<!ELEMENT Rs_snpType %ENUM;>
<!ATTLIST Rs_snpType value (
        notwithdrawn |
        artifact |
        gene-duplication |
        duplicate-submission |
        notspecified |
        ambiguous-location |
        low-map-quality
        ) #REQUIRED >


<!ELEMENT Rs_molType %ENUM;>
<!ATTLIST Rs_molType value (
        genomic |
        cDNA |
        mito |
        chloro |
        unknown
        ) #REQUIRED >


<!-- minimum reported success rate of all submissions in cluster -->
<!ELEMENT Rs_validProbMin (%INTEGER;)>

<!-- maximum reported success rate of all submissions in cluster -->
<!ELEMENT Rs_validProbMax (%INTEGER;)>

<!-- at least one genotype reported for this refSNP -->
<!ELEMENT Rs_genotype EMPTY>
<!ATTLIST Rs_genotype value ( true | false ) #REQUIRED >


<!ELEMENT Rs_het (
        Rs_het_type, 
        Rs_het_value, 
        Rs_het_stdError?)>

<!-- Est=Estimated average het from allele frequencies, Obs=Observed from genotype data -->
<!ELEMENT Rs_het_type %ENUM;>
<!ATTLIST Rs_het_type value (
        est |
        obs
        ) #REQUIRED >


<!-- Heterozygosity -->
<!ELEMENT Rs_het_value (%REAL;)>

<!-- Standard error of Het estimate -->
<!ELEMENT Rs_het_stdError (%REAL;)>

<!ELEMENT Rs_validation (
        Rs_validation_byCluster?, 
        Rs_validation_byFrequency?, 
        Rs_validation_byOtherPop?, 
        Rs_validation_by2Hit2Allele?, 
        Rs_validation_byHapMap?, 
        Rs_validation_otherPopBatchId?, 
        Rs_validation_twoHit2AlleleBatchId?)>

<!-- at least one subsnp in cluster has frequency data submitted -->
<!ELEMENT Rs_validation_byCluster EMPTY>
<!ATTLIST Rs_validation_byCluster value ( true | false ) #REQUIRED >


<!-- cluster has 2+ submissions, with 1+ submissions assayed with a non-computational method -->
<!ELEMENT Rs_validation_byFrequency EMPTY>
<!ATTLIST Rs_validation_byFrequency value ( true | false ) #REQUIRED >


<!ELEMENT Rs_validation_byOtherPop EMPTY>
<!ATTLIST Rs_validation_byOtherPop value ( true | false ) #REQUIRED >


<!-- cluster has 2+ submissions, with 1+ submissions assayed with a non-computational method -->
<!ELEMENT Rs_validation_by2Hit2Allele EMPTY>
<!ATTLIST Rs_validation_by2Hit2Allele value ( true | false ) #REQUIRED >


<!-- TBD -->
<!ELEMENT Rs_validation_byHapMap EMPTY>
<!ATTLIST Rs_validation_byHapMap value ( true | false ) #REQUIRED >


<!-- dbSNP batch-id's for other pop snp validation data. -->
<!ELEMENT Rs_validation_otherPopBatchId (Rs_validation_otherPopBatchId_E*)>


<!ELEMENT Rs_validation_otherPopBatchId_E (%INTEGER;)>

<!-- dbSNP batch-id's for double-hit snp validation data. Use batch-id to get methods, etc. -->
<!ELEMENT Rs_validation_twoHit2AlleleBatchId (Rs_validation_twoHit2AlleleBatchId_E*)>


<!ELEMENT Rs_validation_twoHit2AlleleBatchId_E (%INTEGER;)>

<!-- date the refsnp cluster was instantiated -->
<!ELEMENT Rs_create (
        Rs_create_build?, 
        Rs_create_date?)>

<!-- build number when the cluster was created -->
<!ELEMENT Rs_create_build (%INTEGER;)>

<!-- yyyy-mm-dd -->
<!ELEMENT Rs_create_date (#PCDATA)>

<!-- most recent date the cluster was updated (member added or deleted) -->
<!ELEMENT Rs_update (
        Rs_update_build?, 
        Rs_update_date?)>

<!-- build number when the cluster was updated -->
<!ELEMENT Rs_update_build (%INTEGER;)>

<!-- yyyy-mm-dd -->
<!ELEMENT Rs_update_date (#PCDATA)>

<!ELEMENT Rs_sequence (
        Rs_sequence_exemplarSs, 
        Rs_sequence_seq5?, 
        Rs_sequence_observed, 
        Rs_sequence_seq3?)>

<!-- dbSNP ss# selected as source of refSNP flanking sequence, ss# part of ss-list below -->
<!ELEMENT Rs_sequence_exemplarSs (%INTEGER;)>

<!-- 5' sequence that flanks the variation -->
<!ELEMENT Rs_sequence_seq5 (#PCDATA)>

<!-- list of all nucleotide alleles observed in ss-list members, correcting for reverse complementation of memebers reported in reverse orientation -->
<!ELEMENT Rs_sequence_observed (#PCDATA)>

<!-- 3' sequence that flanks the variation -->
<!ELEMENT Rs_sequence_seq3 (#PCDATA)>

<!ELEMENT Rs_ss (Ss*)>

<!ELEMENT Rs_assembly (Assembly*)>

<!ELEMENT Rs_primarySequence (PrimarySequence*)>

<!ELEMENT Rs_rsStruct (RsStruct*)>

<!ELEMENT Rs_rsLinkout (RsLinkout*)>

<!ELEMENT Rs_mergeHistory (Rs_mergeHistory_E*)>


<!ELEMENT Rs_mergeHistory_E (
        Rs_mergeHistory_E_rsId, 
        Rs_mergeHistory_E_buildId?, 
        Rs_mergeHistory_E_orientFlip?)>

<!-- previously issued rs id whose member assays have now been merged -->
<!ELEMENT Rs_mergeHistory_E_rsId (%INTEGER;)>

<!-- build id when rs id was merged into parent rs -->
<!ELEMENT Rs_mergeHistory_E_buildId (%INTEGER;)>

<!-- TRUE if strand of rs id is reverse to parent object's current strand -->
<!ELEMENT Rs_mergeHistory_E_orientFlip EMPTY>
<!ATTLIST Rs_mergeHistory_E_orientFlip value ( true | false ) #REQUIRED >


<!-- data for an individual submission to dbSNP -->
<!ELEMENT Ss (
        Ss_ssId, 
        Ss_handle, 
        Ss_batchId, 
        Ss_locSnpId?, 
        Ss_subSnpClass?, 
        Ss_orient?, 
        Ss_strand?, 
        Ss_molType?, 
        Ss_buildId?, 
        Ss_methodClass?, 
        Ss_validated?, 
        Ss_linkoutUrl?, 
        Ss_sequence)>

<!-- dbSNP accession number for submission -->
<!ELEMENT Ss_ssId (%INTEGER;)>

<!-- Tag for the submitting laboratory -->
<!ELEMENT Ss_handle (#PCDATA)>

<!-- dbSNP number for batch submission -->
<!ELEMENT Ss_batchId (%INTEGER;)>

<!-- submission (ss#) submitter ID -->
<!ELEMENT Ss_locSnpId (#PCDATA)>

<!-- SubSNP classification by type of variation -->
<!ELEMENT Ss_subSnpClass %ENUM;>
<!ATTLIST Ss_subSnpClass value (
        snp |
        in-del |
        heterozygous |
        microsatellite |
        named-locus |
        no-variation |
        mixed |
        multinucleotide-polymorphism
        ) #REQUIRED >


<!-- orientation of refsnp cluster members to refsnp cluster sequence -->
<!ELEMENT Ss_orient %ENUM;>

<!--
    forward	-  ss flanking sequence is in same orientation as seq-ss-exemplar
    reverse	-  lanking sequence and alleles are reverse complement of refSNP as defined by ss exemplar
-->
<!ATTLIST Ss_orient value (
        forward |
        reverse
        ) #REQUIRED >


<!-- strand is defined as TOP/BOTTOM by nature of flanking nucleotide sequence -->
<!ELEMENT Ss_strand %ENUM;>
<!ATTLIST Ss_strand value (
        top |
        bottom
        ) #REQUIRED >


<!-- moltype from Batch table -->
<!ELEMENT Ss_molType %ENUM;>
<!ATTLIST Ss_molType value (
        genomic |
        cDNA |
        mito |
        chloro |
        unknown
        ) #REQUIRED >


<!-- dbSNP build number when ss# was added to a refSNP (rs#) cluster -->
<!ELEMENT Ss_buildId (%INTEGER;)>

<!-- class of method used to assay for the variation -->
<!ELEMENT Ss_methodClass %ENUM;>

<!--
    dHPLC	-  Denaturing High Pressure Liquid Chromatography used to detect SNP
    hybridize	-  a hybridization method (e.g. chip) was used to assay for variation
    computed	-  variation was mined from sequence alignment with software
    sequence	-  samples were sequenced and resulting alignment used to define variation
-->
<!ATTLIST Ss_methodClass value (
        dHPLC |
        hybridize |
        computed |
        sSCP |
        other |
        unknown |
        rFLP |
        sequence
        ) #REQUIRED >


<!ELEMENT Ss_validated %ENUM;>

<!--
    by-submitter	-  subsnp has been experimentally validated by submitter
    by-frequency	-  subsnp has frequency data submitted
    by-cluster	-  has 2+ submissions, with 1+ submission assayed with a non-computational method
-->
<!ATTLIST Ss_validated value (
        by-submitter |
        by-frequency |
        by-cluster
        ) #REQUIRED >


<!-- append loc-snp-id to this base URL to construct a pointer to submitter data. -->
<!ELEMENT Ss_linkoutUrl (#PCDATA)>

<!ELEMENT Ss_sequence (
        Ss_sequence_seq5?, 
        Ss_sequence_observed, 
        Ss_sequence_seq3?)>

<!-- 5' sequence that flanks the variation -->
<!ELEMENT Ss_sequence_seq5 (#PCDATA)>

<!-- list of all nucleotide alleles observed in ss-list members, correcting for reverse complementation of memebers reported in reverse orientation -->
<!ELEMENT Ss_sequence_observed (#PCDATA)>

<!-- 3' sequence that flanks the variation -->
<!ELEMENT Ss_sequence_seq3 (#PCDATA)>

<!-- Position of a single hit of a variation on a contig -->
<!ELEMENT MapLoc (
        MapLoc_asnFrom, 
        MapLoc_asnTo, 
        MapLoc_locType, 
        MapLoc_alnQuality?, 
        MapLoc_orient?, 
        MapLoc_physMapInt?, 
        MapLoc_leftFlankNeighborPos?, 
        MapLoc_rightFlankNeighborPos?, 
        MapLoc_leftContigNeighborPos?, 
        MapLoc_rightContigNeighborPos?, 
        MapLoc_numberOfMismatches?, 
        MapLoc_numberOfDeletions?, 
        MapLoc_numberOfInsertions?, 
        MapLoc_fxnSet?)>

<!-- beginning of variation as feature on contig -->
<!ELEMENT MapLoc_asnFrom (%INTEGER;)>

<!-- end position of variation as feature on contig -->
<!ELEMENT MapLoc_asnTo (%INTEGER;)>

<!-- defines the seq-loc symbol if asn_from != asn_to -->
<!ELEMENT MapLoc_locType %ENUM;>

<!--
    insertion	-  insertion on contig
    exact	-  asn-from = asn-to write as 'asn-from'
    deletion	-  deletion on contig
-->
<!ATTLIST MapLoc_locType value (
        insertion |
        exact |
        deletion |
        range-ins |
        range-exact |
        range-del
        ) #REQUIRED >


<!-- alignment qualiity -->
<!ELEMENT MapLoc_alnQuality (%REAL;)>

<!-- orientation of refSNP sequence to contig sequence -->
<!ELEMENT MapLoc_orient %ENUM;>
<!ATTLIST MapLoc_orient value (
        forward |
        reverse
        ) #REQUIRED >


<!-- chromosome position as integer for sorting -->
<!ELEMENT MapLoc_physMapInt (%INTEGER;)>

<!-- nearest aligned position in 5' flanking sequence of snp -->
<!ELEMENT MapLoc_leftFlankNeighborPos (%INTEGER;)>

<!-- nearest aligned position in 3' flanking sequence of snp -->
<!ELEMENT MapLoc_rightFlankNeighborPos (%INTEGER;)>

<!-- nearest aligned position in 5' contig alignment of snp -->
<!ELEMENT MapLoc_leftContigNeighborPos (%INTEGER;)>

<!-- nearest aligned position in 3' contig alignment of snp -->
<!ELEMENT MapLoc_rightContigNeighborPos (%INTEGER;)>

<!-- number of Mismatched positions in this alignment -->
<!ELEMENT MapLoc_numberOfMismatches (%INTEGER;)>

<!-- number of deletions in this alignment -->
<!ELEMENT MapLoc_numberOfDeletions (%INTEGER;)>

<!-- number of insetions in this alignment -->
<!ELEMENT MapLoc_numberOfInsertions (%INTEGER;)>

<!ELEMENT MapLoc_fxnSet (FxnSet*)>

<!-- functional relationship of SNP (and possibly alleles) to genes at contig location as defined in organism-specific bxxx_SNPContigLocusId_xxx tables. -->
<!ELEMENT FxnSet (
        FxnSet_geneId?, 
        FxnSet_symbol?, 
        FxnSet_mrnaAcc?, 
        FxnSet_mrnaVer?, 
        FxnSet_protAcc?, 
        FxnSet_protVer?, 
        FxnSet_fxnClass?, 
        FxnSet_readingFrame?, 
        FxnSet_allele?, 
        FxnSet_residue?, 
        FxnSet_aaPosition?)>

<!-- gene-id of gene as aligned to contig -->
<!ELEMENT FxnSet_geneId (%INTEGER;)>

<!-- symbol (official if present in Entrez Gene) of gene -->
<!ELEMENT FxnSet_symbol (#PCDATA)>

<!-- mRNA accession if variation in transcript -->
<!ELEMENT FxnSet_mrnaAcc (#PCDATA)>

<!-- mRNA sequence version if variation is in transcripot -->
<!ELEMENT FxnSet_mrnaVer (%INTEGER;)>

<!-- protein accession if variation in protein -->
<!ELEMENT FxnSet_protAcc (#PCDATA)>

<!-- protein version if variation is in protein -->
<!ELEMENT FxnSet_protVer (%INTEGER;)>

<!ELEMENT FxnSet_fxnClass %ENUM;>

<!--
    locus-region	-  variation in region of gene, but not in transcript
-->
<!ATTLIST FxnSet_fxnClass value (
        locus-region |
        coding-unknown |
        coding-synonymous |
        coding-nonsynonymous |
        mrna-utr |
        intron |
        splice-site |
        reference |
        coding-exception
        ) #REQUIRED >


<!ELEMENT FxnSet_readingFrame (%INTEGER;)>

<!-- variation allele: * suffix indicates allele of contig at this location -->
<!ELEMENT FxnSet_allele (#PCDATA)>

<!-- translated amino acid residue for allele -->
<!ELEMENT FxnSet_residue (#PCDATA)>

<!-- position of the variant residue in peptide sequence -->
<!ELEMENT FxnSet_aaPosition (%INTEGER;)>

<!-- A collection of genome sequence records (curated gene regions (NG's), contigs (NWNT's)  and chromosomes (NC/AC's) produced by a genome sequence project. Structure is populated from ContigInfo tables. -->
<!ELEMENT Assembly (
        Assembly_dbSnpBuild, 
        Assembly_genomeBuild, 
        Assembly_groupLabel?, 
        Assembly_assemblySource?, 
        Assembly_current?, 
        Assembly_reference?, 
        Assembly_component?, 
        Assembly_snpStat)>

<!-- dbSNP build number defining the rsid set aligned to this assembly -->
<!ELEMENT Assembly_dbSnpBuild (%INTEGER;)>

<!-- assembly build number with possible 'subbuild' version numbers to reflect updates in gene annotation (human e.g. 34_3, 35_1, 36_1) -->
<!ELEMENT Assembly_genomeBuild (#PCDATA)>

<!-- High-level classification of the assembly to distinguish reference projects from alternate solutions. GroupLabel field from organism/build-specific ContigInfo tables. "reference" is occasionally used as the preferred assembly; standards will converge as additional organism genome projects are finished. Note that some organism assembly names include extended characters like '~' and '/' that may be incompatible with OS filename conventions. -->
<!ELEMENT Assembly_groupLabel (#PCDATA)>

<!-- Name of the group(s) or organization(s) that generated the assembly -->
<!ELEMENT Assembly_assemblySource (#PCDATA)>

<!-- Marks the current genomic assembly -->
<!ELEMENT Assembly_current EMPTY>
<!ATTLIST Assembly_current value ( true | false ) #REQUIRED >


<!ELEMENT Assembly_reference EMPTY>
<!ATTLIST Assembly_reference value ( true | false ) #REQUIRED >


<!ELEMENT Assembly_component (Component*)>

<!ELEMENT Assembly_snpStat (
        Assembly_snpStat_mapWeight, 
        Assembly_snpStat_chromCount?, 
        Assembly_snpStat_placedContigCount?, 
        Assembly_snpStat_unplacedContigCount?, 
        Assembly_snpStat_seqlocCount?, 
        Assembly_snpStat_hapCount?)>

<!-- summary measure of placement precision in the assembly -->
<!ELEMENT Assembly_snpStat_mapWeight %ENUM;>
<!ATTLIST Assembly_snpStat_mapWeight value (
        unmapped |
        unique-in-contig |
        two-hits-in-contig |
        less-10-hits |
        multiple-hits
        ) #REQUIRED >


<!-- number of distinct chromosomes in the mapset -->
<!ELEMENT Assembly_snpStat_chromCount (%INTEGER;)>

<!-- number of distinct contigs [ gi | accession[.version] ] in the mapset -->
<!ELEMENT Assembly_snpStat_placedContigCount (%INTEGER;)>

<!-- number of sequence postions to a contig with unknown chromosomal assignment -->
<!ELEMENT Assembly_snpStat_unplacedContigCount (%INTEGER;)>

<!-- total number of sequence positions in the mapset -->
<!ELEMENT Assembly_snpStat_seqlocCount (%INTEGER;)>

<!-- Number of hits to alternative genomic haplotypes (e.g. HLA DR region, KIR, or pseudo-autosomal regions like PAR) within the assembly mapset. Note that positions on haplotypes defined in other assemblies (a different assembly_group_label value) will not be counted in this value. -->
<!ELEMENT Assembly_snpStat_hapCount (%INTEGER;)>


<!ELEMENT Assay (
        Assay_handle?, 
        Assay_batch?, 
        Assay_batchId?, 
        Assay_batchType?, 
        Assay_molType?, 
        Assay_sampleSize?, 
        Assay_population?, 
        Assay_linkoutUrl?, 
        Assay_method?, 
        Assay_taxonomy, 
        Assay_strains?, 
        Assay_comment?, 
        Assay_citation?)>

<!ELEMENT Assay_handle (#PCDATA)>

<!ELEMENT Assay_batch (#PCDATA)>

<!ELEMENT Assay_batchId (%INTEGER;)>

<!ELEMENT Assay_batchType %ENUM;>
<!ATTLIST Assay_batchType value (
        snpassay |
        validation |
        doublehit
        ) #REQUIRED >


<!ELEMENT Assay_molType %ENUM;>
<!ATTLIST Assay_molType value (
        genomic |
        cDNA |
        mito |
        chloro
        ) #REQUIRED >


<!ELEMENT Assay_sampleSize (%INTEGER;)>

<!ELEMENT Assay_population (#PCDATA)>

<!ELEMENT Assay_linkoutUrl (#PCDATA)>

<!ELEMENT Assay_method (
        Assay_method_name?, 
        Assay_method_id?, 
        Assay_method_exception)>

<!-- Submitters method identifier -->
<!ELEMENT Assay_method_name (#PCDATA)>

<!-- dbSNP method identifier -->
<!ELEMENT Assay_method_id (#PCDATA)>

<!-- description of deviation from/addition to given method -->
<!ELEMENT Assay_method_exception (#PCDATA)>

<!ELEMENT Assay_taxonomy (
        Assay_taxonomy_id, 
        Assay_taxonomy_organism?)>

<!-- NCBI taxonomy ID for variation -->
<!ELEMENT Assay_taxonomy_id (%INTEGER;)>

<!ELEMENT Assay_taxonomy_organism (#PCDATA)>

<!ELEMENT Assay_strains (Assay_strains_E*)>


<!ELEMENT Assay_strains_E (#PCDATA)>

<!ELEMENT Assay_comment (#PCDATA)>

<!ELEMENT Assay_citation (Assay_citation_E*)>


<!ELEMENT Assay_citation_E (#PCDATA)>

<!-- URL value from dbSNP_main.BaseURL links table. attributes provide context information and URL id that is referenced within individual refSNP objects. -->
<!ELEMENT BaseURL (
        BaseURL_urlId?, 
        BaseURL_resourceName?, 
        BaseURL_resourceId?, 
        BaseURL_baseURL)>

<!-- Resource identifier from dbSNP_main.baseURL. -->
<!ELEMENT BaseURL_urlId (%INTEGER;)>

<!-- Name of linked resource -->
<!ELEMENT BaseURL_resourceName (#PCDATA)>

<!-- identifier expected by resource for URL -->
<!ELEMENT BaseURL_resourceId (#PCDATA)>

<!ELEMENT BaseURL_baseURL (#PCDATA)>


<!ELEMENT PrimarySequence (
        PrimarySequence_dbSnpBuild, 
        PrimarySequence_gi, 
        PrimarySequence_source?, 
        PrimarySequence_accession?, 
        PrimarySequence_mapLoc)>

<!ELEMENT PrimarySequence_dbSnpBuild (%INTEGER;)>

<!ELEMENT PrimarySequence_gi (%INTEGER;)>

<!ELEMENT PrimarySequence_source %ENUM;>
<!ATTLIST PrimarySequence_source value (
        submitter |
        blastmb |
        xm
        ) #REQUIRED >


<!ELEMENT PrimarySequence_accession (#PCDATA)>

<!ELEMENT PrimarySequence_mapLoc (MapLoc*)>

<!-- structure information for SNP -->
<!ELEMENT RsStruct (
        RsStruct_protAcc?, 
        RsStruct_protGi?, 
        RsStruct_protLoc?, 
        RsStruct_protResidue?, 
        RsStruct_rsResidue?, 
        RsStruct_structGi?, 
        RsStruct_structLoc?, 
        RsStruct_structResidue?)>

<!-- accession of the protein with variation -->
<!ELEMENT RsStruct_protAcc (#PCDATA)>

<!-- GI of the protein with variation -->
<!ELEMENT RsStruct_protGi (%INTEGER;)>

<!-- position of the residue for the protein GI -->
<!ELEMENT RsStruct_protLoc (%INTEGER;)>

<!-- residue specified for protein at prot-loc location -->
<!ELEMENT RsStruct_protResidue (#PCDATA)>

<!-- alternative residue specified by variation sequence -->
<!ELEMENT RsStruct_rsResidue (#PCDATA)>

<!-- GI of the structure neighbor -->
<!ELEMENT RsStruct_structGi (%INTEGER;)>

<!-- position of the residue for the structure GI -->
<!ELEMENT RsStruct_structLoc (%INTEGER;)>

<!-- residue specified for protein at struct-loc location -->
<!ELEMENT RsStruct_structResidue (#PCDATA)>

<!-- link data for another resource -->
<!ELEMENT RsLinkout (
        RsLinkout_resourceId, 
        RsLinkout_linkValue)>

<!-- BaseURLList.url_id -->
<!ELEMENT RsLinkout_resourceId (#PCDATA)>

<!-- value to append to ResourceURL.base-url for complete link -->
<!ELEMENT RsLinkout_linkValue (#PCDATA)>


<!ELEMENT Component (
        Component_componentType?, 
        Component_ctgId?, 
        Component_accession?, 
        Component_name?, 
        Component_chromosome?, 
        Component_start?, 
        Component_end?, 
        Component_orientation?, 
        Component_gi?, 
        Component_groupTerm?, 
        Component_contigLabel?, 
        Component_mapLoc)>

<!-- type of component: chromosome, contig, gene_region, etc. -->
<!ELEMENT Component_componentType %ENUM;>
<!ATTLIST Component_componentType value (
        contig |
        mrna
        ) #REQUIRED >


<!-- dbSNP contig_id used to join on contig hit / mapset data to these assembly properties -->
<!ELEMENT Component_ctgId (%INTEGER;)>

<!-- Accession[.version] for the sequence component -->
<!ELEMENT Component_accession (#PCDATA)>

<!-- contig name defined as either a submitter local id, element of a whole genome assembly set, or internal NCBI local id -->
<!ELEMENT Component_name (#PCDATA)>

<!-- Organism appropriate chromosome tag, 'Un' reserved for default case of unplaced components -->
<!ELEMENT Component_chromosome (#PCDATA)>

<!-- component starting position on the chromosome (base 0 inclusive) -->
<!ELEMENT Component_start (%INTEGER;)>

<!-- component ending position on the chromosome (base 0 inclusive) -->
<!ELEMENT Component_end (%INTEGER;)>

<!-- orientation of this component to chromosome, forward (fwd) = 0, reverse (rev) = 1, unknown = NULL in ContigInfo.orient. -->
<!ELEMENT Component_orientation %ENUM;>
<!ATTLIST Component_orientation value (
        fwd |
        rev |
        unknown
        ) #REQUIRED >


<!-- NCBI gi for component sequence (equivalent to accession.version) for nucleotide sequence. -->
<!ELEMENT Component_gi (#PCDATA)>

<!-- Identifier label for the genome assembly that defines the contigs in this mapset and their placement within the organism genome. -->
<!ELEMENT Component_groupTerm (#PCDATA)>

<!-- Display label for component -->
<!ELEMENT Component_contigLabel (#PCDATA)>

<!ELEMENT Component_mapLoc (MapLoc*)>


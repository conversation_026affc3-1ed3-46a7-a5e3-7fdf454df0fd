import unittest
from unittest.mock import patch, MagicMock
from datetime import datetime, timedelta
from typing import List, Dict, Any
from literature_search import SearchService, PubMedProvider, ArxivProvider, SmartQuery

class TestSearchService(unittest.TestCase):    
    def setUp(self):
        # 创建模拟的LLM管理器
        self.llm_manager = MagicMock()
        self.llm_provider = "mock_llm"
        self.llm_model = "mock_model"
        
        # 创建模拟的PubMed和Arxiv提供者
        self.pubmed_provider = MagicMock(spec=PubMedProvider)
        self.arxiv_provider = MagicMock(spec=ArxivProvider)
        
        # 配置模拟的提供者
        self.pubmed_provider.provider_name = "PubMed"
        self.arxiv_provider.provider_name = "arXiv"
        
        # 创建搜索服务实例
        self.search_service = SearchService(
            llm_manager=self.llm_manager
        )
        # 替换为模拟的提供者
        self.search_service.providers = [p for p in [self.pubmed_provider, self.arxiv_provider] if p is not None]
    
    def test_skip_arxiv_when_pubmed_has_enough_results(self):
        """测试当PubMed返回足够结果时跳过arXiv搜索"""
        # 模拟PubMed返回35个结果
        mock_pubmed_results = [
            {
                'title': f'Clinical trial on diabetes treatment {i}',
                'abstract': 'A randomized controlled trial on diabetes treatment.',
                'year': '2023',
                'is_clinical': True
            } for i in range(35)
        ]
        
        # 模拟SmartQuery
        with patch('literature_search.SmartQuery') as mock_smart_query:
            mock_query = MagicMock()
            mock_query.get_queries.return_value = ['diabetes treatment']
            mock_smart_query.return_value = mock_query
            
            # 配置模拟的PubMed提供者
            self.pubmed_provider.search.return_value = mock_pubmed_results
            
            # 执行搜索
            results = self.search_service.search_all("diabetes treatment")
            
            # 验证
            self.assertEqual(len(results), 35)  # 应该只返回PubMed的结果
            self.pubmed_provider.search.assert_called()
            self.arxiv_provider.search.assert_not_called()  # 应该没有调用arXiv搜索
    
    def test_use_arxiv_when_pubmed_has_insufficient_results(self):
        """测试当PubMed结果不足时使用arXiv搜索"""
        # 模拟PubMed返回10个结果
        mock_pubmed_results = [
            {
                'title': f'Clinical trial on diabetes treatment {i}',
                'abstract': 'A randomized controlled trial on diabetes treatment.',
                'year': '2023',
                'is_clinical': True
            } for i in range(10)
        ]
        
        # 模拟arXiv返回20个结果
        mock_arxiv_results = [
            {
                'title': f'Clinical study on diabetes management {i}',
                'abstract': 'A clinical study on diabetes management.',
                'year': '2023',
                'is_clinical': True
            } for i in range(20)
        ]
        
        # 模拟SmartQuery
        with patch('literature_search.SmartQuery') as mock_smart_query:
            mock_query = MagicMock()
            mock_query.get_queries.return_value = ['diabetes treatment']
            mock_smart_query.return_value = mock_query
            
            # 配置模拟的提供者
            self.pubmed_provider.search.return_value = mock_pubmed_results
            self.arxiv_provider.search.return_value = mock_arxiv_results
            
            # 执行搜索
            results = self.search_service.search_all("diabetes treatment")
            
            # 验证
            self.assertEqual(len(results), 30)  # 10 + 20 结果
            self.pubmed_provider.search.assert_called()
            self.arxiv_provider.search.assert_called()  # 应该调用了arXiv搜索
    
    def test_clinical_study_filtering(self):
        """测试临床研究筛选功能"""
        from literature_search import ArxivProvider
        
        # 测试临床研究
        clinical_studies = [
            ("Randomized Controlled Trial of New Drug", 
             "A randomized controlled trial involving 100 patients with diabetes.", True),
            ("Clinical Study on Treatment Effects",
             "A clinical study evaluating the effects of a new therapy.", True),
            ("Efficacy of New Treatment in Patients",
             "This study evaluated the efficacy of a new treatment in 150 patients.", True)
        ]
        
        # 测试非临床研究
        non_clinical_studies = [
            ("In Vitro Study of Drug Effects",
             "An in vitro study on cell cultures.", False),
            ("Animal Model of Disease",
             "A study using mouse models to test the drug.", False),
            ("Review of Diabetes Treatments",
             "A systematic review of current diabetes treatments.", False)
        ]
        
        # 测试临床研究检测
        for title, abstract, expected in clinical_studies + non_clinical_studies:
            result = ArxivProvider._is_clinical_study(title, abstract)
            self.assertEqual(result, expected, 
                           f"Failed for: {title}\n{abstract}\nExpected: {expected}")
    
    def test_duplicate_removal(self):
        """测试去重功能"""
        # 创建有重复的结果
        mock_results = [
            {'title': 'Study A', 'abstract': 'Abstract A', 'year': '2023', 'is_clinical': True},
            {'title': 'Study A', 'abstract': 'Abstract A', 'year': '2023', 'is_clinical': True},  # 重复
            {'title': 'Study B', 'abstract': 'Abstract B', 'year': '2023', 'is_clinical': True},
            {'title': 'Study C', 'abstract': 'Abstract C', 'year': '2023', 'is_clinical': True},
            {'title': 'Study B', 'abstract': 'Abstract B', 'year': '2023', 'is_clinical': True}   # 重复
        ]
        
        # 重置模拟对象
        self.pubmed_provider.reset_mock()
        
        # 模拟SmartQuery
        with patch('literature_search.SmartQuery') as mock_smart_query:
            # 配置模拟的SmartQuery
            mock_query = MagicMock()
            mock_query.get_queries.return_value = ['test query']
            mock_smart_query.return_value = mock_query
            
            # 配置模拟的PubMed提供者返回结果
            self.pubmed_provider.search.return_value = mock_results
            
            # 执行搜索
            results = self.search_service.search_all("test query")
            
            # 验证去重
            titles = [r['title'] for r in results]
            unique_titles = set(titles)
            
            # 打印调试信息
            print(f"原始结果数量: {len(mock_results)}")
            print(f"去重后结果数量: {len(results)}")
            print(f"唯一标题数量: {len(unique_titles)}")
            print("所有标题:", titles)
            
            # 验证结果
            self.assertEqual(len(titles), len(unique_titles), 
                           f"发现重复标题: {[t for t in titles if titles.count(t) > 1]}")
            self.assertEqual(len(results), 3, 
                           f"预期3个唯一结果，实际得到{len(results)}个结果")
    
    def test_search_strategy(self):
        """测试检索策略是否正确生成"""
        from literature_search import SmartQuery
        
        # 测试查询
        test_query = "Sepsis AND \"Blood glucose\""
        current_year = datetime.now().year
        
        # 创建SmartQuery实例，不传入llm_manager以使用默认实现
        smart_query = SmartQuery(
            raw_query=test_query,
            llm_manager=None,  # 不传入llm_manager
            llm_provider=None,
            llm_model=None,
            max_results=100
        )
        
        # 直接调用_create_strategies方法
        queries = smart_query._create_strategies()
        
        # 验证查询不为空
        self.assertTrue(queries, "未生成任何查询")
        self.assertLessEqual(len(queries), 5, "生成的查询策略不应超过5个")
        
        # 验证每个查询都包含必要的部分
        required_parts = [
            "Sepsis",
            "\"Blood glucose\"",
            "[pdat]"
        ]
        
        # 验证每个查询
        for i, query in enumerate(queries, 1):
            print(f"\n策略 {i} 查询: {query}")
            
            # 验证基本部分
            for part in required_parts:
                self.assertIn(part, query, f"策略 {i} 缺少必要部分: {part}")
            
            # 验证人类研究筛选条件
            self.assertIn("human", query.lower(), f"策略 {i} 缺少人类研究筛选条件")
            
            # 验证排除动物研究
            self.assertIn("NOT (animal", query, f"策略 {i} 缺少排除动物研究的条件")
            
            # 验证临床研究筛选条件
            self.assertTrue(
                any(term.lower() in query.lower() for term in [
                    "systematic review",
                    "meta-analysis",
                    "randomized controlled trial",
                    "RCT",
                    "clinical trial",
                    "cohort study",
                    "case-control",
                    "observational study"
                ]),
                f"策略 {i} 缺少临床研究筛选条件"
            )
        
        print(f"\n所有 {len(queries)} 个策略测试通过")
    
    def test_search_years_detection(self):
        """测试检索年限检测逻辑"""
        from literature_search import SmartQuery
        
        # 测试快速发展的领域（应返回5年）
        cancer_query = SmartQuery("breast cancer immunotherapy", None, None, None)
        self.assertEqual(cancer_query._get_search_years(), 5, "肿瘤免疫治疗应使用5年检索")
        
        # 测试慢性病（应返回15年）
        diabetes_query = SmartQuery("diabetes management", None, None, None)
        self.assertEqual(diabetes_query._get_search_years(), 15, "糖尿病管理应使用15年检索")
        
        # 测试默认情况（应返回10年）
        default_query = SmartQuery("common cold treatment", None, None, None)
        self.assertEqual(default_query._get_search_years(), 10, "普通疾病应使用10年检索")

if __name__ == '__main__':
    unittest.main()

<!-- ============================================================= -->
<!--  MODULE:    Notation Declarations                             -->
<!--  VERSION:   2.0                                               -->
<!--  DATE:      July 2003                                         -->
<!--                                                               -->
<!-- ============================================================= -->

<!-- ============================================================= -->
<!--                    PUBLIC DOCUMENT TYPE DEFINITION            -->
<!--                        TYPICAL INVOCATION                     -->
<!--
"-//NLM//DTD Archiving and Interchange DTD Suite Notation Declarations v2.0 20040830//EN
     Delivered as file "notat.ent"                                 -->
<!-- ============================================================= -->

<!-- ============================================================= -->
<!-- SYSTEM:     Archiving and Interchange DTD Suite               -->
<!--                                                               -->
<!-- PURPOSE:    To name all the allowable notations               -->
<!--                                                               -->
<!-- CONTAINS:   Notation Declarations                             -->
<!--                                                               -->
<!-- CREATED FOR:                                                  -->
<!--             Digital archives and publishers who wish to       -->
<!--             create a custom XML DTD for original markup of    -->
<!--             journal literature, books, and related material,  -->
<!--             or for archiving and transferring such material   -->
<!--             between archives.                                 -->
<!--                                                               -->
<!--             This DTD is in the public domain. An organization -->
<!--             that wishes to create its own DTD from the suite  -->
<!--             may do so without permission from NLM.            -->
<!--                                                               -->
<!--             The suite has been set up to be extended using a  -->
<!--             new DTD file and a new DTD-specific customization -->
<!--             module to redefine the many Parameter Entities.   -->
<!--             Do not modify the suite directly or redistribute  -->
<!--             modified versions of the suite.                   -->
<!--                                                               -->
<!--             In the interest of maintaining consistency and    -->
<!--             clarity for potential users, NLM requests:        -->
<!--                                                               -->
<!--             1. If you create a DTD from the Archiving and     -->
<!--                Interchange DTD Suite and intend to stay       -->
<!--                compatible with the suite, then please include -->
<!--                the following statement as a comment in all of -->
<!--                your DTD modules:                              -->
<!--                   "Created from, and fully compatible with,   -->
<!--                    the Archiving and Interchange DTD Suite."  -->
<!--                                                               -->
<!--             2. If you alter one or more modules of the suite, -->
<!--                then please rename your version and all its    -->
<!--                modules to avoid any confusion with the        -->
<!--                original suite. Also, please include the       -->
<!--                following statement as a comment in all your   -->
<!--                DTD modules:                                   -->
<!--                   "Based in part on, but not fully compatible -->
<!--                    with, the Archiving and Interchange DTD    --> 
<!--                    Suite."                                    -->
<!--                                                               -->
<!--             Suggestions for refinements and enhancements to   -->
<!--             the DTD suite should be sent in email to:         -->
<!--                 <EMAIL>                  -->
<!--                                                               -->
<!-- ORIGINAL CREATION DATE:                                       -->
<!--             December 2002                                     -->
<!--                                                               -->
<!-- CREATED BY: Jeff Beck       (NCBI)                            -->
<!--             Deborah Lapeyre (Mulberry Technologies, Inc.)     -->
<!--             Bruce Rosenblum (Inera Inc.)                      -->
<!--                                                               -->
<!--             NLM thanks the Harvard University Libraries, both -->
<!--             for proposing that a draft archiving NLM DTD for  --> 
<!--             life sciences journals be extended to accommodate -->
<!--             journals in all disciplines and for sponsoring    -->
<!--             Bruce Rosenblum's collaboration with other DTD    -->
<!--             authors in completing Version 1.0. The Andrew W.  --> 
<!--             Mellon Foundation provided support for these      --> 
<!--             important contributions.                          -->
<!--                                                               -->
<!-- ============================================================= -->


<!-- ============================================================= -->
<!--                    DTD VERSION/CHANGE HISTORY                 -->
<!-- ============================================================= -->
<!--
     =============================================================

Version  Reason/Occasion                   (who) vx.x (yyyy-mm-dd)

     =============================================================
     Version 2.0                       (DAL/BTU) v2.0 (2004-08-30)
     
     Major requirement changes led to the new release, producing
     DTD version "2.0":
       a) The splitting of the Archival and Interchange Tag Set 
          DTDs into three DTDs from two: an authoring DTD, an
          archive regularization and interchange DTD (the
          current Blue Publishing DTD), and a preservationist 
          archive DTD (the current Green Archiving and Interchange 
          DTD).
       b) AIT Working Group suggestions from the June 04 meeting 
          and June/July 2004 followup discussions
       c) Suite remodularization to meet new (and newly articulated)
          modularization requirements
       d) New or renamed classes and mixes to make modifications
          easier and more consistent
          modularization requirements.

  2. LATEX - Added as a notation for display and inline formulas  
  
  1. Updated public identifier to "v2.0 20040830"                  -->


<!-- ============================================================= -->
<!--               NOTATION DECLARATIONS                           -->
<!-- ============================================================= -->

 
<!--               LATEX (for Mathematics)                         -->
<!NOTATION  LaTeX  PUBLIC
           "+//ISBN 3-893-196463::Goosens//NOTATION Der LaTeX Begleiter//DE"  >

 
<!--               TEX (for Mathematics)                           -->
<!NOTATION  TEX  PUBLIC
           "+//ISBN 0-201-13448-9::Knuth//NOTATION The TeXbook//EN"  >
<!NOTATION  tex  PUBLIC
           "+//ISBN 0-201-13448-9::Knuth//NOTATION The TeXbook//EN"  >
<!NOTATION  TeX  PUBLIC
           "+//ISBN 0-201-13448-9::Knuth//NOTATION The TeXbook//EN"  >


<!-- ============================================================= -->
<!--               POTENTIAL NOTATION DECLARATIONS                 -->
<!-- ============================================================= -->


<!--               CGM (Computer Graphics Metafile)                -->
<!NOTATION cgmchar PUBLIC 
"ISO 8632/2//NOTATION Character encoding//EN"
                                                                     >
<!NOTATION cgmclear PUBLIC 
"ISO 8632/4//NOTATION Clear text encoding//EN"
                                                                     >


<!--               GIF (Graphic Interchange Format)                -->
<!NOTATION gif     PUBLIC
"-//ISBN 0-7923-9432-1::Graphic Notation//NOTATION CompuServe 
Graphic Interchange Format//EN"                                      
                                                                     >

<!--               EPS (Adobe's Encapsulate Postscript)            -->
<!NOTATION eps     PUBLIC
"+//ISBN 0-201-18127-4::Adobe//NOTATION PostScript Language Reference Manual//EN" 
                                                                     >


<!--               JPEG (Joint Photographic Experts Group raster)  -->
<!NOTATION jpeg    PUBLIC
"+//ISBN 0-7923-9432-1::Graphic Notation//NOTATION Joint Photographic Experts Group raster//EN"                               
                                                                     >

<!--               TIFF (Uncompressed)                             -->
<!NOTATION tiff    PUBLIC
"+//ISBN 0-7923-9432-1::Graphic Notation//NOTATION Aldus/Microsoft 
Tagged Interchange File Format//EN"                                  >


<!-- ================== End Notation Module ====================== -->

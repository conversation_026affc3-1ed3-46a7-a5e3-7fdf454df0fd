<!-- ============================================
     ::DATATOOL:: Generated from "seqfeat.asn"
     ::DATATOOL:: by application DATATOOL version 1.9.0
     ::DATATOOL:: on 09/15/2008 23:08:25
     ============================================ -->

<!-- ============================================ -->
<!-- This section is mapped from module "NCBI-RNA"
================================================= -->

<!--
**********************************************************************

  NCBI RNAs
  by <PERSON>, 1990
  version 0.8

**********************************************************************
-->

<!-- Elements used by other modules:
          RNA-ref,
          Trna-ext,
          RNA-gen,
          RNA-qual,
          RNA-qual-set -->

<!-- Elements referenced from other modules:
          Seq-loc FROM NCBI-Seqloc -->
<!-- ============================================ -->

<!--
*** rnas ***********************************************
*
*  various rnas
*
 minimal RNA sequence
-->
<!ELEMENT RNA-ref (
        RNA-ref_type, 
        RNA-ref_pseudo?, 
        RNA-ref_ext?)>
<!-- type of RNA feature -->
<!ELEMENT RNA-ref_type %ENUM;>

<!--
    snRNA	-  will become ncRNA, with RNA-gen.class = snRNA
    scRNA	-  will become ncRNA, with RNA-gen.class = scRNA
    snoRNA	-  will become ncRNA, with RNA-gen.class = snoRNA
    ncRNA	-  non-coding RNA; subsumes snRNA, scRNA, snoRNA
-->
<!ATTLIST RNA-ref_type value (
        unknown |
        premsg |
        mRNA |
        tRNA |
        rRNA |
        snRNA |
        scRNA |
        snoRNA |
        ncRNA |
        tmRNA |
        miscRNA |
        other
        ) #REQUIRED >


<!ELEMENT RNA-ref_pseudo EMPTY>
<!ATTLIST RNA-ref_pseudo value ( true | false ) #REQUIRED >


<!-- generic fields for ncRNA, tmRNA, miscRNA -->
<!ELEMENT RNA-ref_ext (
        RNA-ref_ext_name | 
        RNA-ref_ext_tRNA | 
        RNA-ref_ext_gen)>

<!-- for naming "other" type -->
<!ELEMENT RNA-ref_ext_name (#PCDATA)>

<!-- for tRNAs -->
<!ELEMENT RNA-ref_ext_tRNA (Trna-ext)>

<!ELEMENT RNA-ref_ext_gen (RNA-gen)>

<!-- tRNA feature extensions -->
<!ELEMENT Trna-ext (
        Trna-ext_aa?, 
        Trna-ext_codon?, 
        Trna-ext_anticodon?)>
<!-- aa this carries -->
<!ELEMENT Trna-ext_aa (
        Trna-ext_aa_iupacaa | 
        Trna-ext_aa_ncbieaa | 
        Trna-ext_aa_ncbi8aa | 
        Trna-ext_aa_ncbistdaa)>

<!ELEMENT Trna-ext_aa_iupacaa (%INTEGER;)>

<!ELEMENT Trna-ext_aa_ncbieaa (%INTEGER;)>

<!ELEMENT Trna-ext_aa_ncbi8aa (%INTEGER;)>

<!ELEMENT Trna-ext_aa_ncbistdaa (%INTEGER;)>

<!-- codon(s) as in Genetic-code -->
<!ELEMENT Trna-ext_codon (Trna-ext_codon_E*)>


<!ELEMENT Trna-ext_codon_E (%INTEGER;)>

<!-- location of anticodon -->
<!ELEMENT Trna-ext_anticodon (Seq-loc)>


<!ELEMENT RNA-gen (
        RNA-gen_class?, 
        RNA-gen_product?, 
        RNA-gen_quals?)>

<!--
 for ncRNAs, the class of non-coding RNA:
 examples: antisense_RNA, guide_RNA, snRNA
-->
<!ELEMENT RNA-gen_class (#PCDATA)>

<!ELEMENT RNA-gen_product (#PCDATA)>

<!-- e.g., tag_peptide qualifier for tmRNAs -->
<!ELEMENT RNA-gen_quals (RNA-qual-set)>

<!-- Additional data values for RNA-gen, -->
<!ELEMENT RNA-qual (
        RNA-qual_qual, 
        RNA-qual_val)>

<!-- in a tag (qual), value (val) format -->
<!ELEMENT RNA-qual_qual (#PCDATA)>

<!ELEMENT RNA-qual_val (#PCDATA)>


<!ELEMENT RNA-qual-set (RNA-qual*)>


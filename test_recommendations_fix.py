#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试修复后的临床推荐功能
"""

import json
import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.getcwd())

def test_clinical_recommendations():
    """测试临床推荐功能"""
    try:
        from ebm_professional_enhancements import ProfessionalEBMProcessor, ClinicalRecommendationGenerator
        
        # 加载测试数据
        with open('output/脓毒血症和血糖_search_results.json', 'r', encoding='utf-8') as f:
            studies = json.load(f)
        
        print(f"加载了 {len(studies)} 项研究")
        
        # 创建处理器并测试质量评估
        processor = ProfessionalEBMProcessor(None)
        quality_summary = processor.generate_quality_summary(studies[:20])
        
        print("\n=== 质量评估结果 ===")
        print(f"总研究数: {quality_summary.get('total_studies', 0)}")
        print(f"质量分布: {quality_summary.get('quality_distribution', {})}")
        print(f"整体推荐: {quality_summary.get('recommendation', '未知')}")
        
        # 测试临床推荐生成
        rec_generator = ClinicalRecommendationGenerator(None)
        evidence_summary = {"total_studies": 20, "key_findings": []}
        
        recommendations = rec_generator.generate_clinical_recommendations(
            evidence_summary, quality_summary, "openai", "gpt-4"
        )
        
        print(f"\n=== 临床推荐结果 ===")
        print(f"主要推荐数量: {len(recommendations.get('primary_recommendations', []))}")
        
        for i, rec in enumerate(recommendations.get('primary_recommendations', [])[:3], 1):
            print(f"\n推荐 {i}:")
            print(f"  内容: {rec.get('recommendation', '未知')}")
            print(f"  强度: {rec.get('strength', '未知')}")
            print(f"  证据等级: {rec.get('evidence_level', '未知')}")
            print(f"  理由: {rec.get('rationale', '未知')}")
        
        print(f"\n实施指导: {recommendations.get('implementation_considerations', [])}")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_clinical_recommendations()
    if success:
        print("\n✅ 临床推荐修复测试通过")
    else:
        print("\n❌ 临床推荐修复测试失败")
        sys.exit(1)

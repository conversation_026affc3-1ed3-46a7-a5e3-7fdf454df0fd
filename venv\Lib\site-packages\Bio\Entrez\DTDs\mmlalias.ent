
<!--
     File mmlalias.ent produced by the XSL script characters.xsl
     from input data in unicode.xml.

     Please report any errors to <PERSON>
     via the public W3<NAME_EMAIL>.

     The numeric character values assigned to each entity
     (should) match the Unicode assignments in Unicode 4.0.

     References to the VARIANT SELECTOR 1 character (&#x0FE00;)
     should match the uses listed in Unicode Technical Report 25.

-->

<!ENTITY angle            "&#x02220;" ><!--alias ISOAMSO ang -->
<!ENTITY ApplyFunction    "&#x02061;" ><!--character showing function application in presentation tagging -->
<!ENTITY approx           "&#x02248;" ><!--alias ISOTECH ap -->
<!ENTITY approxeq         "&#x0224A;" ><!--alias ISOAMSR ape -->
<!ENTITY Assign           "&#x02254;" ><!--assignment operator, alias ISOAMSR colone -->
<!ENTITY backcong         "&#x0224C;" ><!--alias ISOAMSR bcong -->
<!ENTITY backepsilon      "&#x003F6;" ><!--alias <PERSON>AMSR bepsi -->
<!ENTITY backprime        "&#x02035;" ><!--alias ISOAMSO bprime -->
<!ENTITY backsim          "&#x0223D;" ><!--alias ISOAMSR bsim -->
<!ENTITY backsimeq        "&#x022CD;" ><!--alias ISOAMSR bsime -->
<!ENTITY Backslash        "&#x02216;" ><!--alias ISOAMSB setmn -->
<!ENTITY barwedge         "&#x02305;" ><!--alias ISOAMSB barwed -->
<!ENTITY Because          "&#x02235;" ><!--alias ISOTECH becaus -->
<!ENTITY because          "&#x02235;" ><!--alias ISOTECH becaus -->
<!ENTITY Bernoullis       "&#x0212C;" ><!--alias ISOTECH bernou -->
<!ENTITY between          "&#x0226C;" ><!--alias ISOAMSR twixt -->
<!ENTITY bigcap           "&#x022C2;" ><!--alias ISOAMSB xcap -->
<!ENTITY bigcirc          "&#x025EF;" ><!--alias ISOAMSB xcirc -->
<!ENTITY bigcup           "&#x022C3;" ><!--alias ISOAMSB xcup -->
<!ENTITY bigodot          "&#x02A00;" ><!--alias ISOAMSB xodot -->
<!ENTITY bigoplus         "&#x02A01;" ><!--alias ISOAMSB xoplus -->
<!ENTITY bigotimes        "&#x02A02;" ><!--alias ISOAMSB xotime -->
<!ENTITY bigsqcup         "&#x02A06;" ><!--alias ISOAMSB xsqcup -->
<!ENTITY bigstar          "&#x02605;" ><!--ISOPUB    starf  -->
<!ENTITY bigtriangledown  "&#x025BD;" ><!--alias ISOAMSB xdtri -->
<!ENTITY bigtriangleup    "&#x025B3;" ><!--alias ISOAMSB xutri -->
<!ENTITY biguplus         "&#x02A04;" ><!--alias ISOAMSB xuplus -->
<!ENTITY bigvee           "&#x022C1;" ><!--alias ISOAMSB xvee -->
<!ENTITY bigwedge         "&#x022C0;" ><!--alias ISOAMSB xwedge -->
<!ENTITY bkarow           "&#x0290D;" ><!--alias ISOAMSA rbarr -->
<!ENTITY blacklozenge     "&#x029EB;" ><!--alias ISOPUB lozf -->
<!ENTITY blacksquare      "&#x025AA;" ><!--ISOTECH  squarf  -->
<!ENTITY blacktriangle    "&#x025B4;" ><!--alias ISOPUB utrif -->
<!ENTITY blacktriangledown "&#x025BE;" ><!--alias ISOPUB dtrif -->
<!ENTITY blacktriangleleft "&#x025C2;" ><!--alias ISOPUB ltrif -->
<!ENTITY blacktriangleright "&#x025B8;" ><!--alias ISOPUB rtrif -->
<!ENTITY bot              "&#x022A5;" ><!--alias ISOTECH bottom -->
<!ENTITY boxminus         "&#x0229F;" ><!--alias ISOAMSB minusb -->
<!ENTITY boxplus          "&#x0229E;" ><!--alias ISOAMSB plusb -->
<!ENTITY boxtimes         "&#x022A0;" ><!--alias ISOAMSB timesb -->
<!ENTITY Breve            "&#x002D8;" ><!--alias ISODIA breve -->
<!ENTITY bullet           "&#x02022;" ><!--alias ISOPUB bull -->
<!ENTITY Bumpeq           "&#x0224E;" ><!--alias ISOAMSR bump -->
<!ENTITY bumpeq           "&#x0224F;" ><!--alias ISOAMSR bumpe -->
<!ENTITY CapitalDifferentialD "&#x02145;" ><!--D for use in differentials, e.g., within integrals -->
<!ENTITY Cayleys          "&#x0212D;" ><!--the non-associative ring of octonions or Cayley numbers -->
<!ENTITY Cedilla          "&#x000B8;" ><!--alias ISODIA cedil -->
<!ENTITY CenterDot        "&#x000B7;" ><!--alias ISONUM middot -->
<!ENTITY centerdot        "&#x000B7;" ><!--alias ISONUM middot -->
<!ENTITY checkmark        "&#x02713;" ><!--alias ISOPUB check -->
<!ENTITY circeq           "&#x02257;" ><!--alias ISOAMSR cire -->
<!ENTITY circlearrowleft  "&#x021BA;" ><!--alias ISOAMSA olarr -->
<!ENTITY circlearrowright "&#x021BB;" ><!--alias ISOAMSA orarr -->
<!ENTITY circledast       "&#x0229B;" ><!--alias ISOAMSB oast -->
<!ENTITY circledcirc      "&#x0229A;" ><!--alias ISOAMSB ocir -->
<!ENTITY circleddash      "&#x0229D;" ><!--alias ISOAMSB odash -->
<!ENTITY CircleDot        "&#x02299;" ><!--alias ISOAMSB odot -->
<!ENTITY circledR         "&#x000AE;" ><!--alias ISONUM reg -->
<!ENTITY circledS         "&#x024C8;" ><!--alias ISOAMSO oS -->
<!ENTITY CircleMinus      "&#x02296;" ><!--alias ISOAMSB ominus -->
<!ENTITY CirclePlus       "&#x02295;" ><!--alias ISOAMSB oplus -->
<!ENTITY CircleTimes      "&#x02297;" ><!--alias ISOAMSB otimes -->
<!ENTITY ClockwiseContourIntegral "&#x02232;" ><!--alias ISOTECH cwconint -->
<!ENTITY CloseCurlyDoubleQuote "&#x0201D;" ><!--alias ISONUM rdquo -->
<!ENTITY CloseCurlyQuote  "&#x02019;" ><!--alias ISONUM rsquo -->
<!ENTITY clubsuit         "&#x02663;" ><!--ISOPUB    clubs  -->
<!ENTITY coloneq          "&#x02254;" ><!--alias ISOAMSR colone -->
<!ENTITY complement       "&#x02201;" ><!--alias ISOAMSO comp -->
<!ENTITY complexes        "&#x02102;" ><!--the field of complex numbers -->
<!ENTITY Congruent        "&#x02261;" ><!--alias ISOTECH equiv -->
<!ENTITY ContourIntegral  "&#x0222E;" ><!--alias ISOTECH conint -->
<!ENTITY Coproduct        "&#x02210;" ><!--alias ISOAMSB coprod -->
<!ENTITY CounterClockwiseContourIntegral "&#x02233;" ><!--alias ISOTECH awconint -->
<!ENTITY CupCap           "&#x0224D;" ><!--alias asympeq -->
<!ENTITY curlyeqprec      "&#x022DE;" ><!--alias ISOAMSR cuepr -->
<!ENTITY curlyeqsucc      "&#x022DF;" ><!--alias ISOAMSR cuesc -->
<!ENTITY curlyvee         "&#x022CE;" ><!--alias ISOAMSB cuvee -->
<!ENTITY curlywedge       "&#x022CF;" ><!--alias ISOAMSB cuwed -->
<!ENTITY curvearrowleft   "&#x021B6;" ><!--alias ISOAMSA cularr -->
<!ENTITY curvearrowright  "&#x021B7;" ><!--alias ISOAMSA curarr -->
<!ENTITY dbkarow          "&#x0290F;" ><!--alias ISOAMSA rBarr -->
<!ENTITY ddagger          "&#x02021;" ><!--alias ISOPUB Dagger -->
<!ENTITY ddotseq          "&#x02A77;" ><!--alias ISOAMSR eDDot -->
<!ENTITY Del              "&#x02207;" ><!--alias ISOTECH nabla -->
<!ENTITY DiacriticalAcute "&#x000B4;" ><!--alias ISODIA acute -->
<!ENTITY DiacriticalDot   "&#x002D9;" ><!--alias ISODIA dot -->
<!ENTITY DiacriticalDoubleAcute "&#x002DD;" ><!--alias ISODIA dblac -->
<!ENTITY DiacriticalGrave "&#x00060;" ><!--alias ISODIA grave -->
<!ENTITY DiacriticalTilde "&#x002DC;" ><!--alias ISODIA tilde -->
<!ENTITY Diamond          "&#x022C4;" ><!--alias ISOAMSB diam -->
<!ENTITY diamond          "&#x022C4;" ><!--alias ISOAMSB diam -->
<!ENTITY diamondsuit      "&#x02666;" ><!--ISOPUB    diams  -->
<!ENTITY DifferentialD    "&#x02146;" ><!--d for use in differentials, e.g., within integrals -->
<!ENTITY digamma          "&#x003DD;" ><!--alias ISOGRK3 gammad -->
<!ENTITY div              "&#x000F7;" ><!--alias ISONUM divide -->
<!ENTITY divideontimes    "&#x022C7;" ><!--alias ISOAMSB divonx -->
<!ENTITY doteq            "&#x02250;" ><!--alias ISOAMSR esdot -->
<!ENTITY doteqdot         "&#x02251;" ><!--alias ISOAMSR eDot -->
<!ENTITY DotEqual         "&#x02250;" ><!--alias ISOAMSR esdot -->
<!ENTITY dotminus         "&#x02238;" ><!--alias ISOAMSB minusd -->
<!ENTITY dotplus          "&#x02214;" ><!--alias ISOAMSB plusdo -->
<!ENTITY dotsquare        "&#x022A1;" ><!--alias ISOAMSB sdotb -->
<!ENTITY doublebarwedge   "&#x02306;" ><!--alias ISOAMSB Barwed -->
<!ENTITY DoubleContourIntegral "&#x0222F;" ><!--alias ISOTECH Conint -->
<!ENTITY DoubleDot        "&#x000A8;" ><!--alias ISODIA die -->
<!ENTITY DoubleDownArrow  "&#x021D3;" ><!--alias ISOAMSA dArr -->
<!ENTITY DoubleLeftArrow  "&#x021D0;" ><!--alias ISOTECH lArr -->
<!ENTITY DoubleLeftRightArrow "&#x021D4;" ><!--alias ISOAMSA hArr -->
<!ENTITY DoubleLeftTee    "&#x02AE4;" ><!--alias for  &Dashv;  -->
<!ENTITY DoubleLongLeftArrow "&#x027F8;" ><!--alias ISOAMSA xlArr -->
<!ENTITY DoubleLongLeftRightArrow "&#x027FA;" ><!--alias ISOAMSA xhArr -->
<!ENTITY DoubleLongRightArrow "&#x027F9;" ><!--alias ISOAMSA xrArr -->
<!ENTITY DoubleRightArrow "&#x021D2;" ><!--alias ISOTECH rArr -->
<!ENTITY DoubleRightTee   "&#x022A8;" ><!--alias ISOAMSR vDash -->
<!ENTITY DoubleUpArrow    "&#x021D1;" ><!--alias ISOAMSA uArr -->
<!ENTITY DoubleUpDownArrow "&#x021D5;" ><!--alias ISOAMSA vArr -->
<!ENTITY DoubleVerticalBar "&#x02225;" ><!--alias ISOTECH par -->
<!ENTITY DownArrow        "&#x02193;" ><!--alias ISONUM darr -->
<!ENTITY Downarrow        "&#x021D3;" ><!--alias ISOAMSA dArr -->
<!ENTITY downarrow        "&#x02193;" ><!--alias ISONUM darr -->
<!ENTITY DownArrowUpArrow "&#x021F5;" ><!--alias ISOAMSA duarr -->
<!ENTITY downdownarrows   "&#x021CA;" ><!--alias ISOAMSA ddarr -->
<!ENTITY downharpoonleft  "&#x021C3;" ><!--alias ISOAMSA dharl -->
<!ENTITY downharpoonright "&#x021C2;" ><!--alias ISOAMSA dharr -->
<!ENTITY DownLeftVector   "&#x021BD;" ><!--alias ISOAMSA lhard -->
<!ENTITY DownRightVector  "&#x021C1;" ><!--alias ISOAMSA rhard -->
<!ENTITY DownTee          "&#x022A4;" ><!--alias ISOTECH top -->
<!ENTITY DownTeeArrow     "&#x021A7;" ><!--alias for mapstodown -->
<!ENTITY drbkarow         "&#x02910;" ><!--alias ISOAMSA RBarr -->
<!ENTITY Element          "&#x02208;" ><!--alias ISOTECH isinv -->
<!ENTITY emptyset         "&#x02205;" ><!--alias ISOAMSO empty -->
<!ENTITY eqcirc           "&#x02256;" ><!--alias ISOAMSR ecir -->
<!ENTITY eqcolon          "&#x02255;" ><!--alias ISOAMSR ecolon -->
<!ENTITY eqsim            "&#x02242;" ><!--alias ISOAMSR esim -->
<!ENTITY eqslantgtr       "&#x02A96;" ><!--alias ISOAMSR egs -->
<!ENTITY eqslantless      "&#x02A95;" ><!--alias ISOAMSR els -->
<!ENTITY EqualTilde       "&#x02242;" ><!--alias ISOAMSR esim -->
<!ENTITY Equilibrium      "&#x021CC;" ><!--alias ISOAMSA rlhar -->
<!ENTITY Exists           "&#x02203;" ><!--alias ISOTECH exist -->
<!ENTITY expectation      "&#x02130;" ><!--expectation (operator) -->
<!ENTITY ExponentialE     "&#x02147;" ><!--e use for the exponential base of the natural logarithms -->
<!ENTITY exponentiale     "&#x02147;" ><!--base of the Napierian logarithms -->
<!ENTITY fallingdotseq    "&#x02252;" ><!--alias ISOAMSR efDot -->
<!ENTITY ForAll           "&#x02200;" ><!--alias ISOTECH forall -->
<!ENTITY Fouriertrf       "&#x02131;" ><!--Fourier transform -->
<!ENTITY geq              "&#x02265;" ><!--alias ISOTECH ge -->
<!ENTITY geqq             "&#x02267;" ><!--alias ISOAMSR gE -->
<!ENTITY geqslant         "&#x02A7E;" ><!--alias ISOAMSR ges -->
<!ENTITY gg               "&#x0226B;" ><!--alias ISOAMSR Gt -->
<!ENTITY ggg              "&#x022D9;" ><!--alias ISOAMSR Gg -->
<!ENTITY gnapprox         "&#x02A8A;" ><!--alias ISOAMSN gnap -->
<!ENTITY gneq             "&#x02A88;" ><!--alias ISOAMSN gne -->
<!ENTITY gneqq            "&#x02269;" ><!--alias ISOAMSN gnE -->
<!ENTITY GreaterEqual     "&#x02265;" ><!--alias ISOTECH ge -->
<!ENTITY GreaterEqualLess "&#x022DB;" ><!--alias ISOAMSR gel -->
<!ENTITY GreaterFullEqual "&#x02267;" ><!--alias ISOAMSR gE -->
<!ENTITY GreaterLess      "&#x02277;" ><!--alias ISOAMSR gl -->
<!ENTITY GreaterSlantEqual "&#x02A7E;" ><!--alias ISOAMSR ges -->
<!ENTITY GreaterTilde     "&#x02273;" ><!--alias ISOAMSR gsim -->
<!ENTITY gtrapprox        "&#x02A86;" ><!--alias ISOAMSR gap -->
<!ENTITY gtrdot           "&#x022D7;" ><!--alias ISOAMSR gtdot -->
<!ENTITY gtreqless        "&#x022DB;" ><!--alias ISOAMSR gel -->
<!ENTITY gtreqqless       "&#x02A8C;" ><!--alias ISOAMSR gEl -->
<!ENTITY gtrless          "&#x02277;" ><!--alias ISOAMSR gl -->
<!ENTITY gtrsim           "&#x02273;" ><!--alias ISOAMSR gsim -->
<!ENTITY gvertneqq        "&#x02269;&#x0FE00;" ><!--alias ISOAMSN gvnE -->
<!ENTITY Hacek            "&#x002C7;" ><!--alias ISODIA caron -->
<!ENTITY hbar             "&#x0210F;" ><!--alias ISOAMSO plank -->
<!ENTITY heartsuit        "&#x02665;" ><!--ISOPUB hearts -->
<!ENTITY HilbertSpace     "&#x0210B;" ><!--Hilbert space -->
<!ENTITY hksearow         "&#x02925;" ><!--alias ISOAMSA searhk -->
<!ENTITY hkswarow         "&#x02926;" ><!--alias ISOAMSA swarhk -->
<!ENTITY hookleftarrow    "&#x021A9;" ><!--alias ISOAMSA larrhk -->
<!ENTITY hookrightarrow   "&#x021AA;" ><!--alias ISOAMSA rarrhk -->
<!ENTITY hslash           "&#x0210F;" ><!--alias ISOAMSO plankv -->
<!ENTITY HumpDownHump     "&#x0224E;" ><!--alias ISOAMSR bump -->
<!ENTITY HumpEqual        "&#x0224F;" ><!--alias ISOAMSR bumpe -->
<!ENTITY iiiint           "&#x02A0C;" ><!--alias ISOTECH qint -->
<!ENTITY iiint            "&#x0222D;" ><!--alias ISOTECH tint -->
<!ENTITY Im               "&#x02111;" ><!--alias ISOAMSO image -->
<!ENTITY ImaginaryI       "&#x02148;" ><!--i for use as a square root of -1 -->
<!ENTITY imagline         "&#x02110;" ><!--the geometric imaginary line -->
<!ENTITY imagpart         "&#x02111;" ><!--alias ISOAMSO image -->
<!ENTITY Implies          "&#x021D2;" ><!--alias ISOTECH rArr -->
<!ENTITY in               "&#x02208;" ><!--ISOTECH   isin  -->
<!ENTITY integers         "&#x02124;" ><!--the ring of integers -->
<!ENTITY Integral         "&#x0222B;" ><!--alias ISOTECH int -->
<!ENTITY intercal         "&#x022BA;" ><!--alias ISOAMSB intcal -->
<!ENTITY Intersection     "&#x022C2;" ><!--alias ISOAMSB xcap -->
<!ENTITY intprod          "&#x02A3C;" ><!--alias ISOAMSB iprod -->
<!ENTITY InvisibleComma   "&#x02063;" ><!--used as a separator, e.g., in indices -->
<!ENTITY InvisibleTimes   "&#x02062;" ><!--marks multiplication when it is understood without a mark -->
<!ENTITY langle           "&#x02329;" ><!--alias ISOTECH lang -->
<!ENTITY Laplacetrf       "&#x02112;" ><!--Laplace transform -->
<!ENTITY lbrace           "&#x0007B;" ><!--alias ISONUM lcub -->
<!ENTITY lbrack           "&#x0005B;" ><!--alias ISONUM lsqb -->
<!ENTITY LeftAngleBracket "&#x02329;" ><!--alias ISOTECH lang -->
<!ENTITY LeftArrow        "&#x02190;" ><!--alias ISONUM larr -->
<!ENTITY Leftarrow        "&#x021D0;" ><!--alias ISOTECH lArr -->
<!ENTITY leftarrow        "&#x02190;" ><!--alias ISONUM larr -->
<!ENTITY LeftArrowBar     "&#x021E4;" ><!--alias for larrb -->
<!ENTITY LeftArrowRightArrow "&#x021C6;" ><!--alias ISOAMSA lrarr -->
<!ENTITY leftarrowtail    "&#x021A2;" ><!--alias ISOAMSA larrtl -->
<!ENTITY LeftCeiling      "&#x02308;" ><!--alias ISOAMSC lceil -->
<!ENTITY LeftDoubleBracket "&#x0301A;" ><!--left double bracket delimiter -->
<!ENTITY LeftDownVector   "&#x021C3;" ><!--alias ISOAMSA dharl -->
<!ENTITY LeftFloor        "&#x0230A;" ><!--alias ISOAMSC lfloor -->
<!ENTITY leftharpoondown  "&#x021BD;" ><!--alias ISOAMSA lhard -->
<!ENTITY leftharpoonup    "&#x021BC;" ><!--alias ISOAMSA lharu -->
<!ENTITY leftleftarrows   "&#x021C7;" ><!--alias ISOAMSA llarr -->
<!ENTITY LeftRightArrow   "&#x02194;" ><!--alias ISOAMSA harr -->
<!ENTITY Leftrightarrow   "&#x021D4;" ><!--alias ISOAMSA hArr -->
<!ENTITY leftrightarrow   "&#x02194;" ><!--alias ISOAMSA harr -->
<!ENTITY leftrightarrows  "&#x021C6;" ><!--alias ISOAMSA lrarr -->
<!ENTITY leftrightharpoons "&#x021CB;" ><!--alias ISOAMSA lrhar -->
<!ENTITY leftrightsquigarrow "&#x021AD;" ><!--alias ISOAMSA harrw -->
<!ENTITY LeftTee          "&#x022A3;" ><!--alias ISOAMSR dashv -->
<!ENTITY LeftTeeArrow     "&#x021A4;" ><!--alias for mapstoleft -->
<!ENTITY leftthreetimes   "&#x022CB;" ><!--alias ISOAMSB lthree -->
<!ENTITY LeftTriangle     "&#x022B2;" ><!--alias ISOAMSR vltri -->
<!ENTITY LeftTriangleEqual "&#x022B4;" ><!--alias ISOAMSR ltrie -->
<!ENTITY LeftUpVector     "&#x021BF;" ><!--alias ISOAMSA uharl -->
<!ENTITY LeftVector       "&#x021BC;" ><!--alias ISOAMSA lharu -->
<!ENTITY leq              "&#x02264;" ><!--alias ISOTECH le -->
<!ENTITY leqq             "&#x02266;" ><!--alias ISOAMSR lE -->
<!ENTITY leqslant         "&#x02A7D;" ><!--alias ISOAMSR les -->
<!ENTITY lessapprox       "&#x02A85;" ><!--alias ISOAMSR lap -->
<!ENTITY lessdot          "&#x022D6;" ><!--alias ISOAMSR ltdot -->
<!ENTITY lesseqgtr        "&#x022DA;" ><!--alias ISOAMSR leg -->
<!ENTITY lesseqqgtr       "&#x02A8B;" ><!--alias ISOAMSR lEg -->
<!ENTITY LessEqualGreater "&#x022DA;" ><!--alias ISOAMSR leg -->
<!ENTITY LessFullEqual    "&#x02266;" ><!--alias ISOAMSR lE -->
<!ENTITY LessGreater      "&#x02276;" ><!--alias ISOAMSR lg -->
<!ENTITY lessgtr          "&#x02276;" ><!--alias ISOAMSR lg -->
<!ENTITY lesssim          "&#x02272;" ><!--alias ISOAMSR lsim -->
<!ENTITY LessSlantEqual   "&#x02A7D;" ><!--alias ISOAMSR les -->
<!ENTITY LessTilde        "&#x02272;" ><!--alias ISOAMSR lsim -->
<!ENTITY ll               "&#x0226A;" ><!--alias ISOAMSR Lt -->
<!ENTITY llcorner         "&#x0231E;" ><!--alias ISOAMSC dlcorn -->
<!ENTITY Lleftarrow       "&#x021DA;" ><!--alias ISOAMSA lAarr -->
<!ENTITY lmoustache       "&#x023B0;" ><!--alias ISOAMSC lmoust -->
<!ENTITY lnapprox         "&#x02A89;" ><!--alias ISOAMSN lnap -->
<!ENTITY lneq             "&#x02A87;" ><!--alias ISOAMSN lne -->
<!ENTITY lneqq            "&#x02268;" ><!--alias ISOAMSN lnE -->
<!ENTITY LongLeftArrow    "&#x027F5;" ><!--alias ISOAMSA xlarr -->
<!ENTITY Longleftarrow    "&#x027F8;" ><!--alias ISOAMSA xlArr -->
<!ENTITY longleftarrow    "&#x027F5;" ><!--alias ISOAMSA xlarr -->
<!ENTITY LongLeftRightArrow "&#x027F7;" ><!--alias ISOAMSA xharr -->
<!ENTITY Longleftrightarrow "&#x027FA;" ><!--alias ISOAMSA xhArr -->
<!ENTITY longleftrightarrow "&#x027F7;" ><!--alias ISOAMSA xharr -->
<!ENTITY longmapsto       "&#x027FC;" ><!--alias ISOAMSA xmap -->
<!ENTITY LongRightArrow   "&#x027F6;" ><!--alias ISOAMSA xrarr -->
<!ENTITY Longrightarrow   "&#x027F9;" ><!--alias ISOAMSA xrArr -->
<!ENTITY longrightarrow   "&#x027F6;" ><!--alias ISOAMSA xrarr -->
<!ENTITY looparrowleft    "&#x021AB;" ><!--alias ISOAMSA larrlp -->
<!ENTITY looparrowright   "&#x021AC;" ><!--alias ISOAMSA rarrlp -->
<!ENTITY LowerLeftArrow   "&#x02199;" ><!--alias ISOAMSA swarr -->
<!ENTITY LowerRightArrow  "&#x02198;" ><!--alias ISOAMSA searr -->
<!ENTITY lozenge          "&#x025CA;" ><!--alias ISOPUB loz -->
<!ENTITY lrcorner         "&#x0231F;" ><!--alias ISOAMSC drcorn -->
<!ENTITY Lsh              "&#x021B0;" ><!--alias ISOAMSA lsh -->
<!ENTITY lvertneqq        "&#x02268;&#x0FE00;" ><!--alias ISOAMSN lvnE -->
<!ENTITY maltese          "&#x02720;" ><!--alias ISOPUB malt -->
<!ENTITY mapsto           "&#x021A6;" ><!--alias ISOAMSA map -->
<!ENTITY measuredangle    "&#x02221;" ><!--alias ISOAMSO angmsd -->
<!ENTITY Mellintrf        "&#x02133;" ><!--Mellin transform -->
<!ENTITY MinusPlus        "&#x02213;" ><!--alias ISOTECH mnplus -->
<!ENTITY mp               "&#x02213;" ><!--alias ISOTECH mnplus -->
<!ENTITY multimap         "&#x022B8;" ><!--alias ISOAMSA mumap -->
<!ENTITY napprox          "&#x02249;" ><!--alias ISOAMSN nap -->
<!ENTITY natural          "&#x0266E;" ><!--alias ISOPUB natur -->
<!ENTITY naturals         "&#x02115;" ><!--the semi-ring of natural numbers -->
<!ENTITY nearrow          "&#x02197;" ><!--alias ISOAMSA nearr -->
<!ENTITY NegativeMediumSpace "&#x0200B;" ><!--space of width -4/18 em -->
<!ENTITY NegativeThickSpace "&#x0200B;" ><!--space of width -5/18 em -->
<!ENTITY NegativeThinSpace "&#x0200B;" ><!--space of width -3/18 em -->
<!ENTITY NegativeVeryThinSpace "&#x0200B;" ><!--space of width -1/18 em -->
<!ENTITY NestedGreaterGreater "&#x0226B;" ><!--alias ISOAMSR Gt -->
<!ENTITY NestedLessLess   "&#x0226A;" ><!--alias ISOAMSR Lt -->
<!ENTITY nexists          "&#x02204;" ><!--alias ISOAMSO nexist -->
<!ENTITY ngeq             "&#x02271;" ><!--alias ISOAMSN nge -->
<!ENTITY ngeqq            "&#x02267;&#x00338;" ><!--alias ISOAMSN ngE -->
<!ENTITY ngeqslant        "&#x02A7E;&#x00338;" ><!--alias ISOAMSN nges -->
<!ENTITY ngtr             "&#x0226F;" ><!--alias ISOAMSN ngt -->
<!ENTITY nLeftarrow       "&#x021CD;" ><!--alias ISOAMSA nlArr -->
<!ENTITY nleftarrow       "&#x0219A;" ><!--alias ISOAMSA nlarr -->
<!ENTITY nLeftrightarrow  "&#x021CE;" ><!--alias ISOAMSA nhArr -->
<!ENTITY nleftrightarrow  "&#x021AE;" ><!--alias ISOAMSA nharr -->
<!ENTITY nleq             "&#x02270;" ><!--alias ISOAMSN nle -->
<!ENTITY nleqq            "&#x02266;&#x00338;" ><!--alias ISOAMSN nlE -->
<!ENTITY nleqslant        "&#x02A7D;&#x00338;" ><!--alias ISOAMSN nles -->
<!ENTITY nless            "&#x0226E;" ><!--alias ISOAMSN nlt -->
<!ENTITY NonBreakingSpace "&#x000A0;" ><!--alias ISONUM nbsp -->
<!ENTITY NotCongruent     "&#x02262;" ><!--alias ISOAMSN nequiv -->
<!ENTITY NotDoubleVerticalBar "&#x02226;" ><!--alias ISOAMSN npar -->
<!ENTITY NotElement       "&#x02209;" ><!--alias ISOTECH notin -->
<!ENTITY NotEqual         "&#x02260;" ><!--alias ISOTECH ne -->
<!ENTITY NotEqualTilde    "&#x02242;&#x00338;" ><!--alias for  &nesim; -->
<!ENTITY NotExists        "&#x02204;" ><!--alias ISOAMSO nexist -->
<!ENTITY NotGreater       "&#x0226F;" ><!--alias ISOAMSN ngt -->
<!ENTITY NotGreaterEqual  "&#x02271;" ><!--alias ISOAMSN nge -->
<!ENTITY NotGreaterFullEqual "&#x02266;&#x00338;" ><!--alias ISOAMSN nlE -->
<!ENTITY NotGreaterGreater "&#x0226B;&#x00338;" ><!--alias ISOAMSN nGtv -->
<!ENTITY NotGreaterLess   "&#x02279;" ><!--alias ISOAMSN ntvgl -->
<!ENTITY NotGreaterSlantEqual "&#x02A7E;&#x00338;" ><!--alias ISOAMSN nges -->
<!ENTITY NotGreaterTilde  "&#x02275;" ><!--alias ISOAMSN ngsim -->
<!ENTITY NotHumpDownHump  "&#x0224E;&#x00338;" ><!--alias for &nbump; -->
<!ENTITY NotLeftTriangle  "&#x022EA;" ><!--alias ISOAMSN nltri -->
<!ENTITY NotLeftTriangleEqual "&#x022EC;" ><!--alias ISOAMSN nltrie -->
<!ENTITY NotLess          "&#x0226E;" ><!--alias ISOAMSN nlt -->
<!ENTITY NotLessEqual     "&#x02270;" ><!--alias ISOAMSN nle -->
<!ENTITY NotLessGreater   "&#x02278;" ><!--alias ISOAMSN ntvlg -->
<!ENTITY NotLessLess      "&#x0226A;&#x00338;" ><!--alias ISOAMSN nLtv -->
<!ENTITY NotLessSlantEqual "&#x02A7D;&#x00338;" ><!--alias ISOAMSN nles -->
<!ENTITY NotLessTilde     "&#x02274;" ><!--alias ISOAMSN nlsim -->
<!ENTITY NotPrecedes      "&#x02280;" ><!--alias ISOAMSN npr -->
<!ENTITY NotPrecedesEqual "&#x02AAF;&#x00338;" ><!--alias ISOAMSN npre -->
<!ENTITY NotPrecedesSlantEqual "&#x022E0;" ><!--alias ISOAMSN nprcue -->
<!ENTITY NotReverseElement "&#x0220C;" ><!--alias ISOTECH notniva -->
<!ENTITY NotRightTriangle "&#x022EB;" ><!--alias ISOAMSN nrtri -->
<!ENTITY NotRightTriangleEqual "&#x022ED;" ><!--alias ISOAMSN nrtrie -->
<!ENTITY NotSquareSubsetEqual "&#x022E2;" ><!--alias ISOAMSN nsqsube -->
<!ENTITY NotSquareSupersetEqual "&#x022E3;" ><!--alias ISOAMSN nsqsupe -->
<!ENTITY NotSubset        "&#x02282;&#x020D2;" ><!--alias ISOAMSN vnsub -->
<!ENTITY NotSubsetEqual   "&#x02288;" ><!--alias ISOAMSN nsube -->
<!ENTITY NotSucceeds      "&#x02281;" ><!--alias ISOAMSN nsc -->
<!ENTITY NotSucceedsEqual "&#x02AB0;&#x00338;" ><!--alias ISOAMSN nsce -->
<!ENTITY NotSucceedsSlantEqual "&#x022E1;" ><!--alias ISOAMSN nsccue -->
<!ENTITY NotSuperset      "&#x02283;&#x020D2;" ><!--alias ISOAMSN vnsup -->
<!ENTITY NotSupersetEqual "&#x02289;" ><!--alias ISOAMSN nsupe -->
<!ENTITY NotTilde         "&#x02241;" ><!--alias ISOAMSN nsim -->
<!ENTITY NotTildeEqual    "&#x02244;" ><!--alias ISOAMSN nsime -->
<!ENTITY NotTildeFullEqual "&#x02247;" ><!--alias ISOAMSN ncong -->
<!ENTITY NotTildeTilde    "&#x02249;" ><!--alias ISOAMSN nap -->
<!ENTITY NotVerticalBar   "&#x02224;" ><!--alias ISOAMSN nmid -->
<!ENTITY nparallel        "&#x02226;" ><!--alias ISOAMSN npar -->
<!ENTITY nprec            "&#x02280;" ><!--alias ISOAMSN npr -->
<!ENTITY npreceq          "&#x02AAF;&#x00338;" ><!--alias ISOAMSN npre -->
<!ENTITY nRightarrow      "&#x021CF;" ><!--alias ISOAMSA nrArr -->
<!ENTITY nrightarrow      "&#x0219B;" ><!--alias ISOAMSA nrarr -->
<!ENTITY nshortmid        "&#x02224;" ><!--alias ISOAMSN nsmid -->
<!ENTITY nshortparallel   "&#x02226;" ><!--alias ISOAMSN nspar -->
<!ENTITY nsimeq           "&#x02244;" ><!--alias ISOAMSN nsime -->
<!ENTITY nsubset          "&#x02282;&#x020D2;" ><!--alias ISOAMSN vnsub -->
<!ENTITY nsubseteq        "&#x02288;" ><!--alias ISOAMSN nsube -->
<!ENTITY nsubseteqq       "&#x02AC5;&#x00338;" ><!--alias ISOAMSN nsubE -->
<!ENTITY nsucc            "&#x02281;" ><!--alias ISOAMSN nsc -->
<!ENTITY nsucceq          "&#x02AB0;&#x00338;" ><!--alias ISOAMSN nsce -->
<!ENTITY nsupset          "&#x02283;&#x020D2;" ><!--alias ISOAMSN vnsup -->
<!ENTITY nsupseteq        "&#x02289;" ><!--alias ISOAMSN nsupe -->
<!ENTITY nsupseteqq       "&#x02AC6;&#x00338;" ><!--alias ISOAMSN nsupE -->
<!ENTITY ntriangleleft    "&#x022EA;" ><!--alias ISOAMSN nltri -->
<!ENTITY ntrianglelefteq  "&#x022EC;" ><!--alias ISOAMSN nltrie -->
<!ENTITY ntriangleright   "&#x022EB;" ><!--alias ISOAMSN nrtri -->
<!ENTITY ntrianglerighteq "&#x022ED;" ><!--alias ISOAMSN nrtrie -->
<!ENTITY nwarrow          "&#x02196;" ><!--alias ISOAMSA nwarr -->
<!ENTITY oint             "&#x0222E;" ><!--alias ISOTECH conint -->
<!ENTITY OpenCurlyDoubleQuote "&#x0201C;" ><!--alias ISONUM ldquo -->
<!ENTITY OpenCurlyQuote   "&#x02018;" ><!--alias ISONUM lsquo -->
<!ENTITY orderof          "&#x02134;" ><!--alias ISOTECH order -->
<!ENTITY parallel         "&#x02225;" ><!--alias ISOTECH par -->
<!ENTITY PartialD         "&#x02202;" ><!--alias ISOTECH part -->
<!ENTITY pitchfork        "&#x022D4;" ><!--alias ISOAMSR fork -->
<!ENTITY PlusMinus        "&#x000B1;" ><!--alias ISONUM plusmn -->
<!ENTITY pm               "&#x000B1;" ><!--alias ISONUM plusmn -->
<!ENTITY Poincareplane    "&#x0210C;" ><!--the Poincare upper half-plane -->
<!ENTITY prec             "&#x0227A;" ><!--alias ISOAMSR pr -->
<!ENTITY precapprox       "&#x02AB7;" ><!--alias ISOAMSR prap -->
<!ENTITY preccurlyeq      "&#x0227C;" ><!--alias ISOAMSR prcue -->
<!ENTITY Precedes         "&#x0227A;" ><!--alias ISOAMSR pr -->
<!ENTITY PrecedesEqual    "&#x02AAF;" ><!--alias ISOAMSR pre -->
<!ENTITY PrecedesSlantEqual "&#x0227C;" ><!--alias ISOAMSR prcue -->
<!ENTITY PrecedesTilde    "&#x0227E;" ><!--alias ISOAMSR prsim -->
<!ENTITY preceq           "&#x02AAF;" ><!--alias ISOAMSR pre -->
<!ENTITY precnapprox      "&#x02AB9;" ><!--alias ISOAMSN prnap -->
<!ENTITY precneqq         "&#x02AB5;" ><!--alias ISOAMSN prnE -->
<!ENTITY precnsim         "&#x022E8;" ><!--alias ISOAMSN prnsim -->
<!ENTITY precsim          "&#x0227E;" ><!--alias ISOAMSR prsim -->
<!ENTITY primes           "&#x02119;" ><!--the prime natural numbers -->
<!ENTITY Proportion       "&#x02237;" ><!--alias ISOAMSR Colon -->
<!ENTITY Proportional     "&#x0221D;" ><!--alias ISOTECH prop -->
<!ENTITY propto           "&#x0221D;" ><!--alias ISOTECH prop -->
<!ENTITY quaternions      "&#x0210D;" ><!--the ring (skew field) of quaternions -->
<!ENTITY questeq          "&#x0225F;" ><!--alias ISOAMSR equest -->
<!ENTITY rangle           "&#x0232A;" ><!--alias ISOTECH rang -->
<!ENTITY rationals        "&#x0211A;" ><!--the field of rational numbers -->
<!ENTITY rbrace           "&#x0007D;" ><!--alias ISONUM rcub -->
<!ENTITY rbrack           "&#x0005D;" ><!--alias ISONUM rsqb -->
<!ENTITY Re               "&#x0211C;" ><!--alias ISOAMSO real -->
<!ENTITY realine          "&#x0211B;" ><!--the geometric real line -->
<!ENTITY realpart         "&#x0211C;" ><!--alias ISOAMSO real -->
<!ENTITY reals            "&#x0211D;" ><!--the field of real numbers -->
<!ENTITY ReverseElement   "&#x0220B;" ><!--alias ISOTECH niv -->
<!ENTITY ReverseEquilibrium "&#x021CB;" ><!--alias ISOAMSA lrhar -->
<!ENTITY ReverseUpEquilibrium "&#x0296F;" ><!--alias ISOAMSA duhar -->
<!ENTITY RightAngleBracket "&#x0232A;" ><!--alias ISOTECH rang -->
<!ENTITY RightArrow       "&#x02192;" ><!--alias ISONUM rarr -->
<!ENTITY Rightarrow       "&#x021D2;" ><!--alias ISOTECH rArr -->
<!ENTITY rightarrow       "&#x02192;" ><!--alias ISONUM rarr -->
<!ENTITY RightArrowBar    "&#x021E5;" ><!--alias for rarrb -->
<!ENTITY RightArrowLeftArrow "&#x021C4;" ><!--alias ISOAMSA rlarr -->
<!ENTITY rightarrowtail   "&#x021A3;" ><!--alias ISOAMSA rarrtl -->
<!ENTITY RightCeiling     "&#x02309;" ><!--alias ISOAMSC rceil -->
<!ENTITY RightDoubleBracket "&#x0301B;" ><!--right double bracket delimiter -->
<!ENTITY RightDownVector  "&#x021C2;" ><!--alias ISOAMSA dharr -->
<!ENTITY RightFloor       "&#x0230B;" ><!--alias ISOAMSC rfloor -->
<!ENTITY rightharpoondown "&#x021C1;" ><!--alias ISOAMSA rhard -->
<!ENTITY rightharpoonup   "&#x021C0;" ><!--alias ISOAMSA rharu -->
<!ENTITY rightleftarrows  "&#x021C4;" ><!--alias ISOAMSA rlarr -->
<!ENTITY rightleftharpoons "&#x021CC;" ><!--alias ISOAMSA rlhar -->
<!ENTITY rightrightarrows "&#x021C9;" ><!--alias ISOAMSA rrarr -->
<!ENTITY rightsquigarrow  "&#x0219D;" ><!--alias ISOAMSA rarrw -->
<!ENTITY RightTee         "&#x022A2;" ><!--alias ISOAMSR vdash -->
<!ENTITY RightTeeArrow    "&#x021A6;" ><!--alias ISOAMSA map -->
<!ENTITY rightthreetimes  "&#x022CC;" ><!--alias ISOAMSB rthree -->
<!ENTITY RightTriangle    "&#x022B3;" ><!--alias ISOAMSR vrtri -->
<!ENTITY RightTriangleEqual "&#x022B5;" ><!--alias ISOAMSR rtrie -->
<!ENTITY RightUpVector    "&#x021BE;" ><!--alias ISOAMSA uharr -->
<!ENTITY RightVector      "&#x021C0;" ><!--alias ISOAMSA rharu -->
<!ENTITY risingdotseq     "&#x02253;" ><!--alias ISOAMSR erDot -->
<!ENTITY rmoustache       "&#x023B1;" ><!--alias ISOAMSC rmoust -->
<!ENTITY Rrightarrow      "&#x021DB;" ><!--alias ISOAMSA rAarr -->
<!ENTITY Rsh              "&#x021B1;" ><!--alias ISOAMSA rsh -->
<!ENTITY searrow          "&#x02198;" ><!--alias ISOAMSA searr -->
<!ENTITY setminus         "&#x02216;" ><!--alias ISOAMSB setmn -->
<!ENTITY ShortDownArrow   "&#x02193;" ><!--short down arrow -->
<!ENTITY ShortLeftArrow   "&#x02190;" ><!--alias ISOAMSA slarr -->
<!ENTITY shortmid         "&#x02223;" ><!--alias ISOAMSR smid -->
<!ENTITY shortparallel    "&#x02225;" ><!--alias ISOAMSR spar -->
<!ENTITY ShortRightArrow  "&#x02192;" ><!--alias ISOAMSA srarr -->
<!ENTITY ShortUpArrow     "&#x02191;" ><!--short up arrow  -->
<!ENTITY simeq            "&#x02243;" ><!--alias ISOTECH sime -->
<!ENTITY SmallCircle      "&#x02218;" ><!--alias ISOTECH compfn -->
<!ENTITY smallsetminus    "&#x02216;" ><!--alias ISOAMSB ssetmn -->
<!ENTITY spadesuit        "&#x02660;" ><!--ISOPUB    spades  -->
<!ENTITY Sqrt             "&#x0221A;" ><!--alias ISOTECH radic -->
<!ENTITY sqsubset         "&#x0228F;" ><!--alias ISOAMSR sqsub -->
<!ENTITY sqsubseteq       "&#x02291;" ><!--alias ISOAMSR sqsube -->
<!ENTITY sqsupset         "&#x02290;" ><!--alias ISOAMSR sqsup -->
<!ENTITY sqsupseteq       "&#x02292;" ><!--alias ISOAMSR sqsupe -->
<!ENTITY Square           "&#x025A1;" ><!--alias for square -->
<!ENTITY SquareIntersection "&#x02293;" ><!--alias ISOAMSB sqcap -->
<!ENTITY SquareSubset     "&#x0228F;" ><!--alias ISOAMSR sqsub -->
<!ENTITY SquareSubsetEqual "&#x02291;" ><!--alias ISOAMSR sqsube -->
<!ENTITY SquareSuperset   "&#x02290;" ><!--alias ISOAMSR sqsup -->
<!ENTITY SquareSupersetEqual "&#x02292;" ><!--alias ISOAMSR sqsupe -->
<!ENTITY SquareUnion      "&#x02294;" ><!--alias ISOAMSB sqcup -->
<!ENTITY Star             "&#x022C6;" ><!--alias ISOAMSB sstarf -->
<!ENTITY straightepsilon  "&#x003F5;" ><!--alias ISOGRK3 epsi -->
<!ENTITY straightphi      "&#x003D5;" ><!--alias ISOGRK3 phi -->
<!ENTITY Subset           "&#x022D0;" ><!--alias ISOAMSR Sub -->
<!ENTITY subset           "&#x02282;" ><!--alias ISOTECH sub -->
<!ENTITY subseteq         "&#x02286;" ><!--alias ISOTECH sube -->
<!ENTITY subseteqq        "&#x02AC5;" ><!--alias ISOAMSR subE -->
<!ENTITY SubsetEqual      "&#x02286;" ><!--alias ISOTECH sube -->
<!ENTITY subsetneq        "&#x0228A;" ><!--alias ISOAMSN subne -->
<!ENTITY subsetneqq       "&#x02ACB;" ><!--alias ISOAMSN subnE -->
<!ENTITY succ             "&#x0227B;" ><!--alias ISOAMSR sc -->
<!ENTITY succapprox       "&#x02AB8;" ><!--alias ISOAMSR scap -->
<!ENTITY succcurlyeq      "&#x0227D;" ><!--alias ISOAMSR sccue -->
<!ENTITY Succeeds         "&#x0227B;" ><!--alias ISOAMSR sc -->
<!ENTITY SucceedsEqual    "&#x02AB0;" ><!--alias ISOAMSR sce -->
<!ENTITY SucceedsSlantEqual "&#x0227D;" ><!--alias ISOAMSR sccue -->
<!ENTITY SucceedsTilde    "&#x0227F;" ><!--alias ISOAMSR scsim -->
<!ENTITY succeq           "&#x02AB0;" ><!--alias ISOAMSR sce -->
<!ENTITY succnapprox      "&#x02ABA;" ><!--alias ISOAMSN scnap -->
<!ENTITY succneqq         "&#x02AB6;" ><!--alias ISOAMSN scnE -->
<!ENTITY succnsim         "&#x022E9;" ><!--alias ISOAMSN scnsim -->
<!ENTITY succsim          "&#x0227F;" ><!--alias ISOAMSR scsim -->
<!ENTITY SuchThat         "&#x0220B;" ><!--ISOTECH  ni -->
<!ENTITY Sum              "&#x02211;" ><!--alias ISOAMSB sum -->
<!ENTITY Superset         "&#x02283;" ><!--alias ISOTECH sup -->
<!ENTITY SupersetEqual    "&#x02287;" ><!--alias ISOTECH supe -->
<!ENTITY Supset           "&#x022D1;" ><!--alias ISOAMSR Sup -->
<!ENTITY supset           "&#x02283;" ><!--alias ISOTECH sup -->
<!ENTITY supseteq         "&#x02287;" ><!--alias ISOTECH supe -->
<!ENTITY supseteqq        "&#x02AC6;" ><!--alias ISOAMSR supE -->
<!ENTITY supsetneq        "&#x0228B;" ><!--alias ISOAMSN supne -->
<!ENTITY supsetneqq       "&#x02ACC;" ><!--alias ISOAMSN supnE -->
<!ENTITY swarrow          "&#x02199;" ><!--alias ISOAMSA swarr -->
<!ENTITY Therefore        "&#x02234;" ><!--alias ISOTECH there4 -->
<!ENTITY therefore        "&#x02234;" ><!--alias ISOTECH there4 -->
<!ENTITY thickapprox      "&#x02248;" ><!--ISOAMSR   thkap  -->
<!ENTITY thicksim         "&#x0223C;" ><!--ISOAMSR   thksim -->
<!ENTITY ThinSpace        "&#x02009;" ><!--space of width 3/18 em alias ISOPUB thinsp -->
<!ENTITY Tilde            "&#x0223C;" ><!--alias ISOTECH sim -->
<!ENTITY TildeEqual       "&#x02243;" ><!--alias ISOTECH sime -->
<!ENTITY TildeFullEqual   "&#x02245;" ><!--alias ISOTECH cong -->
<!ENTITY TildeTilde       "&#x02248;" ><!--alias ISOTECH ap -->
<!ENTITY toea             "&#x02928;" ><!--alias ISOAMSA nesear -->
<!ENTITY tosa             "&#x02929;" ><!--alias ISOAMSA seswar -->
<!ENTITY triangle         "&#x025B5;" ><!--alias ISOPUB utri -->
<!ENTITY triangledown     "&#x025BF;" ><!--alias ISOPUB dtri -->
<!ENTITY triangleleft     "&#x025C3;" ><!--alias ISOPUB ltri -->
<!ENTITY trianglelefteq   "&#x022B4;" ><!--alias ISOAMSR ltrie -->
<!ENTITY triangleq        "&#x0225C;" ><!--alias ISOAMSR trie -->
<!ENTITY triangleright    "&#x025B9;" ><!--alias ISOPUB rtri -->
<!ENTITY trianglerighteq  "&#x022B5;" ><!--alias ISOAMSR rtrie -->
<!ENTITY TripleDot        "&#x020DB;" ><!--alias ISOTECH tdot -->
<!ENTITY twoheadleftarrow "&#x0219E;" ><!--alias ISOAMSA Larr -->
<!ENTITY twoheadrightarrow "&#x021A0;" ><!--alias ISOAMSA Rarr -->
<!ENTITY ulcorner         "&#x0231C;" ><!--alias ISOAMSC ulcorn -->
<!ENTITY Union            "&#x022C3;" ><!--alias ISOAMSB xcup -->
<!ENTITY UnionPlus        "&#x0228E;" ><!--alias ISOAMSB uplus -->
<!ENTITY UpArrow          "&#x02191;" ><!--alias ISONUM uarr -->
<!ENTITY Uparrow          "&#x021D1;" ><!--alias ISOAMSA uArr -->
<!ENTITY uparrow          "&#x02191;" ><!--alias ISONUM uarr -->
<!ENTITY UpArrowDownArrow "&#x021C5;" ><!--alias ISOAMSA udarr -->
<!ENTITY UpDownArrow      "&#x02195;" ><!--alias ISOAMSA varr -->
<!ENTITY Updownarrow      "&#x021D5;" ><!--alias ISOAMSA vArr -->
<!ENTITY updownarrow      "&#x02195;" ><!--alias ISOAMSA varr -->
<!ENTITY UpEquilibrium    "&#x0296E;" ><!--alias ISOAMSA udhar -->
<!ENTITY upharpoonleft    "&#x021BF;" ><!--alias ISOAMSA uharl -->
<!ENTITY upharpoonright   "&#x021BE;" ><!--alias ISOAMSA uharr -->
<!ENTITY UpperLeftArrow   "&#x02196;" ><!--alias ISOAMSA nwarr -->
<!ENTITY UpperRightArrow  "&#x02197;" ><!--alias ISOAMSA nearr -->
<!ENTITY upsilon          "&#x003C5;" ><!--alias ISOGRK3 upsi -->
<!ENTITY UpTee            "&#x022A5;" ><!--alias ISOTECH perp -->
<!ENTITY UpTeeArrow       "&#x021A5;" ><!--Alias mapstoup -->
<!ENTITY upuparrows       "&#x021C8;" ><!--alias ISOAMSA uuarr -->
<!ENTITY urcorner         "&#x0231D;" ><!--alias ISOAMSC urcorn -->
<!ENTITY varepsilon       "&#x003B5;" ><!--alias ISOGRK3 epsiv -->
<!ENTITY varkappa         "&#x003F0;" ><!--alias ISOGRK3 kappav -->
<!ENTITY varnothing       "&#x02205;" ><!--alias ISOAMSO emptyv -->
<!ENTITY varphi           "&#x003C6;" ><!--alias ISOGRK3 phiv -->
<!ENTITY varpi            "&#x003D6;" ><!--alias ISOGRK3 piv -->
<!ENTITY varpropto        "&#x0221D;" ><!--alias ISOAMSR vprop -->
<!ENTITY varrho           "&#x003F1;" ><!--alias ISOGRK3 rhov -->
<!ENTITY varsigma         "&#x003C2;" ><!--alias ISOGRK3 sigmav -->
<!ENTITY varsubsetneq     "&#x0228A;&#x0FE00;" ><!--alias ISOAMSN vsubne -->
<!ENTITY varsubsetneqq    "&#x02ACB;&#x0FE00;" ><!--alias ISOAMSN vsubnE -->
<!ENTITY varsupsetneq     "&#x0228B;&#x0FE00;" ><!--alias ISOAMSN vsupne -->
<!ENTITY varsupsetneqq    "&#x02ACC;&#x0FE00;" ><!--alias ISOAMSN vsupnE -->
<!ENTITY vartheta         "&#x003D1;" ><!--alias ISOGRK3 thetav -->
<!ENTITY vartriangleleft  "&#x022B2;" ><!--alias ISOAMSR vltri -->
<!ENTITY vartriangleright "&#x022B3;" ><!--alias ISOAMSR vrtri -->
<!ENTITY Vee              "&#x022C1;" ><!--alias ISOAMSB xvee -->
<!ENTITY vee              "&#x02228;" ><!--alias ISOTECH or -->
<!ENTITY Vert             "&#x02016;" ><!--alias ISOTECH Verbar -->
<!ENTITY vert             "&#x0007C;" ><!--alias ISONUM verbar -->
<!ENTITY VerticalBar      "&#x02223;" ><!--alias ISOAMSR mid -->
<!ENTITY VerticalTilde    "&#x02240;" ><!--alias ISOAMSB wreath -->
<!ENTITY VeryThinSpace    "&#x0200A;" ><!--space of width 1/18 em alias ISOPUB hairsp -->
<!ENTITY Wedge            "&#x022C0;" ><!--alias ISOAMSB xwedge -->
<!ENTITY wedge            "&#x02227;" ><!--alias ISOTECH and -->
<!ENTITY wp               "&#x02118;" ><!--alias ISOAMSO weierp -->
<!ENTITY wr               "&#x02240;" ><!--alias ISOAMSB wreath -->
<!ENTITY zeetrf           "&#x02128;" ><!--zee transform -->

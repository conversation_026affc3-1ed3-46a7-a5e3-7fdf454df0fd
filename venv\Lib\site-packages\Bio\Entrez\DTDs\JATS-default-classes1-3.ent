<!-- ============================================================= -->
<!--  MODULE:    Default Element Classes Module                    -->
<!--  VERSION:   ANSI/NISO JATS Version 1.3 (Z39.96-2021)          -->
<!--  DATE:      June 2021                                         -->
<!--                                                               -->
<!-- ============================================================= -->

<!-- ============================================================= -->
<!--                    PUBLIC DOCUMENT TYPE DEFINITION            -->
<!--                        TYPICAL INVOCATION                     -->
<!--
"-//NLM//DTD JATS (Z39.96) Default Element Classes Module v1.3 20210610//EN"
Delivered as file "JATS-default-classes1-3.ent"                    -->
<!-- ============================================================= -->


<!-- ============================================================= -->
<!-- SYSTEM:     JATS DTD Suite                                    -->
<!--                                                               -->
<!-- PURPOSE:    To declare the Parameter Entities (PEs) used to   -->
<!--             define the default element classes. Classes are   -->
<!--             OR-groups of elements that are defined together   -->
<!--             to be used in mixes and in Element Declarations.  -->
<!--                                                               -->
<!--             Note: Since PEs must be declared before they      -->
<!--             are used, this module must be called before all   -->
<!--             content modules that declare elements, and after  -->
<!--             the class customization module (if any).          -->
<!--                                                               -->
<!-- CONTAINS:   PEs that define the element classes to be used    -->
<!--             in the JATS DTD Suite modules.                    -->
<!--                                                               -->
<!-- TAG SET SPONSOR                                               -->
<!--             National Center for Biotechnology                 -->
<!--                Information (NCBI)                             -->
<!--             National Library of Medicine (NLM)                -->
<!--                                                               -->
<!-- CREATED FOR:                                                  -->
<!--             This module was created for the JATS DTD Suite.   -->
<!--             Digital archives and publishers may use the       -->
<!--             DTD as is for markup of journal literature or     -->
<!--             related material for archiving and transferring   -->
<!--             such material between archives or create a        -->
<!--             custom XML DTD from the Suite for                 -->
<!--             these purposes.                                   -->
<!--                                                               -->
<!--             This DTD is in the public domain. An organization -->
<!--             that wishes to create its own DTD from the suite  -->
<!--             may do so without permission from NLM.            -->
<!--                                                               -->
<!--             The suite has been set up to be extended using a  -->
<!--             new DTD file and a new DTD-specific customization -->
<!--             module to redefine the many Parameter Entities.   -->
<!--             Do not modify the suite directly or redistribute  -->
<!--             modified versions of the suite.                   -->
<!--                                                               -->
<!--             In the interest of maintaining consistency and    -->
<!--             clarity for potential users, NLM requests:        -->
<!--                                                               -->
<!--             1. If you create a DTD from the JATS DTD Suite    -->
<!--                and intend to stay compatible with the suite,  -->
<!--                then please include the following statement    -->
<!--                as a comment in all of your DTD modules:       -->
<!--                   "Created from, and fully compatible with,   -->
<!--                    the ANSI/NISO Z39.96 Journal Article Tag   -->
<!--                    Suite (JATS)."                             -->
<!--                                                               -->
<!--             2. If you alter one or more modules of the suite, -->
<!--                then please rename your version and all its    -->
<!--                modules to avoid any confusion with the        -->
<!--                original suite. Also, please include the       -->
<!--                following statement as a comment in all your   -->
<!--                DTD modules:                                   -->
<!--                   "Based in part on, but not fully compatible -->
<!--                    with, the ANSI/NISO Z39.96 Journal Article -->
<!--                    Tag Suite (JATS)."                         -->
<!--                                                               -->
<!-- ORIGINAL CREATION DATE:                                       -->
<!--             August 2004                                       -->
<!--                                                               -->
<!-- CREATED BY: Mulberry Technologies, Inc. for the NISO Z39.96   -->
<!--             Working Group. Mulberry Technologies was          -->
<!--             supported by National Center for Biotechnology    -->
<!--             Information (NCBI), a center of the US National   -->
<!--             Library of Medicine (NLM).                        -->
<!--                                                               -->
<!--             This module is part of the JATS DTD Suite. The    -->
<!--             Suite is a continuation of work done by NCBI,     -->
<!--             Mulberry Technologies, and Inera, Inc. on the NLM -->
<!--             Journal Archiving and Interchange DTD Suite, which-->
<!--             was originally released in December, 2002.        -->
<!--                                                               -->
<!--             NLM thanks the Harvard University Libraries, both -->
<!--             for proposing that a draft archiving NLM DTD for  -->
<!--             life sciences journals be extended to accommodate -->
<!--             journals in all disciplines and for sponsoring    -->
<!--             Inera Inc.'s collaboration with other DTD         -->
<!--             authors in completing NLM Version 1.0. The        -->
<!--             Andrew W. Mellon Foundation provided support for  -->
<!--             these important contributions.                    -->
<!--                                                               -->
<!--             Suggestions for refinements and enhancements to   -->
<!--             this DTD should be sent in email to:              -->
<!--                 <EMAIL>                         -->
<!-- ============================================================= -->


<!-- ============================================================= -->
<!--                    DTD VERSION/CHANGE HISTORY                 -->
<!-- ============================================================= -->
<!--
Version  Reason/Occasion                   (who) vx.x (yyyy-mm-dd)
   ==============================================================
     JATS Version 1.3 (ANSI/NISO Z39.96-2021)
                                  (DAL/BTU) v1.3 (2021-06-10)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by both the NISO and ANSI memberships 
     to be changed. This version of the NISO JATS was approved 
     by ANSI vote on June 10, 2021, so it supersedes all Committee 
     Drafts as well as the draft version JATS 1.3 2021 (the version
     submitted for vote), becoming NISO JATS v1.3 20210610 
     (ANSI/NISO Z39.96-2019).

     The file names and formal public identifiers have been 
     changed in the modules and the catalogs.

 46. JATS 'v1.3d2 20201130' becomes 'v1.3 20210610' 
     (final ANSI vote date)
     'ANSI/NISO Z39.96-2019' becomes 'ANSI/NISO Z39.96-2021'

  ==============================================================
     JATS Version 1.3d2 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.3d2 (2020-11-30)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by the NISO and ANSI memberships 
     to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     of the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting; it is for Standing Committee review and
     testing. The formal public identifiers have been changed 
     in the modules and the catalogs.
      
 45. NEW DATE-ONLY CLASS - Added new date only class, since
     date.class grew to include <string-date>, and there are 
     times when ,string-date> is not wanted.
 
 44. STRING DATE - Added <string-date> to all citations
     (mixed-, element-, product, related-article, related-object)
     by adding it to %references.class;.
 
 43. JATS "v1.3d2 20201130" becomes 'v1.3 20210610' (second 
     committee draft following the final ANSI/NISO 1.2 vote)

   ==============================================================
     JATS Version 1.3d1 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.3.d1 (2019-08-31)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by the NISO and ANSI memberships 
     to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     of the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
      
 42. NEW BLOCK ALTERNATIVES CLASS - Added new display class: 
     block-alternatives class to hold the elements that may be
     used inside the new <block-alternatives> element.
     
 41. QUESTIONS AND ANSWERS - Added the BITS question and answer
     models to JATS. 
     
      - The following elements were added to the block display 
        class Parameter Entity: 
         - answer and answer-set
         - explanation
         - question, question-wrap, question-wrap-group
         
      - The block display class noalt received the same elements
      
      - A new PE, block display no explanation, was added
        for use in <answer> and <option> elements.
        
       - New PE were added for modeling questions/answers:
          - question-answer.class
          - question-wrap.class
          - option.class

 40. SUBJECT GROUP CLASS - Made the element <subj-group> into its own
     class ( subj-group.class ) for use in models.

 39. JATS "1.2" becomes "v1.3d1 20190831" (first committee
     draft following the final ANSI/NISO vote) 

   ==============================================================
     JATS Version 1.2 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.2     (2019-02-08)
    
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the both the NISO and ANSI membership 
     to be changed. This version of the NISO JATS was approved 
     by ANSI vote on Feb 08, 2019, so it supersedes all Committee 
     Drafts as well as the draft version JATS 1.2 2018 (the version
     submitted for vote), becoming NISO JATS v1.2 20190208 
     (ANSI/NISO Z39.96-2021).
  
 38. JATS "1.2" becomes "v1.2 20190208" (final ANSI vote date)
     "ANSI/NISO Z39.96-2015" becomes "ANSI/NISO Z39.96-2019"
     
   ==============================================================
     JATS Version 1.2 (ANSI/NISO Z39.96-2018)
                                   (DAL/BTU) v1.2     (2018-11-30)
    
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the both the NISO and ANSI membership 
     to be changed. This version of the NISO JATS was approved 
     by ANSI and NISO vote, so it supersedes all Committee Drafts
     and becoming NISO JATS 1.2 (ANSI/NISO Z39.96-2018).
  
 37. 
     JATS "1.2d2" and "v1.2d2 20180401" became
     JATS "1.2" and "v1.2 20181130" 

    =============================================================
     JATS Version 1.2d2              (DAL/BTU) v1.2d2  (2018-04-01)

     JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
     BITS is under continuous maintenance, and is modified at the
     discretion of the working group.
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.

 36. INLINE-INDEX - Added the elements <index-term> and
     <index-term-range-end> to phrase class to add the ability
     to tag inline (embedded) index terms. 

 35. INLINE-MEDIA - <inline-media> added to all the classes that 
     contained <inline-graphic>

 34. BITS "2.0" and "v2.0 20151225" remain unchanged
   
     JATS "1.2d1" and "v1.2d1 20171231" became
     JATS "1.2d2" and "v1.2d2 20180401". 

     No module names were changed.
 
    =============================================================
     JATS Version 1.2d1 (ANSI/NISO Z39.96-2015)
                                   (DAL/BTU) v1.2d1  (2017-12-31)
 
     NISO JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
  
 33. JATS "1.2d1" and "v1.2d1 20170631" became
     JATS "1.2d1" and "v1.2d1 20171231". 

    =============================================================
     JATS Version 1.2d1              (DAL/BTU) v1.2d1  (2017-06-31)

     NISO JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
 
 32. NEW CLASS FOR TITLES/SOURCES - Made the element <abbrev>
     into its own class, so it can be added to title elements 
     without adding milestones.

 31. INSIDE A TABLE: Added <disp-formula> as a sibling to
     <graphic>, <list>, etc. as the content of a <table-wrap>
     that does not contain a <table>. Added <disp-formula> to 
     simple-intable-display.class.

 30. JATS became version "1.2d1" and "v1.2d1 20170631" 

     =============================================================
     JATS Version 1.1                (DAL/BTU) v1.1  (2015-12-15)

     JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
     JATS 1.1 was approved in late 2015, so the formal public 
     identifiers were changed in the modules and the catalogs.
     No model or attribute changes were made at this time. 
 
 29. JATS became version "1.1" and "v1.1 20151215"
     BITS remained version "2.0" but becomes "v2.0 20151225"

     =============================================================
     JATS Version 1.1d3             (DAL/BTU) v1.1d3  (2015-03-01)
   
     The changes in this release are in response to NISO 
     License and Indicators Recommended Practice.

     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the NISO membership to be changed. This
     Committee Draft 1.1d3 will be sent to the NISO voting
     membership, to become (if approved) NISO JATS 1.1.
     
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee.

 28. ALI (NISO Access and License Indicators)
     - Added <ali:license_ref> to license-p.class, as 
       alternatives inside <license>
     - Created a new class: license.class to hold the new ALI
       element <ali:free_to_read>, as an alternative to
       <license> inside <permissions>. 

 27. JATS became version "1.1d3" and "v1.1d3 20150301"

     =============================================================
     JATS Version 1.1d2              (DAL/BTU) v1.1d2 (2014-09-30)
   
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the NISO membership to be changed. This
     Committee Draft 1.1d2 will be sent to the NISO voting
     membership, to become (if approved) NISO JATS 1.1.
     
     This catalog represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee.

 26. ADDED TO REFERENCES - Added the following elements to 
     %references.class so that it would be allowed inside all 
     citation types, product, et al.
      - conf-acronym
      - data-title (for citing datasets and parts of datasets)
      - version (for data and software, similar to edition)

 25. STATEMENT - Added new statement.class, so that statements
     can be recursive.

 24. JATS became version "1.1d2" and "v1.1d2 20140930//EN"

 23. ADDRESS ELEMENTS IN NEW CLASSES
      - Added 3 new elements: <city>, <state>, <postal-code>
          - address.class
          - address-line.class
        which adds then to: address, addr-line, add, collab, 
        conf-loc, corresp, publisher-loc      
   
     =============================================================
     NISO JATS Version 1.1d1           (DAL/BTU) v1.1 (2013-11-15)
   
     ANSI/NISO Z39.96-2012 (pre-release for V1.0 BITS; Version 1.1d1) 

     NISO JATS Standing Committee met and answered the requests and
     suggestions from the NISO request forms.

     Details concerning ANSI/NISO Z39.96-2012 JATS-based DTDs,  
     XSDs, RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.3d2/

 22. NEW <era> ELEMENT
     Added <era> to date-parts.class

 21. NEW CLASSES
     - def-item.class
     - def-list.class
     - list-item.class
     - ref.class for use in <ref-list>
     - unstructured-kwd-group.class for use in Green

 20. NEW CODE ELEMENT- Added <code> to
      - alternatives-display.class
      - block-display.class
      - block-display-noalt.class
      - floats-display.class
      - inside-chem-struct-wrap.class      
      - simple-display.class
      - simple-display-noalt.class
      - simple-intable-display.class

 19. INSTITUTION-WRAP ADDED
     Added the element <institution-wrap> to the following classes:
      - address.class
      - address-line.class
      - recipient-name.class
      - references.class

 18. RUBY
     - Added <ruby> to emphasis class
     - Added a face-markup class (all the emphasis class except 
         <ruby>) for use inside <ruby>

 17. ADDED NEW CLASSES (for new locations for the elements)
     - abstract.class
     - kwd-group.class
     - institution.class
     - institution-wrap.class
     
     =============================================================
     NISO JATS Version 1.0             (DAL/BTU) v1.0 (2012-xx-xx)
   
     ANSI/NISO Z39.96-2012 (Version 1.0) 

     Details concerning ANSI/NISO Z39.96-2012 JATS-based DTDs,  
     XSDs, RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.3d2/
   
 16. CITATION ALTERNATIVES - Added a new element 
     <citation-alternatives> to the citation.class. The element
     will hold, for example, a single citation in multiple
     languages or a single citation tagged as a <mixed-citation>
     complete with punctuation and spacing and also as an
     <element-citation> with all punctuation and spacing removed.

 15. CONTRIBUTOR ID CLASS - Created a new class "contrib-id.class"
     to hold identifiers for people such as contributors such
     as <contrib> and <principal-investigator>s. This will hold
     publishers-specific IDs, ORCIDs, and JST and NII identifiers.

 14. NESTED KEYWORD CLASS - Created a new class "nested-kwd.class"
     to hold the kinds of keywords that can be used inside
     a nested keyword structure.
     
 13. COLLAB CLASS - Created a new class "collab.class" to hold 
     only the element <collab>, for use in the element
     <collab-alternatives>.

 12. COLLABORATION ALTERNATIVES - Added <collab-alternatives> to
     the name.class and -references.class to allow the
     <collab-alternatives> element to occur anywhere <collab> 
     was already used.
   
 11. STANDARDS CLASS - Created a new class "std.class" to hold all
     the elements inside the <std> element within citations.
   
 10. REFERENCES CLASS - Added <issn-l> to the class.
   
  9. NOTHING BUT PARA CLASS - Added a new %nothing-but-para.class;
     to get rid of PE clashes when the entity %just-para.class; needed
     to be expanded to include other elements. The nothing-but
     parameter entity contains <p> and only <p> and should never
     be changed in a customization. The infelicitously-named PE
     %just-para.class; can be modified for a customization to include
     other elements as well.

     =============================================================
     Version 0.4                       (DAL/BTU) v0.4 (2011-01-31)
   
     This Tag Set is in the process of becoming a NISO standard.
     The version numbers are starting over from 0.4", as a Trial
     Use Draft, to be made into "Version 1.0" when the Tag Suite 
     becomes a NISO standard. Thus, the version number that would
     have been "NLM Version 3.1 Draft" has become NISO JATS
     "Version 0.4".

     Details on NISO Trial Use Draft Version 0.4 are available at
           http://jats.nlm.nih.gov/JATS-0.4.
   
  8. Updated the DTD-version attribute to "0.4" 
   
  7. Updated the public identifier to "v0.4 20110131", 
     modified the formal public identifier to include "JATS (Z39.96)",
     and the filename as delivered to include "JATS" and the
     new version number "0".
           http://jats.nlm.nih.gov/0.4.

  6. NEW CLASS FOR just <alt-text> %small-access.class;
     Since not everything needs a <long-desc>

  5. ALTERNATIVES - Added <private-char> so that the alternatives
     wrapper may be used to hold alternative characters, some as
     <private-char>, some as <textual-form>, some as <graphic>,
     etc.

  4. AFFILIATION ALTERNATIVES - Added the element <aff-alternatives>
     to hold multiple <aff>s that are representations of a single
     affiliation, for example, the name of an institution in two
     languages or two scripts.
      -  contrib-info.class
      -  person-group-info.class
      -  Made a new class %aff-alternatives.class to hold just
         <aff> and <aff-alternatives> NEW PE

  3. NAME ALTERNATIVES - Added a new container element to hold
     alternate versions of a person's name, for example:
       -  a Roman alphabet transliteration, the Japanese characters,
          and the Japanese with Ruby annotations or
       -  the full peerage title of a person as well as a name
          that is just surname and given names
       -  a name and a sort version of the name that transliterates
          the diacritics (such as cedillas and umlauts) to the same
          character with no diacritic.
     a. This structure was added to name.class PE
     b. This structure was added to %investigator-name.class;
     c. This structure was added to %recipient-name.class;
     d. This structure was added to %references.class;

  2. ROLE - Added <role> to %person-group-info.class; thereby
     adding it to: <person-group> and <sig-block>

     =============================================================
     Version 3.0                       (DAL/BTU) v3.0 (2007-10-31)

     Version 3.0 is the first non-backward-compatible release.
     In addition to the usual incremental changes, some
     elements and attributes have been renamed and/or remodeled
     to better meet user needs and to increase tag set consistency.
     All module change histories are available through the Tag Suite
     web site at http://dtd.nlm.nih.gov.

     Details on version 3.0 are available at
           http://dtd.nlm.nih.gov/3.0.

  1. Updated public identifier to "v3.0 20071031//EN"              -->

<!-- ============================================================= -->
<!-- Details concerning 
     ANSI/NISO Z39.96-2015 JATS-based DTDs, XSDs, 
     RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.1/                            -->

<!-- ============================================================= -->
<!--                    CLASSES FOR COMMON ELEMENTS (%common.ent;) -->
<!-- ============================================================= -->


<!--                    ADDRESS CLASS ELEMENTS                     -->
<!--                    Potential element components of an address;
                        not a proper class                         -->
<!ENTITY % address.class
                        "addr-line | city | country | fax |
                         institution | institution-wrap | phone |
                         postal-code | state"                        >


<!--                    ADDRESS LINE CLASS ELEMENTS                -->
<!--                    Potential element components of an address;
                        line not a proper class                    -->
<!ENTITY % address-line.class
                        "city | country | fax | institution | 
                         institution-wrap | phone |
                         postal-code | state"                        >


<!--                    CITATION CLASS ELEMENTS                    -->
<!--                    Reference to an external document, as used
                        within, for example, the text of a
                        paragraph                                  -->
<!ENTITY % citation.class
                        "citation-alternatives | element-citation | 
                         mixed-citation"                             >


<!--                    CITATION MINUS ALTERNATIVES CLASS ELEMENTS -->
<!--                    All the citation elements except the
                        <citation-alternatives> element.           -->
<!ENTITY % citation-minus-alt.class
                        "element-citation | mixed-citation | 
                         nlm-citation"                               >


<!--                    DEFINITION CLASS ELEMENTS                  -->
<!--                    Definitions and other elements to match
                        with terms and abbreviations               -->
<!ENTITY % def.class    "def"                                        >


<!--                    DEGREE CLASS                               -->
<!--                    The academic or professional degrees that
                        accompany a person's name                  -->
<!ENTITY % degree.class "degrees"                                    >


<!--                    IDENTIFIER CLASS ELEMENTS                  -->
<!--                    DOIs and other identifiers are used by
                        publishers at many levels, for example for
                        an <abstract> or a <fig>.                  -->
<!ENTITY % id.class     "object-id"                                  >


<!--                    LABEL CLASS                                -->
<!--                    The label element, used to hold the number
                        or character of a labeled object such as a
                        table or footnote                          -->
<!ENTITY % label.class  "label"                                      >


<!--                    NOTE CLASS ELEMENTS                        -->
<!--                    A note may appear at the end of an Article
                        or at the end of a Table.  For example, a
                        typical end-of-article note is a
                        "Note in Proof".                           -->
<!ENTITY % note.class   "note"                                       >


<!-- ============================================================= -->
<!--                    PERSON NAMING ELEMENTS (%common.ent;)      -->
<!-- ============================================================= -->


<!--                    COLLAB CLASS                               -->
<!--                    A class to hold the <collab> element, so that
                        more than on collaboration can be named 
                        inside <collab-alternatives>.              -->
<!ENTITY % collab.class "collab"                                     >


<!--                    INVESTIGATOR NAMES CLASS                   -->
<!--                    The elements used to name the personal names
                        for principal investigators.               -->
<!ENTITY % investigator-name.class
                        "name | name-alternatives | string-name"     >


<!--                    NAMES ALTERNATIVES CLASS                   -->
<!--                    Elements that may contain the name of a
                        person. These elements act as alternatives
                        inside the <name-alternatives> wrapper, to
                        allow multiple versions of one person's name
                        to be included. These variants are
                        differentiated by attributes: @xml:lang for
                        language, region, language variant, etc. and
                        @specific-use for indexing names,
                        search names, etc.                         -->
<!ENTITY % name-alternatives.class
                        "name | string-name"                         >


<!--                    NAMES CLASS                                -->
<!--                    The elements used to name the personal names
                        for individuals and the collaboration names
                        for groups.                                -->
<!ENTITY % name.class   "anonymous | collab | collab-alternatives |
                         name | name-alternatives | string-name"     >
                         


<!--                    PERSONAL NAMES CLASS                       -->
<!--                    The element components of a person's name,
                        for the name of a contributor              -->
<!ENTITY % person-name.class
                        "degrees | given-names | prefix | surname |
                         suffix"                                     >


<!--                    STRING NAME CLASS                          -->
<!--                    The element <string-name> is most useful
                        inside citations, but some tag sets restrict
                        its use in metadata. A one-element class.  -->
<!ENTITY % string-name.class
                        "string-name"                                >


<!--                    RECIPIENT NAMES CLASS                      -->
<!--                    The elements used to name the personal names
                        or corporate names (such as Labs) for
                        funded award recipients                    -->
<!ENTITY % recipient-name.class
                        "name | name-alternatives | institution |
                         institution-wrap | string-name"             >


<!-- ============================================================= -->
<!--                    ARTICLE METADATA CONTRIBUTOR CLASSES;      -->
<!-- ============================================================= -->


<!--                    CONTRIBUTOR CLASS                          -->
<!--                    Sometimes only the <contrib> element needs to
                        be added to a model. Used mostly in the
                        model for <contrib-group>.                 -->
<!ENTITY % contrib.class
                        "contrib"                                    >


<!--                    CONTRIBUTOR GROUP CLASS                    -->
<!--                    Sometimes only the <contrib-group> element
                        needs to be added to a model.              -->
<!ENTITY % contrib-group.class
                        "contrib-group"                              >


<!--                    CONTRIBUTOR IDENTIFIER CLASS               -->
<!--                    Names the class to hold unique
                        person identifiers, such as an ORCID or
                        a trusted publisher identifier.            -->
<!ENTITY % contrib-id.class
                        "contrib-id"                                 >


<!--                    CONTRIBUTOR INFORMATION CLASS              -->
<!--                    Metadata about a contributor               -->
<!ENTITY % contrib-info.class
                        "address | aff | aff-alternatives |
                         author-comment | bio | email |  etal |
                         ext-link | on-behalf-of | role | uri | xref">


<!--                    CONTRIBUTOR INFORMATION FOR REFERENCES     -->
<!--                    The additions and alternatives to a person's
                        name that may be used inside the element
                        <person-group>                             -->
<!ENTITY % person-group-info.class
                        "aff | aff-alternatives | etal | role"       >


<!-- ============================================================= -->
<!--                    ARTICLE METADATA CLASSES %articlemeta.ent; -->
<!-- ============================================================= -->


<!--                    ARTICLE IDENTIFIER CLASS ELEMENTS          -->
<!--                    Collected article identifiers for use in
                        various models.                            -->
<!ENTITY % article-identifier.class
                        "article-id | issn | issn-l | isbn"          >


<!--                    ARTICLE VERSION CLASS ELEMENTS             -->
<!--                    Making sure that wherever <article-version>
                        is allowed, <article-version-alternatives>
                        is also allowed.                           -->
<!ENTITY % article-version.class
                        "article-version |
                         article-version-alternatives"               > 


<!--                    ABSTRACT CLASS ELEMENTS                    -->
<!--                    Potential element components of an address;
                        not a proper class                         -->
<!ENTITY % abstract.class
                        "abstract"                                   >


<!--                    AFFILIATIONS ALTERNATIVES CLASS            -->
<!--                    The <aff> and <aff-alternatives> elements only
                                                                   -->
<!ENTITY % aff-alternatives.class
                        "aff | aff-alternatives"                     >


<!--                    CONFERENCE CLASS                           -->
<!--                    The element components of the description
                        of a conference; not a proper class        -->
<!ENTITY % conference.class
                        "conf-date | conf-name | conf-num |
                         conf-loc | conf-sponsor | conf-theme |
                         conf-acronym | string-conf"                 >


<!--                    CORRESPONDING AUTHOR CLASS                 -->
<!--                    Elements associated with the corresponding
                        author                                     -->
<!ENTITY % corresp.class
                        "corresp"                                    >


<!--                    DATE CLASS ELEMENTS                        -->
<!--                    Dates and other matters of history         -->
<!ENTITY % date.class   "date | string-date"                         >


<!--                    DATE-ONLY CLASS ELEMENTS                   -->
<!--                    Dates and other matters of history, when
                        <string-date> is expressly not wanted.     -->
<!ENTITY % date-only.class   
                        "date"                                       >


<!--                    DATE PARTS CLASS ELEMENTS                  -->
<!--                    Components of date-style elements          -->
<!ENTITY % date-parts.class
                        "day | era | month | season | year"          >


<!--                    EVENT CLASS ELEMENTS                       -->
<!--                    Dates and other matters of history         -->
<!ENTITY % event.class  "event"                                      >


<!--                    INSTITUTION CLASS ELEMENTS                 -->
<!--                    To hold an <institution> and its
                        identifiers, so you always can use both
                        together.                                  -->
<!ENTITY % institution.class
                        "institution | institution-id"               >


<!--                    INSTITUTION CLASS ELEMENTS                 -->
<!--                    To allow both of the institution elements to 
                        be used anywhere one of them might be needed.
                                                                   -->
<!ENTITY % institution-wrap.class
                        "institution | institution-wrap"             >


<!--                    JOURNAL IDENTIFIER CLASS ELEMENTS          -->
<!--                    The <journal-id> element and any synonyms.
                        Created for use inside <related-article>.  -->
<!ENTITY % journal-id.class
                        "journal-id"                                 >


<!--                    KEYWORD CLASS ELEMENTS                     -->
<!--                    Keywords and any keyword-synonyms          -->
<!ENTITY % kwd.class    "kwd | compound-kwd | nested-kwd"            >


<!--                    KEYWORD GROUP CLASS ELEMENTS               -->
<!--                    The element <kwd-group> for use in models. -->
<!ENTITY % kwd-group.class    
                        "kwd-group"                                  >


<!--                    LICENSE CLASS ELEMENTS                     -->
<!--                    The alternatives to <license> that may live
                        inside <permissions>.                      -->
<!ENTITY % license.class    
                        "ali:free_to_read | license"                 >


<!--                    LICENSE PARAGRAPH CLASS ELEMENTS           -->
<!--                    To hold the paragraphs and license
                        references that may be inside the element
                        <license>.                                 -->
<!ENTITY % license-p.class
                        "ali:license_ref | license-p"                >


<!--                    NESTED KEYWORD CLASS ELEMENTS              -->
<!--                    The kinds of keywords that can be used inside
                        a nested keyword structure.                -->
<!ENTITY % nested-kwd.class    
                        "kwd | compound-kwd"                         >


<!--                    PUBLICATION DATE CLASS ELEMENTS            -->
<!--                    New element <pub-date-not-available> added to
                        <book-meta> as an alternative to <pub-date>. 
                        The meaning is that a publication date was 
                        (for whatever reason) not available. Presence 
                        of the element says nothing about
                        publication status. The element
                        <pub-date-not-available> was NOT added to 
                        this class, since the new element may
                        occur only once.                           -->
<!ENTITY % pub-date.class
                        "pub-date"                                   >


<!--                    SUBJECT GROUP CLASS ELEMENTS               -->
<!--                    The element <subj-group> for use in models.-->
<!ENTITY % subj-group.class    
                        "subj-group"                                 >


<!--                    TITLE CLASS                                -->
<!--                    Title metadata element that can be used
                        to provide article-like metadata about
                        other objects, for example a <supplement>  -->
<!ENTITY % title.class  "title"                                      >


<!--                    UNSTRUCTURED KEYWORD GROUP CLASS           -->
<!--                    The <unstructured-kwd-group> element for use 
                        by itself.                                 -->
<!ENTITY % unstructured-kwd-group.class  
                        "unstructured-kwd-group"                     >


<!--                    STRING DATE CLASS                          -->
<!--                    The <string-date> element for use by itself.
                                                                   -->
<!ENTITY % string-date.class  
                        "string-date"                                >


<!-- ============================================================= -->
<!--                    BACK MATTER CLASSES (%backmatter.ent;)     -->
<!-- ============================================================= -->


<!--                    JUST APPENDIX CLASS                        -->
<!--                    The appendix and only the appendix         -->
<!ENTITY % app.class    "app"                                        >


<!--                    BACK MATTER CLASS                          -->
<!--                    Ancillary elements, typically used in the
                        back matter of an article, section, etc.   -->
<!ENTITY % back.class   "ack | app-group | bio | fn-group |
                         glossary |  ref-list"                       >


<!--                    FRONT MATTER CLASS                         -->
<!--                    Ancillary elements, typically used in the
                        front matter of an article, book, etc.  .  -->
<!ENTITY % front.class  "ack | bio | fn-group | glossary"            >


<!--                    FRONT AND BACK CLASS                       -->
<!--                    Ancillary elements, typically used in the
                        front or back matter of an article         -->
<!ENTITY % front-back.class
                        "notes"                                      >


<!--                    SECTION BACK MATTER CLASS                  -->
<!--                    Ancillary elements, typically used in the
                        back matter of a section, etc.             -->
<!ENTITY % sec-back.class
                        "fn-group | glossary |  ref-list"            >


<!--                    JUST SIGNATURE CLASS                       -->
<!--                    The signature and only the signature <sig>
                        for use inside <sig-block>s                -->
<!ENTITY % sig.class    "sig"                                        >


<!-- ============================================================= -->
<!--                    CLASSES USED IN DISPLAY ELEMENTS           -->
<!-- ============================================================= -->


<!--                    ACCESSIBILITY CLASS ELEMENTS               -->
<!--                    Elements added to make it easier to process
                        journal articles in ways that are more
                        accessible to people and devices with special
                        needs, for example the visually handicapped.
                          <alt-text> is a short phrase description of
                        an objects, <long-desc> is a more complete
                        description of the content or intent of an
                        object.                                    -->
<!ENTITY % access.class "alt-text | long-desc"                       >


<!--                    SMALL ACCESSIBILITY CLASS ELEMENTS         -->
<!--                    Elements added to make it easier to process
                        journal articles in ways that are more
                        accessible to people and devices with special
                        needs, for example the visually handicapped.
                          <alt-text> is a short phrase description of
                        an objects. This class is for use when
                        <long-desc> is just too much.              -->
<!ENTITY % small-access.class
                        "alt-text"                                   >


<!--                    CAPTION DISPLAY ELEMENTS                   -->
<!--                    Basic figure display elements              -->
<!ENTITY % caption.class
                        "caption"                                    >


<!--                    DISPLAY ELEMENT BACK MATTER ELEMENTS       -->
<!--                    Miscellaneous stuff (such as photo credits or
                        copyright statement) at the end of a display
                        element such as a figure or a boxed text
                        element such as a sidebar.                 -->
<!ENTITY % display-back-matter.class
                        "attrib | permissions"                       >


<!-- ============================================================= -->
<!--                    DISPLAY ELEMENTS CLASSES                   -->
<!-- ============================================================= -->


<!--                    DISPLAY CLASS ELEMENTS                     -->
<!--                    Graphical or other image-related elements.
                        The display elements may occur within
                        the text of a table cell or paragraph
                        although they are typically at the same
                        hierarchical level as a paragraph.         -->
<!ENTITY % block-display.class    
                        "address | alternatives |
                         answer | answer-set | array |
                         block-alternatives | 
                         boxed-text | chem-struct-wrap | code |
                         explanation | 
                         fig | fig-group | graphic | media |
                         preformat | 
                         question | question-wrap | 
                         question-wrap-group |
                         supplementary-material |
                         table-wrap | table-wrap-group"              >


<!ENTITY % block-display-noalt.class
                        "address |
                         answer | answer-set | array |
                         block-alternatives | 
                         boxed-text | chem-struct-wrap | code |
                         explanation | 
                         fig | fig-group | graphic | media |
                         preformat | 
                         question | question-wrap | 
                         question-wrap-group |
                         supplementary-material |
                         table-wrap | table-wrap-group"              >


<!--                    DISPLAY CLASS ELEMENTS MINUS EXPLANATION   -->
<!--                    The same as block-display.class without
                        the explanation element, for use in 
                        <answer> and <option> elements.            -->
<!ENTITY % block-display-minus-explanation.class
                        "address | alternatives |
                         answer | answer-set | array |
                         block-alternatives | 
                         boxed-text | chem-struct-wrap | code |
                         fig | fig-group | graphic | media |
                         preformat | 
                         question | question-wrap | 
                         question-wrap-group |
                         supplementary-material |
                         table-wrap | table-wrap-group"              >


<!--                    FLOATING DISPLAY CLASS ELEMENTS            -->
<!--                    Block display elements that can take the
                        position attribute, and thus may be floating
                        as opposed to fixed in narrative position in
                        an article. Created for use as the content
                        of <floats-group>.                         -->
<!ENTITY % floats-display.class
                        "alternatives | block-alternatives | 
                         boxed-text |
                         chem-struct-wrap | code | fig | fig-group |
                         graphic | media | preformat |
                         supplementary-material |
                         table-wrap | table-wrap-group"              >


<!--                    BLOCK ALTERNATIVES CLASS ELEMENTS          -->
<!--                    Block display elements that can take the
                        be used inside the <block-alternatives>
                        element.                                   -->
<!ENTITY % block-alternatives.class
                        "boxed-text | fig | fig-group |
                         table-wrap | table-wrap-group"              >


<!--                    FIGURE DISPLAY ELEMENTS                    -->
<!--                    Basic figure display elements              -->
<!ENTITY % fig-display.class
                        "fig | block-alternatives"                   >


<!--                    INLINE DISPLAY CLASS ELEMENTS              -->
<!--                    Non-block display elements that set or
                        display inline with the text               -->
<!ENTITY % inline-display.class
                        "alternatives | inline-graphic |
                         inline-media | 
                         private-char"                               >


<!ENTITY % inline-display-noalt.class
                        "inline-graphic | 
                         inline-media | private-char"                >


<!--                    MOST BASIC DISPLAY ELEMENTS                -->
<!--                    Just the few display elements that are
                        simple display objects: a picture, a movie,
                        a sound file.                              -->
<!ENTITY % just-base-display.class
                        "alternatives | graphic | media"             >


<!ENTITY % just-base-display-noalt.class
                        "graphic | media"                            >


<!--                    SIMPLE DISPLAY ELEMENTS                    -->
<!--                    The simplest and most basic of the Display
                        Class elements, which may be allowed in many
                        places, for example, inside other Display
                        Class elements or inside the cell of a
                        Table                                      -->
<!ENTITY % simple-display.class
                        "alternatives | array | code | graphic | 
                         media | preformat"                          >


<!ENTITY % simple-display-noalt.class
                        "array | code | graphic | media | preformat" >

<!--                    SIMPLE TABLE DISPLAY ELEMENTS              -->
<!--                    Very similar to the simple-display.class, but
                        Table Wrappers <table-wrap> should contain
                        <table>s, <oasis:table>s, etc., not
                        arrays.                                    -->
<!ENTITY % simple-intable-display.class
                        "alternatives | chem-struct-wrap | code |
                         disp-formula | 
                         graphic | media | preformat"                >


<!--                    INSIDE CHEMICAL STRUCTURE ELEMENTS         -->
<!--                    Very similar to the simple-display.class, but
                        for use inside <chem-struct-wrap>, so it
                        can contain both <chem-struct> and
                        <textual-form>                             -->
<!ENTITY % inside-chem-struct-wrap.class
                        "alternatives | chem-struct | code | graphic |
                         media | preformat | textual-form"           >


<!--                    ALTERNATIVES DISPLAY CLASS ELEMENTS        -->
<!--                    Display elements that can be alternatives to
                        each  other inside an alternatives element.
                                                                   -->
<!ENTITY % alternatives-display.class
                        "array | chem-struct | code | graphic |
                         inline-graphic | inline-media | 
                         inline-supplementary-material |
                         media | preformat | private-char |
                         supplementary-material | table |
                         textual-form"                               >


<!-- ============================================================= -->
<!--                   QUESTION AND ANSWER CLASSES                 -->
<!-- ============================================================= -->

<!--                    QUESTION AND ANSWER CLASS                  -->
<!--                    Questions and answers (also in block display
                        class, for use in other places).           -->
<!ENTITY % question-answer.class
                        "answer | answer-set | explanation |
                         question |
                         question-wrap | question-wrap-group"        >


<!--                    QUESTION WRAP CLASS                        -->
<!--                    Class to hold just the element
                        <question-wrap> for use in models.         -->
<!ENTITY % question-wrap.class
                        "question-wrap"                              >


<!--                    OPTION-ONLY CLASS                          -->
<!--                    Holds the <option> element, for use in 
                        content models.                            -->
<!ENTITY % option.class "option"                                     >



<!-- ============================================================= -->
<!--                    MATH CLASSES (%math.ent;)                  -->
<!-- ============================================================= -->


<!--                    MATHEMATICAL EXPRESSIONS AND FORMULAE
                        CLASS ELEMENTS                             -->
<!ENTITY % block-math.class
                        "disp-formula | disp-formula-group"          >


<!--                    CHEMICAL STRUCTURE WRAPPER CLASS           -->
<!ENTITY % chem-struct-wrap.class
                        "chem-struct-wrap"                           >


<!--                    INLINE MATHEMATICAL EXPRESSIONS MIX CLASS
                        ELEMENTS                                   -->
<!ENTITY % inline-math.class
                        "chem-struct | inline-formula"               >


<!--                    MATHEMATICAL EXPRESSIONS CLASS ELEMENTS    -->
<!ENTITY % math.class   "tex-math | mml:math"                        >


<!--                    SIMPLE MATHML CLASS                        -->
<!ENTITY % simple-math.class
                        "mml:math"                                   >


<!-- ============================================================= -->
<!--                    FORMAT CLASSES (%format.ent;)              -->
<!-- ============================================================= -->


<!--                    APPEARANCE CLASS ELEMENTS                  -->
<!--                    Names those elements (inherited from the
                        XHTML table DTD that are only concerned with
                        appearance, not with structure or content.
                        Use of these elements is to be discouraged.-->
<!ENTITY % appearance.class
                        "hr"                                         >


<!--                    FORCED BREAK FORMATTING CLASS ELEMENTS     -->
<!--                    Element to force a formatting break such as
                        a line break                               -->
<!ENTITY % break.class  "break"                                      >


<!--                    EMPHASIS/RENDITION ELEMENTS                -->
<!--                    Elements concerning with marking the location
                        of typographical emphasis (highlighting)
                        DTD Design Note: There are no emphasis
                        elements for <fractur>, <openface> (black
                        board), <script>, etc. because this DTD
                        recommends the use of the STIX extensions
                        to accomplish this, as soon as they are
                        available.                                 -->
<!ENTITY % emphasis.class
                        "bold | fixed-case | italic | monospace |
                         overline | overline-start | overline-end |
                         roman | sans-serif | sc | strike |
                         underline | underline-start | 
                         underline-end |
                         ruby"                                       >


<!--                    FACE MARKUP ELEMENTS                       -->
<!--                    All of the emphasis/rendition elements
                        except <ruby>, for use (initially) inside
                        <ruby> itself.                             -->
<!ENTITY % face-markup.class
                        "bold | fixed-case | italic | monospace |
                         overline | overline-start | overline-end |
                         roman | sans-serif | sc | strike |
                         underline | underline-start | 
                         underline-end"                              >


<!--                    UP/DOWN RENDITION ELEMENTS                 -->
<!ENTITY % subsup.class "sub | sup"                                  >


<!-- ============================================================= -->
<!--                    LINK CLASSES (%link.ent;)                  -->
<!-- ============================================================= -->


<!--                    ADDRESS LINK CLASS ELEMENTS                -->
<!--                    Link elements that can be used inside
                        addresses. This is essentially the three
                        generic external links.
                        (Note: in earlier releases, this Parameter
                        Entity was named %address-elements;,
                        although it functioned as a class.)        -->
<!ENTITY % address-link.class
                        "email | ext-link | uri"                     >



<!--                    SIMPLE LINKS/CROSS-REFERENCES CLASS        -->
<!--                    The smaller and simpler linking elements
                        that might be inside, for example, a
                        Keyword <kwd>                              -->
<!ENTITY % simple-link.class
                        "fn | target | xref"                         >


<!--                    CROSS-REFERENCES CLASS                     -->
<!--                    A class just to hold <xref>.               -->
<!ENTITY % xref.class   "xref"                                       >


<!-- ============================================================= -->
<!--                    FOOTNOTE CLASSES (%link.ent;)              -->
<!-- ============================================================= -->

<!--                    FOOTNOTE LINKS CLASS                       -->
<!--                    Only the most basic, internal links        -->
<!ENTITY % fn-link.class
                        "fn"                                         >


<!--                    FOOTNOTE GROUP CLASS                       -->
<!--                    Collections of footnotes                   -->
<!ENTITY % fn-group.class
                        "fn-group"                                   >


<!-- ============================================================= -->
<!--                    RELATED-OBJECT CLASSES                     -->
<!-- ============================================================= -->


<!--                    JOURNAL ARTICLE LINK CLASS ELEMENTS        -->
<!--                    Links used inside journal articles, to point
                        to related material                        -->
<!ENTITY % article-link.class
                        "inline-supplementary-material |
                         related-article | related-object"           >


<!--                    RELATED ARTICLE LINKS CLASS                -->
<!--                    For using <related-article> at the paragraph
                        level                                      -->
<!ENTITY % related-article.class
                        "related-article  | related-object"          >


<!-- ============================================================= -->
<!--                    LIST CLASSES (%list.ent;)                  -->
<!-- ============================================================= -->


<!--                    DEFINITION LIST CLASS ELEMENT              -->
<!--                    Class for the <list-item>, so it may be used
                        in OR groups.                              -->
<!ENTITY % def-list.class   
                        "def-list"                                   >


<!--                    DEFINITION LIST ITEM CLASS ELEMENT         -->
<!--                    Class for the <list-item>, so it may be used
                        in OR groups.                              -->
<!ENTITY % def-item.class   
                        "def-item"                                   >


<!--                    LIST CLASS ELEMENTS                        -->
<!--                    All the types of lists that may occur
                        as part of the text, therefore excluding
                        Bibliographic Reference Lists <ref-list>   -->
<!ENTITY % list.class   "def-list | list"                            >


<!--                    LIST ITEM CLASS ELEMENT                    -->
<!--                    Class for the <list-item>, so it may be used
                        in OR groups.                              -->
<!ENTITY % list-item.class   
                        "list-item"                                  >


<!-- ============================================================= -->
<!--                    PARAGRAPH CLASSES (%para.ent;)             -->
<!-- ============================================================= -->


<!--                    REST OF PARAGRAPH CLASS                    -->
<!--                    Information for the reader that is at the
                        same structural level as a Paragraph.
                        Contains all paragraph-level objects that are
                        not also used inside tables and excepting
                        also the paragraph element itself          -->
<!ENTITY % rest-of-para.class
                        "ack | disp-quote | speech | statement |
                         verse-group"                                >


<!--                    IN TABLE PARAGRAPH CLASS                   -->
<!--                    The simpler of the paragraph-level elements
                        that might be found inside a table cell or
                        as alternatives to a <table> inside
                        <table-wrap>.                              -->
<!ENTITY % intable-para.class
                        "disp-quote | speech | statement |
                         verse-group"                                >


<!--                    JUST PARAGRAPH CLASS                       -->
<!--                    Set by default to hold the just the Paragraph 
                        element, alone. This parameter entity may
                        be redefined by a customization to name any
                        element(s) that the Tag Set wishes to be
                        able to use anywhere within textual elements
                        that a paragraph may occur. Such elements
                        would also need to be added to the PE
                        %block-display.class; and to some individual
                        element PEs.                               -->
<!ENTITY % just-para.class
                        "p"                                          >


<!--                    NOTHING BUT PARAGRAPH CLASS                -->
<!--                    To hold the paragraph element <p>, alone.
                        This parameter entity should be left
                        untouched by any customization             -->
<!ENTITY % nothing-but-para.class
                        "p"                                          >


<!--                    STATEMENT CLASS                            -->
<!--                    To hold the paragraph element <statement>, 
                        alone.                                     -->
<!ENTITY % statement.class
                        "statement"                                  >


<!-- ============================================================= -->
<!--                    PHRASE CLASSES (%phrase.ent;)              -->
<!-- ============================================================= -->


<!--                    ABBREVIATION CLASS ELEMENTS                -->
<!--                    The element <abbrev>, so it can be added
                        to title elements without adding milestones.
                                                                   -->
<!ENTITY % abbrev.class "abbrev"                                     >


<!--                    FUNDING CLASS ELEMENTS                     -->
<!--                    Small inline elements, that surround a word
                        or phrase in the text to identify pieces of
                        funding metadata. where such material is not
                        pulled out separately but just mixed into
                        text as phrases or sentences within a
                        paragraph. These elements may be used to mark,
                        for example, the <award-id> (such as a grant
                        number or contract number) or the
                        <funding-source> such as the sponsoring
                        organization or trust that awarded the grant.
                                                                   -->
<!ENTITY % funding.class
                        "award-id | funding-source | open-access"    >


<!--                    PHRASE CLASS ELEMENTS                      -->
<!--                    Small inline elements, that surround a word
                        or phrase in the text because the subject
                        (content) should be identified as something
                        special or different                       -->
<!ENTITY % phrase.class "abbrev | 
                         index-term | index-term-range-end |
                         milestone-end | milestone-start |
                         named-content | styled-content"             >


<!--                    STYLED CONTENT CLASS ELEMENTS              -->
<!--                    The <styled-content> element alone, so it
                        can be used in places where emphasis is used.
                                                                   -->
<!ENTITY % styled-content.class
                        "styled-content"                             >


<!--                    PHRASE CONTENT CLASS ELEMENTS              -->
<!--                    Small inline elements, that surround a word
                        or phrase in the text because the subject
                        (content) should be identified as something
                        special or different. This class in is
                        intended to be a subset of the  Phrase Class,
                        for use in restricted situations, such as
                        within metadata paragraphs, to record
                        publisher-identified semantic or usage
                        material.                                  -->
<!ENTITY % phrase-content.class
                        "named-content | styled-content"             >


<!--                    PRICE CLASS ELEMENTS                       -->
<!--                    Created to hold the <price> element so that
                        it can be selectively added to elements,
                        initially to <product>.                    -->
<!ENTITY % price.class  "price"                                      >


<!--                    PRODUCT ELEMENTS                           -->
<!--                    Created to hold the <product> element so that
                        it can be selectively added to elements,
                        initially to <note>.                       -->
<!ENTITY % product.class
                        "product"                                    >


<!-- ============================================================= -->
<!--                    REFERENCES CLASSES (references.ent)        -->
<!-- ============================================================= -->


<!--                    BIBLIOGRAPHIC REFERENCE (CITATION) CLASS   -->
<!--                    The elements that may be included inside a
                        citation (bibliographic reference), either
                        in an all-element-content (<element-citation>)
                        or in a mixed-content citation
                        (<mixed-citation>).                        -->
<!ENTITY % references.class
                        "annotation | article-title | chapter-title |
                         collab | collab-alternatives |comment | 
                         conf-acronym | conf-date | conf-loc | 
                         conf-name | conf-sponsor | 
                         data-title | date | date-in-citation | 
                         day | edition |  email | elocation-id |  
                         etal | ext-link | fpage |
                         gov | institution | institution-wrap |
                         isbn | issn | issn-l | 
                         issue | issue-id | issue-part | issue-title |
                         lpage | month | name | name-alternatives |
                         object-id | page-range | part-title |
                         patent | person-group | pub-id |
                         publisher-loc | publisher-name | role |
                         season | series | size | source | std |
                         string-date | string-name | supplement |
                         trans-source | trans-title | uri | version |
                         volume | volume-id | volume-series | year"  >


<!--                    JUST REFERENCE-LIST CLASS                  -->
<!--                    The reference list and only the reference
                        list                                       -->
<!ENTITY % ref-list.class
                        "ref-list"                                   >


<!--                    STANDARDS CLASS                            -->
<!--                    The elements that can be used in the mixed
                        content model of a <std>, which is a
                        cited standard inside a citation:
                          source contains the name of the standard
                          pub-id contains the standard designator
                          date elements name the official date
                          std-organization names the standards body
                          named-content takes anything else a 
                            publisher/archive needs (come in 
                            from %rendition-plus.                  -->
<!ENTITY % std.class    "day | month | pub-id | source | 
                         std-organization | year"                    >


<!--                    JUST REFERENCE CLASS                       -->
<!--                    The element <ref> for use in models.       -->
<!ENTITY % ref.class    "ref"                                        >


<!-- ============================================================= -->
<!--                    SECTION CLASS (%section.ent;)              -->
<!-- ============================================================= -->


<!--                    SECTION CLASS ELEMENTS                     -->
<!--                    Information for the reader that is at the
                        same structural level as a Section, which is
                        a headed structure full of smaller elements
                        such as paragraphs.                        -->
<!ENTITY % sec.class    "sec"                                        >


<!-- ============================================================= -->
<!--                    TABLE MODEL CLASSES                        -->
<!-- ============================================================= -->


<!--                    JUST TABLE CLASS                           -->
<!--                    To include just a table <table-wrap>
                        element                                    -->
<!ENTITY % just-table.class
                        "table-wrap"                                 >


<!--                    TABLE CLASS ELEMENTS                       -->
<!--                    Elements that will be used to contain the
                        rows and columns inside the Table Wrapper
                        element <table-wrap>.  The following elements
                        can be set up for inclusion:
                          XHTML Table Model    table               -->
<!ENTITY % table.class  "table"                                      >


<!--                    TABLE FOOT CLASS                           -->
<!--                    Elements to include at the end of a table
                        in the table.                              -->
<!ENTITY % table-foot.class
                        "table-wrap-foot"                            >


<!--                    TABLE BODY CLASS                           -->
<!--                    To include just a table <table-wrap>
                        element                                    -->
<!ENTITY % tbody.class  "tbody"                                      >


<!-- ================== End Journal Suite Default Classes  ======= -->

<!-- ============================================================= -->
<!--  MODULE:    Display Class Elements                            -->
<!--  VERSION:   ANSI/NISO JATS Version 1.3 (Z39.96-2021)          -->
<!--  DATE:      June 2021                                         -->
<!--                                                               -->
<!-- ============================================================= -->

<!-- ============================================================= -->
<!--                    PUBLIC DOCUMENT TYPE DEFINITION            -->
<!--                        TYPICAL INVOCATION                     -->
<!--
"-//NLM//DTD JATS (Z39.96) JATS DTD Suite Display Class Elements v1.3 20210610//EN"
     Delivered as file "JATS-display1-3.ent"                       -->
<!-- ============================================================= -->

<!-- ============================================================= -->
<!-- SYSTEM:     JATS DTD Suite                                     -->
<!--                                                               -->
<!-- PURPOSE:    Describes display-related elements such as        -->
<!--             Figures, Graphics, Math, Chemical Structures,     -->
<!--             Graphics, etc.                                    -->
<!--                                                               -->
<!-- TAG SET SPONSOR                                               -->
<!--             National Center for Biotechnology                 -->
<!--                Information (NCBI)                             -->
<!--             National Library of Medicine (NLM)                -->
<!--                                                               -->
<!-- CREATED FOR:                                                  -->
<!--             This module was created for the JATS DTD Suite.   -->
<!--             Digital archives and publishers may use the       -->
<!--             DTD as is for markup of journal literature or     -->
<!--             related material for archiving and transferring   -->
<!--             such material between archives or create a        -->
<!--             custom XML DTD from the Suite for                 -->
<!--             these purposes.                                   -->
<!--                                                               -->
<!--             This DTD is in the public domain. An organization -->
<!--             that wishes to create its own DTD from the suite  -->
<!--             may do so without permission from NLM.            -->
<!--                                                               -->
<!--             The suite has been set up to be extended using a  -->
<!--             new DTD file and a new DTD-specific customization -->
<!--             module to redefine the many Parameter Entities.   -->
<!--             Do not modify the suite directly or redistribute  -->
<!--             modified versions of the suite.                   -->
<!--                                                               -->
<!--             In the interest of maintaining consistency and    -->
<!--             clarity for potential users, NLM requests:        -->
<!--                                                               -->
<!--             1. If you create a DTD from the JATS DTD Suite    -->
<!--                and intend to stay compatible with the suite,  -->
<!--                then please include the following statement    -->
<!--                as a comment in all of your DTD modules:       -->
<!--                   "Created from, and fully compatible with,   -->
<!--                    the ANSI/NISO Z39.96 Journal Article Tag   -->
<!--                    Suite (JATS)."                             -->
<!--                                                               -->
<!--             2. If you alter one or more modules of the suite, -->
<!--                then please rename your version and all its    -->
<!--                modules to avoid any confusion with the        -->
<!--                original suite. Also, please include the       -->
<!--                following statement as a comment in all your   -->
<!--                DTD modules:                                   -->
<!--                   "Based in part on, but not fully compatible -->
<!--                    with, the ANSI/NISO Z39.96 Journal Article -->
<!--                    Tag Suite (JATS)."                         -->
<!--                                                               -->
<!-- ORIGINAL CREATION DATE:                                       -->
<!--             December 2002                                     -->
<!--                                                               -->
<!-- CREATED BY: Mulberry Technologies, Inc. for the NISO Z39.96   -->
<!--             Working Group. Mulberry Technologies was          -->
<!--             supported by National Center for Biotechnology    -->
<!--             Information (NCBI), a center of the US National   -->
<!--             Library of Medicine (NLM).                        -->
<!--                                                               -->
<!--             This module is part of the JATS DTD Suite. The    -->
<!--             Suite is a continuation of work done by NCBI,     -->
<!--             Mulberry Technologies, and Inera, Inc. on the NLM -->
<!--             Journal Archiving and Interchange DTD Suite, which-->
<!--             was originally released in December, 2002.        -->
<!--                                                               -->
<!--             NLM thanks the Harvard University Libraries, both -->
<!--             for proposing that a draft archiving NLM DTD for  -->
<!--             life sciences journals be extended to accommodate -->
<!--             journals in all disciplines and for sponsoring    -->
<!--             Inera Inc.'s collaboration with other DTD         -->
<!--             authors in completing NLM Version 1.0. The        -->
<!--             Andrew W. Mellon Foundation provided support for  -->
<!--             these important contributions.                    -->
<!--                                                               -->
<!--             Suggestions for refinements and enhancements to   -->
<!--             this DTD should be sent in email to:              -->
<!--                 <EMAIL>                         -->
<!-- ============================================================= -->


<!-- ============================================================= -->
<!--                    DTD VERSION/CHANGE HISTORY                 -->
<!-- ============================================================= -->
<!--
Version  Reason/Occasion                   (who) vx.x (yyyy-mm-dd)
   ==============================================================
     JATS Version 1.3 (ANSI/NISO Z39.96-2021)
                                  (DAL/BTU) v1.3 (2021-06-10)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by both the NISO and ANSI memberships 
     to be changed. This version of the NISO JATS was approved 
     by ANSI vote on June 10, 2021, so it supersedes all Committee 
     Drafts as well as the draft version JATS 1.3 2021 (the version
     submitted for vote), becoming NISO JATS v1.3 20210610 
     (ANSI/NISO Z39.96-2019).

     The file names and formal public identifiers have been 
     changed in the modules and the catalogs.

 34. JATS 'v1.3d2 20201130' becomes 'v1.3 20210610' 
     (final ANSI vote date)
     'ANSI/NISO Z39.96-2019' becomes 'ANSI/NISO Z39.96-2021'

  ==============================================================
     JATS Version 1.3d2 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.3d2 (2020-11-30)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by the NISO and ANSI memberships 
     to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     of the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting; it is for Standing Committee review and
     testing. The formal public identifiers have been changed 
     in the modules and the catalogs.
 
 33. OBJECT ID Added <object-id> to: 
        <block-alternatives>, <fig-group>, <table-wrap-group> 

 32. JATS "v1.3d2 20201130" becomes 'v1.3 20210610' (second 
     committee draft following the final ANSI/NISO 1.2 vote)

   ==============================================================
     JATS Version 1.3d1 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.3.d1 (2019-08-31)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by the NISO and ANSI memberships 
     to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     of the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.

 31. MEDIA MODEL - Added <xref> to media model and graphic model.
      
 30. NEW BLOCK ALTERNATIVES - Added new block element
     <block-alternatives> to hold block-level processing
     alternatives, such as the same figure in two languages.
     The new element can be used anywhere figure is used.
      
 29. ADDED SUBJECTS TO WHEREVER KEYWORDS ALLOWED - Added
     <subj-group> directly to the following elements:
      - chem-struct-wrap 
      - fig and fig-group
      - graphic
      - media
      - supplementary-material (thru fig-model)
      - table-wrap and table-wrap-group
 
 28. MULTI-LANGUAGE FIGURE AND TABLE LABELS AND CAPTIONS

     - Made <label> repeatable inside <fig> and
       <fig-group> to allow both labels and captions to 
       repeat to be tagged in multiple languages:
         label   xml-lang="a"
         label   xml-lang="b"
         caption xml-lang="a"
         caption xml-lang="a"
 
     - Made <label> repeatable inside <fig-model> to allow for
       both labels and captions to be tagged in multiple
       languages:
         label   xml-lang="a"
         label   xml-lang="b"
         caption xml-lang="a"
         caption xml-lang="a"
          
      - Similar change inside <table-wrap> for the same reason.
        
      - Similar change inside <table-wrap-group> for the same reason.
 
 27. LINKING TABLES TO SUPPLEMENTARY MATERIAL
 
     - Added the <xref> element (for an internal cross-reference)
       to the large OR-group inside the body of a 
       <table-wrap-group>, to be used to make a link to a 
       <supplementary-material> element as a way of recording 
       that a dataset or similar is the data from which the 
       tables in the <table-wrap-group> were derived.
  
 25. JATS "1.2" becomes "v1.3d1 20190831" (first committee
     draft following the final ANSI/NISO vote) 

   ==============================================================
     JATS Version 1.2 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.2     (2019-02-08)
    
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the both the NISO and ANSI membership 
     to be changed. This version of the NISO JATS was approved 
     by ANSI vote on Feb 08, 2019, so it supersedes all Committee 
     Drafts as well as the draft version JATS 1.2 2018 (the version
     submitted for vote), becoming NISO JATS v1.2 20190208 
     (ANSI/NISO Z39.96-2021).
  
 24. JATS "1.2" becomes "v1.2 20190208" (final ANSI vote date)
     "ANSI/NISO Z39.96-2015" becomes "ANSI/NISO Z39.96-2019"
     
   ==============================================================
     JATS Version 1.2 (ANSI/NISO Z39.96-2018)
                                   (DAL/BTU) v1.2     (2018-11-30)
    
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the both the NISO and ANSI membership 
     to be changed. This version of the NISO JATS was approved 
     by ANSI and NISO vote, so it supersedes all Committee Drafts
     and becoming NISO JATS 1.2 (ANSI/NISO Z39.96-2018).
  
 23. JATS "1.2d2" and "v1.2d2 20180401" became
     JATS "1.2" and "v1.2 20181130" 

     =============================================================
     JATS Version 1.2d2              (DAL/BTU) v1.2d2  (2018-04-01)

     JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
     BITS is under continuous maintenance, and is modified at the
     discretion of the working group.
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.

 22. INLINE-MEDIA - Added new element <inline-media>, which is to
     <media> as <inline-graphic> is to <graphic>. The content
     model is the same as <inline-supplementary-material>, in
     other words there should be content, this is not typically 
     an empty element. The attributes are the same as <media>,
     except without @position and @orientation.

 21. BITS "2.0" and "v2.0 20151225" remain unchanged
   
     JATS "1.2d1" and "v1.2d1 20171231" became
     JATS "1.2d2" and "v1.2d2 20180401". 

     No module names were changed.
 
    =============================================================
     JATS Version 1.2d1 (ANSI/NISO Z39.96-2015)
                                   (DAL/BTU) v1.2d1  (2017-12-31)
 
     NISO JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
  
 20. JATS "1.2d1" and "v1.2d1 20170631" became
     JATS "1.2d1" and "v1.2d1 20171231". 
     
    =============================================================
     JATS Version 1.2d1              (DAL/BTU) v1.2d1  (2017-06-31)

     NISO JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.

 19. INLINE GRAPHIC and PRIVATE-CHAR - Both <inline-graphic> and
     <private-char> added to elements allowed in <code>.
 
 18. INLINE GRAPHIC - Changed from small-access.class to
     access.class to add <long-desc>
 
 17. JATS became version "1.2d1" and "v1.2d1 20170631" 

     =============================================================
     JATS Version 1.1                (DAL/BTU) v1.1  (2015-12-15)

     JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
     JATS 1.1 was approved in late 2015, so the formal public 
     identifiers were changed in the modules and the catalogs.
     No model or attribute changes were made at this time. 
 
 16. JATS became version "1.1" and "v1.1 20151215"
     BITS remained version "2.0" but becomes "v2.0 20151225"

     =============================================================
     JATS Version 1.1d3             (DAL/BTU) v1.1d3  (2015-03-01)
   
     The changes in this release are in response to NISO 
     License and Indicators Recommended Practice.

     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the NISO membership to be changed. This
     Committee Draft 1.1d3 will be sent to the NISO voting
     membership, to become (if approved) NISO JATS 1.1.
     
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee.

 15. JATS became version "1.1d3" and "v1.1d3 20150301"
 
     =============================================================
     JATS Version 1.1d2              (DAL/BTU) v1.1d2 (2014-09-30)
   
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the NISO membership to be changed. This
     Committee Draft 1.1d2 will be sent to the NISO voting
     membership, to become (if approved) NISO JATS 1.1.
     
     This catalog represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee.

  14. JATS became version "1.1d2" and "v1.1d2 20140930//EN"
   
     =============================================================
     NISO JATS Version 1.1d1           (DAL/BTU) v1.1 (2013-11-15)
   
     ANSI/NISO Z39.96-2012 (pre-release for V1.0 BITS; Version 1.1d1) 

     NISO JATS Standing Committee met and answered the requests and
     suggestions from the NISO request forms.

     Details concerning ANSI/NISO Z39.96-2012 JATS-based DTDs,  
     XSDs, RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.3d2/

 13. CAPTION ATTRIBUTE - Changed the mechanism by which the @style
      attribute is associated with the element <caption>. 
      Previously, the <caption> element's base attributes were 
      declared in this module and additional attributes (@style) 
      for it were declared in the XHTML Table Module. The @style 
      attribute is now declared in this module, and the caption 
      attributes in the XHTML table model have been set to IGNORE. 
      This was done to make sure that the attributes available on 
      the JATS <caption> element did not change based on which table
      model a JATS user was using.

 12. CODE - Added the new element <code> (using code-atts and
     code-elements). Resembles <preformat>, but different
     semantics.

 11. CAPTION ATTRIBUTES - Added the new PE %jats-common-atts; to
     the caption attributes, even though doing so while making
     no other changes would mean multiple definitions
     of the @id and @content-type attributes. This would produce a
     warning, which should be ignored, so see item #13 above.

 10. GLOBAL ATTRIBUTES - Added the new parameter entity 
         %jats-common-atts;
     to every element in this module. This PE adds (for now) the
     @id attribute and the @xml:base attribute to every element,
     whether metadata or narrative.
     Since the @id in this parameter entity is optional, a second
     parameter entity jats-common-atts-id-required was also added.
     The two are kept in sync with the jats-base-atts parameter 
     entity.

  9. ABSTRACT AND KEYWORDS ON NEW STRUCTURES
     - Added <abstract> (through %abstract.class;) and <kwd-group> 
       (through %kwd-group.class;) to the following elements:
        - chem-struct-wrap (through %chem-struct-wrap-model;)
        - fig (through %fig-model;)
        - fig-group (through %fig-group-model;)
        - graphic (through %graphic-model;)
        - media (through %media-model;)
        - table-wrap (through %table-wrap-model;)
        - table-wrap-group (through %table-wrap-group-model;)
   
     =============================================================
     NISO JATS Version 1.0             (DAL/BTU) v1.0 (2012-xx-xx)
   
     ANSI/NISO Z39.96-2012 (Version 1.0) 

     Details concerning ANSI/NISO Z39.96-2012 JATS-based DTDs,  
     XSDs, RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.3d2/
   
  8. Updated the DTD-version attribute to "1.0" and the formal
     public identifier to the date: "v1.0 20120330//EN".

     =============================================================
     Version 0.4                       (DAL/BTU) v0.4 (2011-01-31)
   
     This Tag Set is in the process of becoming a NISO standard.
     The version numbers are starting over from 0.4", as a Trial
     Use Draft, to be made into "Version 1.0" when the Tag Suite 
     becomes a NISO standard. Thus, the version number that would
     have been "NLM Version 3.1 Draft" has become NISO JATS
     "Version 0.4".

     Details on NISO Trial Use Draft Version 0.4 are available at
           http://jats.nlm.nih.gov/JATS-0.4.
   
  7. Updated the DTD-version attribute to "0.4" 
   
  6. Updated the public identifier to "v0.4 20110131", 
     modified the formal public identifier to include "JATS (Z39.96)",
     and the filename as delivered to include "JATS" and the
     new version number "0".

  5. INLINE GRAPHIC - Uses new class %small-access.class; (no
     model change)

  4. POSITION ATTRIBUTE - Added the value "background"

  3. @SPECIFIC-USE and @XML:LANG - Added the @specific-use and
     @xml:lang to %display-atts;. If these attribute were
     already part of the list, deleted them. These attributes were
     added to:
      - array       (already there)
      - boxed-text  (already there)
      - caption     (already there)
      - chem-struct (added @xml:lang, @specific-use already)
      - chem-struct-wrap (added @xml:lang, @specific-use already)
      - fig (already there)
      - fig-group (added @xml:lang, @specific-use already there)
      - graphic (added @xml:lang, @specific-use already there)
      - inline-graphic (both)
      - media (added @xml:lang, @specific -use already there)
      - preformat (already there)
      - supplementary-material (already there)
      - table-wrap (already there)
      - table-wrap-group (added @xml:lang, @specific -use
          already there)

  2. GRAPHIC MODEL - Removed the dependency which had both
     <graphic> and <media> modeled with the same parameter
     entity %graphic-model. Created new PE for %media-model;
     but set it (as the default) to %graphic-model so that no
     customization would break. NEW PE

     =============================================================
     Version 3.0                       (DAL/BTU) v3.0 (2007-10-31)

     Version 3.0 is the first non-backward-compatible release.
     In addition to the usual incremental changes, some
     elements and attributes have been renamed and/or remodeled
     to better meet user needs and to increase tag set consistency.
     All module change histories are available through the Tag Suite
     web site at http://dtd.nlm.nih.gov.

     Details on version 3.0 are available at
           http://dtd.nlm.nih.gov/3.0.

  1. Updated public identifier to "v3.0 20071031//EN"              -->

<!-- ============================================================= -->
<!-- Details concerning 
     ANSI/NISO Z39.96-2015 JATS-based DTDs, XSDs, 
     RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.1/                            -->

<!-- ============================================================= -->
<!--                    ATTRIBUTES FOR MULTIPLE ELEMENTS           -->
<!-- ============================================================= -->


<!--                    DISPLAY ATTRIBUTES                         -->
<!--                    Attributes used for several of the block
                        display elements                           -->
<!ENTITY % display-atts
            "position   (anchor | background | float | margin)
                                                          'float'
             orientation
                        (portrait | landscape)            'portrait'
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!-- ============================================================= -->
<!--                    SPECIFIC ATTRIBUTE LISTS                   -->
<!-- ============================================================= -->


<!--                    ARRAY ATTRIBUTES                           -->
<!--                    Attributes for the <array> element         -->
<!ENTITY % array-atts
            "%jats-common-atts;                                       
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             orientation
                        (portrait | landscape)            'portrait'
             xml:lang   NMTOKEN                           #IMPLIED" >


<!--                    BLOCK ALTERNATIVES ATTRIBUTES              -->
<!--                    Attributes for the <block-alternatives> 
                        element                                    -->
<!ENTITY % block-alternatives-atts
            "%jats-common-atts;"                                     >               

<!--                    BOXED TEXT ATTRIBUTES                      -->
<!--                    Attributes for the <boxed-text> element    -->
<!ENTITY % boxed-text-atts
            "%jats-common-atts;                                       
              %display-atts;
              content-type
                        CDATA                             #IMPLIED"  >


<!--                    CAPTION ATTRIBUTES                         -->
<!--                    Attributes for <caption> element           -->
<!--                    The attribute @style was added to make all
                        JATS captions the same as XHTML table
                        captions.                                  -->
<!ENTITY % caption-atts
            "%jats-common-atts;
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED 
             style      CDATA                             #IMPLIED"  >

<!--                    CHEMICAL STRUCTURE ATTRIBUTES              -->
<!--                    Attributes for <chem-struct>, a wrapper
                        around one (typically inline) chemical
                        structure, or one of several structures in
                        a <chem-struct-wrap>                       -->
<!ENTITY % chem-struct-atts
            "%jats-common-atts;                                       
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED
             %might-link-atts;"                                      >


<!--                    CHEMICAL STRUCTURE WRAPPER ATTRIBUTES      -->
<!--                    Attributes for the <chem-struct-wrap>
                        element, the outer wrapper around one or more
                        block-level chemical structures            -->
<!ENTITY % chem-struct-wrap-atts
            "%jats-common-atts;                                       
              %display-atts;
              content-type
                        CDATA                             #IMPLIED"  >


<!--                   CODE ATTRIBUTES                             -->
<!--                   Attributes for the <code> element           -->
<!ENTITY % code-atts
            "%jats-common-atts;                                       
             code-type          CDATA                     #IMPLIED
             code-version       CDATA                     #IMPLIED
             executable         (yes | no)                #IMPLIED
             language           CDATA                     #IMPLIED
             language-version   CDATA                     #IMPLIED
             platforms          CDATA                     #IMPLIED
             %display-atts;
             xml:space (default | preserve)       #FIXED 'preserve'" >


<!--                    FIGURE ATTRIBUTES                          -->
<!--                    Attributes for Figures <fig>               -->
<!ENTITY % fig-atts
            "%jats-common-atts;                                       
              %display-atts;
              fig-type  CDATA                             #IMPLIED"  >


<!--                    FIGURE GROUP ATTRIBUTES                    -->
<!--                    Attributes for Figure Groups <fig-group>   -->
<!ENTITY % fig-group-atts
            "%jats-common-atts;                                       
              %display-atts;
              content-type
                        CDATA                             #IMPLIED "  >


<!--                    GRAPHIC ATTRIBUTES                         -->
<!--                    Attributes for the <graphic> element       -->
<!ENTITY % graphic-atts
            "%jats-common-atts;                                       
              %display-atts;
              content-type
                        CDATA                             #IMPLIED
              mime-subtype
                        CDATA                             #IMPLIED
              mimetype  CDATA                             #IMPLIED
              %link-atts;"                                           >


<!--                    INLINE GRAPHIC ATTRIBUTES                  -->
<!--                    Attributes for the <inline-graphic> element-->
<!ENTITY % inline-graphic-atts
            "%jats-common-atts;                                       
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             baseline-shift
                        CDATA                             #IMPLIED
             mimetype   CDATA                             #IMPLIED
             mime-subtype
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED
             %link-atts;"                                            >


<!--                    MEDIA ATTRIBUTES                           -->
<!--                    Attributes for the <media> element         -->
<!ENTITY % inline-media-atts
            "%jats-common-atts;                                       
              content-type
                         CDATA                            #IMPLIED
              mimetype   CDATA                            #IMPLIED
              mime-subtype
                         CDATA                            #IMPLIED
              vocab      CDATA                            #IMPLIED
              vocab-identifier
                         CDATA                            #IMPLIED
              vocab-term CDATA                            #IMPLIED
              vocab-term-identifier
                         CDATA                            #IMPLIED
              specific-use
                         CDATA                            #IMPLIED
              xml:lang   NMTOKEN                          #IMPLIED
              %link-atts;"                                           >


<!--                    MEDIA ATTRIBUTES                           -->
<!--                    Attributes for the <media> element         -->
<!ENTITY % media-atts
            "%jats-common-atts;                                       
              %display-atts;
              content-type
                        CDATA                             #IMPLIED
              mimetype  CDATA                             #IMPLIED
              mime-subtype
                        CDATA                             #IMPLIED
              %link-atts;"                                           >


<!--                   PREFORMATTED TEXT ATTRIBUTES                -->
<!--                   Attributes for the <preformat> element      -->
<!ENTITY % preformat-atts
            "%jats-common-atts;                                       
              %display-atts;
              preformat-type
                        CDATA                             #IMPLIED
              xml:space (default | preserve)     #FIXED 'preserve'"  >


<!--                    SUPPLEMENTARY INFORMATION ATTRIBUTES       -->
<!--                    Attributes for Supplementary Material
                        <supplementary-material>                   -->
<!ENTITY % supplementary-material-atts
            "%jats-common-atts;                                       
              %display-atts;
              content-type
                        CDATA                             #IMPLIED
              mimetype  CDATA                             #IMPLIED
              mime-subtype
                        CDATA                             #IMPLIED
              %might-link-atts;"                                     >


<!-- ============================================================= -->
<!--                    BLOCK-LEVEL PROCESSING ALTERNATIVES        -->
<!-- ============================================================= -->


<!--                    BLOCK ALTERNATIVES MODEL                   -->
<!--                    Model for the <alternatives> processing
                        alternatives element                       -->
<!ENTITY % block-alternatives-model
                        "( (%id.class;)*, 
                           (%block-alternatives.class;)+ )"          >



<!--                    BLOCK-LEVEL ALTERNATIVES FOR PROCESSING    -->
<!--                    Container element used to hold a group of
                        block-level processing alternatives, for 
                        example, the same figure, once in Spanish,
                        once in English, and once in Portuguese.
                        This element is a physical grouping to 
                        contain multiple logically equivalent
                        (substitutable) versions of the same 
                        graphical object.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=block-alternatives
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=block-alternatives
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=block-alternatives
                                                                   -->
<!ELEMENT  block-alternatives 
                        %block-alternatives-model;                   >
<!ATTLIST  block-alternatives
             %block-alternatives-atts;                               >


<!-- ============================================================= -->
<!--                    TABLE ATTRIBUTES                           -->
<!-- ============================================================= -->


<!--                    TABLE WRAPPER ATTRIBUTES                   -->
<!--                    Attributes to be added to the regular NLM
                        table attributes, for example, when the
                        Elsevier or OASIS Exchange table models are
                        used.                                      -->
<!ENTITY % other-table-wrap-atts
            ""                                                       >                                       


<!--                    TABLE GROUP ATTRIBUTES                     -->
<!--                    Attributes for groups of <table-wrap>
                        elements <table-wrap-group>                -->
<!ENTITY % table-wrap-group-atts
            "%jats-common-atts;                                       
              %display-atts;
              content-type
                        CDATA                             #IMPLIED
              %other-table-wrap-atts;"                               >


<!--                    TABLE WRAPPER ATTRIBUTES                   -->
<!--                    Attributes for the <table-wrap> element,
                        the container for <table>s                 -->
<!ENTITY % table-wrap-atts
            "%jats-common-atts;                                       
              %display-atts;
              content-type
                        CDATA                             #IMPLIED
              %other-table-wrap-atts;"                               >


<!--                    TABLE WRAP FOOT ATTRIBUTES                 -->
<!--                    Attributes for the <table-wrap-foot> element
                                                                   -->
<!ENTITY % table-wrap-foot-atts
            "%jats-common-atts;"                                     >                                       


<!-- ============================================================= -->
<!--                    PARAMETER ENTITIES FOR CONTENT MODELS      -->
<!-- ============================================================= -->


<!--                    FIGURE-LIKE CONTENT MODEL                  -->
<!--                    Content model for the Figure element and any
                        similarly structured elements              -->
<!ENTITY % fig-model    "((%id.class;)*, 
                          (%label.class;)*, (%caption.class;)*,
                          (%abstract.class;)*, (%kwd-group.class;)*,
                          (%subj-group.class;)*, 
                          (%access.class; | %address-link.class;)*,
                          (%block-math.class; |
                           %chem-struct-wrap.class; |
                           %intable-para.class; |
                           %just-table.class; | %just-para.class; |
                           %list.class; | %simple-display.class; |
                           %xref.class;)*,
                          (%display-back-matter.class;)* )"          >


<!-- ============================================================= -->
<!--                    ARRAY ELEMENTS                             -->
<!-- ============================================================= -->


<!--                    ARRAY CONTENT MODEL                        -->
<!--                    Content model for the <array> element      -->
<!ENTITY % array-model  "(label?,
                          (%access.class; | %address-link.class;)*,
                          ( (%just-base-display.class;)* |
                            %tbody.class; ),
                          (%display-back-matter.class;)* )"          >


<!--                    ARRAY (SIMPLE TABULAR ARRAY)               -->
<!--                    Used to define in-text table-like (columnar)
                        material.  Uses the XHTML table body element
                        or a graphic to express the rows and columns.
                        These have neither labels nor captions,
                        arrays with labels and captions are table
                        wrappers.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=array
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=array
                                                                   -->
<!ELEMENT  array        %array-model;                                >
<!ATTLIST  array
             %array-atts;                                            >


<!-- ============================================================= -->
<!--                    BOXED TEXT ELEMENTS                        -->
<!-- ============================================================= -->


<!--                    BOXED TEXT MODEL                           -->
<!--                    Complete content model for the boxed text
                        element <boxed-text>                       -->
<!ENTITY % boxed-text-model
                        "((%id.class;)*, sec-meta?, label?, caption?,
                          (%para-level;)*, (%sec-level;)*,
                          (%sec-back-matter-mix;)*,
                          (%display-back-matter.class;)* )"          >


<!--                    BOXED TEXT                                 -->
<!--                    Textual material that is outside the flow
                        of the narrative text, for example, a
                        sidebar, marginalia, text insert, caution or
                        note box, etc.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=boxed-text
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=boxed-text
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=boxed-text
                                                                   -->
<!ELEMENT  boxed-text   %boxed-text-model;                           >
<!ATTLIST  boxed-text
             %boxed-text-atts;                                       >


<!-- ============================================================= -->
<!--                    CHEMICAL STRUCTURE ELEMENTS                -->
<!-- ============================================================= -->


<!--                    CHEMICAL STRUCTURE WRAPPER MODEL           -->
<!--                    Content model for the Chemical Structure
                        Wrapper <chem-struct-wrap> element         -->
<!ENTITY % chem-struct-wrap-model
                        "((%id.class;)*, label?, (%caption.class;)?,
                          (%abstract.class;)*, (%kwd-group.class;)*,
                          (%subj-group.class;)*,
                          (%access.class; | %address-link.class;)*,
                          (%inside-chem-struct-wrap.class;)+,
                          (%display-back-matter.class;)* )"          >


<!--                    CHEMICAL STRUCTURE WRAPPER                 -->
<!--                    A chemical expression, reaction, equation,
                        etc. that is set apart within the text.
                        These may be numbered.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=chem-struct-wrap
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=chem-struct-wrap
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=chem-struct-wrap
                                                                   -->
<!ELEMENT  chem-struct-wrap
                        %chem-struct-wrap-model;                     >
<!ATTLIST  chem-struct-wrap
             %chem-struct-wrap-atts;                                 >


<!--                    CHEMICAL STRUCTURE ELEMENTS                -->
<!--                    Those elements that may mix with the data
                        characters inside a Chemical Structure
                        <chem-struct>                              -->
<!ENTITY % chem-struct-elements
                        "| %access.class; | %address-link.class; |
                         %break.class; |
                         %emphasis.class; | %label.class; |
                         %list.class; | %math.class; |
                         %phrase-content.class; |
                         %simple-display.class; |
                         %simple-link.class; | %subsup.class; "      >


<!--                    CHEMICAL STRUCTURE MODEL                   -->
<!--                    A chemical expression, reaction, equation,
                        etc. that is set apart within the text
                                                                   -->
<!ENTITY % chem-struct-model
                        "(#PCDATA %chem-struct-elements;)* "         >


<!--                    CHEMICAL STRUCTURE (DISPLAY)               -->
<!--                    A chemical expression, reaction, equation,
                        etc. that is set apart within the text.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=chem-struct
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=chem-struct
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=chem-struct
                                                                   -->
<!ELEMENT  chem-struct  %chem-struct-model;                          >
<!ATTLIST  chem-struct
             %chem-struct-atts;                                      >


<!-- ============================================================= -->
<!--                    FIGURE ELEMENTS                            -->
<!-- ============================================================= -->


<!--                    FIGURE GROUP MODEL                         -->
<!--                    Content model for a Figure Group element   -->
<!ENTITY % fig-group-model
                        "((%id.class;)*, (%label.class;)*, 
                          (%caption.class;)*, (%abstract.class;)*, 
                          (%kwd-group.class;)*, (%subj-group.class;)*, 
                          (%access.class; | %address-link.class;)*,
                          (%fig-display.class; | %xref.class; | 
                           %just-base-display.class;)* )"            >


<!--                    FIGURE GROUP                               -->
<!--                    Used for a group of figures that must be
                        displayed together
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=fig-group
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=fig-group
                                                                   -->
<!ELEMENT  fig-group    %fig-group-model;                            >
<!ATTLIST  fig-group
             %fig-group-atts;                                        >


<!--                    FIGURE                                     -->
<!--                    A block of graphic or textual material that
                        is identified as a "Figure", usually with
                        a caption and a label such as "Figure" or
                        "Figure 3.".The content of a Figure need not
                        be graphical in nature,.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=fig
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=fig
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=fig
                                                                   -->
<!ELEMENT  fig          %fig-model;                                  >
<!ATTLIST  fig
             %fig-atts;                                              >


<!--                    CAPTION BODY PARTS                         -->
<!--                    Elements that may be included in the body of
                        the <caption> element                      -->
<!ENTITY % caption-body-parts
                        "(%just-para.class;)*"                       >


<!--                    CAPTION MODEL                              -->
<!--                    Content model for the <caption> element    -->
<!ENTITY % caption-model
                        "(title?, %caption-body-parts;)"             >


<!--                    CAPTION OF A FIGURE, TABLE, ETC.           -->
<!--                    Wrapper element for the textual description
                        associated with a figure, table, etc. In
                        some source document captions, the first
                        sentence is set off from the rest as a title.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=caption
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=caption
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=caption
                                                                   -->
<!ELEMENT  caption      %caption-model;                              >
<!ATTLIST  caption
             %caption-atts;                                          >


<!--ELEM  title        Defined in %common.ent;                     -->
<!--ELEM  p            Defined in %common.ent;                     -->


<!-- ============================================================= -->
<!--                    THE GRAPHIC AND MEDIA OBJECT ELEMENTS      -->
<!-- ============================================================= -->


<!--                    GRAPHIC MODEL                              -->
<!--                    Content model for the <graphic> element    -->
<!ENTITY % graphic-model
                        "(%access.class; | %abstract.class; | 
                          %address-link.class; | %caption.class; | 
                          %display-back-matter.class; | 
                          %id.class; | %label.class; |
                          %kwd-group.class; | %subj-group.class; |
                          %xref.class;)* "                           >

<!--                    GRAPHIC                                    -->
<!--                    An external file that holds a picture,
                        illustration, etc., usually as some form of
                        binary object. The "content" of the <graphic>
                        element is not the object, but merely
                        information about the object. The "href"
                        attribute points to the file containing
                        the object.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=graphic
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=graphic
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=graphic
                                                                   -->
<!ELEMENT  graphic      %graphic-model;                              >
<!ATTLIST  graphic
             %graphic-atts;                                          >


<!--                    INLINE MEDIA MODEL                         -->
<!--                    Content model for the <inline-media> element
                                                                   -->
<!ENTITY % inline-media-elements  
                        "| %access.class; | %address-link.class; |
                         %emphasis.class; |
                         %phrase-content.class; | %subsup.class;"    >


<!--                    INLINE MEDIA OBJECT                        -->
<!--                    An external file that holds a media object
                        to be used inline, such as a pronunciation
                        file. "content" of the <media> element is not 
                        the object, but merely information about the
                        object or the word to be linked. The "href" 
                        attribute points to the file containing the 
                        object.
                        Details at:                      http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=inline-media
                      http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=inline-media
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=inline-media
                                                                   -->
<!ELEMENT  inline-media (#PCDATA %inline-media-elements;)*           >
<!ATTLIST  inline-media
             %inline-media-atts;                                     >


<!--                    MEDIA MODEL                                -->
<!--                    Content model for the <media> element      -->
<!ENTITY % media-model  "(%access.class; | %abstract.class; | 
                          %address-link.class; | %caption.class; |
                          %display-back-matter.class; | 
                          %id.class; |  %label.class; | 
                          %kwd-group.class; | %subj-group.class; |                        
                          %xref.class;)* "            >


<!--                    MEDIA OBJECT                               -->
<!--                    An external file that holds a media object,
                        such as an animation or a movie. The
                        "content" of the <media> element is not the
                        object, but merely information about the
                        object. The "href" attribute points to the
                        file containing the object.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=media
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=media
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=media
                                                                   -->
<!ELEMENT  media        %media-model;                                >
<!ATTLIST  media
             %media-atts;                                            >


<!-- ============================================================= -->
<!--                    INLINE GRAPHIC                             -->
<!-- ============================================================= -->


<!--                    INLINE GRAPHIC MODEL                       -->
<!--                    Content model for the <inline-graphic>
                        element                                    -->
<!ENTITY % inline-graphic-model
                        "((%access.class;)*)"                        >


<!--                    INLINE GRAPHIC                             -->
<!--                    A small graphic such as an icon or a small
                        picture symbol that is displayed or set
                        in the same line as the text.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=inline-graphic
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=inline-graphic
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=inline-graphic
                                                                   -->
<!ELEMENT  inline-graphic
                        %inline-graphic-model;                       >
<!ATTLIST  inline-graphic
             %inline-graphic-atts;                                   >


<!-- ============================================================= -->
<!--                    PRESERVE THE WHITESPACE TEXT               -->
<!-- ============================================================= -->


<!--                    PREFORMATTED TEXT ELEMENTS                 -->
<!--                    Elements that may be used, along with data
                        characters, inside the content model for the
                        <preformat> element                        -->
<!ENTITY % preformat-elements
                        "| %access.class; | %address-link.class; |
                         %display-back-matter.class; |
                         %emphasis.class; | %id.class; |
                         %phrase.class; | %subsup.class;"            >


<!--                    PREFORMAT MODEL                            -->
<!--                    Content model for the <preformat> element  -->
<!ENTITY % preformat-model
                        "(#PCDATA %preformat-elements;)*"            >


<!--                    PREFORMATTED TEXT                          -->
<!--                    Used for preformatted text such as
                        computer code in which whitespace, such as
                        tabs, line feeds, and spaces, should be
                        preserved.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=preformat
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=preformat
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=preformat
                                                                   -->
<!ELEMENT  preformat    %preformat-model;                            >
<!ATTLIST  preformat
             %preformat-atts;                                        >


<!--                    CODE ELEMENTS                              -->
<!--                    Elements that may be used, along with data
                        characters, inside the content model for the
                        <code> element                             -->
<!ENTITY % code-elements
                        "| %address-link.class; |  %emphasis.class; | 
                         %inline-display-noalt.class; | 
                         %phrase.class; |  %simple-link.class; |
                         %subsup.class;"                             >


<!--                    CODE TEXT                                  -->
<!--                    A container element for technical content
                        such as programming language code, 
                        pseudo-code, schemas, or a markup fragment.
                          This material may be preformatted text, with
                        or without emphasis, or it may be an external
                        link to a binary executable file.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=code
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=code
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=code
                                                                   -->
<!ELEMENT  code         (#PCDATA %code-elements;)*                   >
<!ATTLIST  code
             %code-atts;                                             >


<!-- ============================================================= -->
<!--                    SUPPLEMENTARY MATERIAL                     -->
<!-- ============================================================= -->


<!--                    SUPPLEMENTARY MATERIAL MODEL               -->
<!--                    Content model for the
                        <supplementary-material> element           -->
<!ENTITY % supplementary-material-model
                        "%fig-model;"                                >


<!--                    SUPPLEMENTARY MATERIAL                     -->
<!--                    Additional data files that contain
                        information directly supportive of the item,
                        for example, an audio clip, movie, database,
                        spreadsheet, applet, or other external file.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=supplementary-material
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=supplementary-material
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=supplementary-material
                                                                   -->
<!ELEMENT  supplementary-material
                        %supplementary-material-model;               >
<!ATTLIST  supplementary-material
             %supplementary-material-atts;                           >


<!-- ============================================================= -->
<!--                    TABLE ELEMENTS                             -->
<!-- ============================================================= -->


<!--                    TABLE WRAPPER GROUP MODEL                  -->
<!--                    Content model for the <table-wrap-group>
                        element                                    -->
<!ENTITY % table-wrap-group-model
                        "((%id.class;)*, (%label.class;)*, 
                          (%caption.class;)*, (%abstract.class;)*, 
                          (%kwd-group.class;)*, (%subj-group.class;)*, 
                          (%access.class; | %address-link.class;)*,
                          (%just-table.class; | %xref.class;)+ )"    >


<!--                    TABLE WRAPPER GROUP                        -->
<!--                    Used as a wrapper tag to contain a group of
                        tables that must be displayed together
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=table-wrap-group
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=table-wrap-group
                                                                   -->
<!ELEMENT  table-wrap-group
                        %table-wrap-group-model;                     >
<!ATTLIST  table-wrap-group
             %table-wrap-group-atts;                                 >


<!--                    TABLE WRAPPER CONTENT MODEL                -->
<!--                    Content model for the container element that
                        surrounds the standard table models for row
                        and columns.                               -->
<!ENTITY % table-wrap-model
                        "((%id.class;)*, 
                          (%label.class;)*, (%caption.class;)*,
                          (%abstract.class;)*, (%kwd-group.class;)*, 
                          (%subj-group.class;)*, 
                          (%access.class; | %address-link.class;)*,
                          (%inside-table-wrap;)*,
                          (%table-foot.class; |
                           %display-back-matter.class;)* )"          >


<!--                    TABLE WRAPPER                              -->
<!--                    Used to hold a complete table, that is, not
                        only the rows and columns that make up a
                        table, but also the table captions, list
                        of table footnotes, alternative descriptions
                        for accessibility, etc.  Within the Table
                        Wrapper element, the row and column tags that
                        describe the table cells are defined by one
                        of the popular "standard" table models, for
                        example the XHTML table model, OASIS Exchange
                        (CALS) table model, or the Elsevier Science
                        Full Length Article table body <tblbody>
                        model, et al.)
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=table-wrap
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=table-wrap
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=table-wrap
                                                                   -->
<!ELEMENT  table-wrap   %table-wrap-model;                           >
<!ATTLIST  table-wrap
             %table-wrap-atts;                                       >


<!--                    TABLE WRAP FOOTER MODEL                    -->
<!--                    Content model for the <table-wrap-foot>
                        element                                    -->
<!ENTITY % table-wrap-foot-model
                        "(title?,
                          (%just-para.class; |  %fn-group.class; |
                           %fn-link.class; |
                           %display-back-matter.class;)+ )"          >


<!--                    TABLE WRAP FOOTER                          -->
<!--                    Wrapper element to hold a group of footnotes
                        or other notes or general paragraphs at the
                        end of a table.  Not the same as the
                        Table Foot <tfoot>, which contains rows
                        and columns like the rest of the table.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=table-wrap-foot
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=table-wrap-foot
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=table-wrap-foot
                                                                   -->
<!ELEMENT  table-wrap-foot
                        %table-wrap-foot-model;                      >
<!ATTLIST  table-wrap-foot
             %table-wrap-foot-atts;                                  >


<!-- ================== End Display Class Module ================= -->
<!-- ============================================================= -->
<!--  MODULE:    MathML DTD SETUP MODULE                           -->
<!--  VERSION:   2.0                                               -->
<!--  DATE:      August 2004                                       -->
<!--                                                               -->
<!-- ============================================================= -->

<!-- ============================================================= -->
<!--                    PUBLIC DOCUMENT TYPE DEFINITION            -->
<!--                        TYPICAL INVOCATION                     -->
<!--
"-//NLM//DTD Archiving and Interchange DTD Suite MathML Setup Module v2.0 20040830//EN"
     Delivered as file "mathmlsetup.ent"                           -->
<!-- ============================================================= -->

<!-- ============================================================= -->
<!-- SYSTEM:     Archiving and Interchange DTD Suite               -->
<!--                                                               -->
<!-- PURPOSE:    Provides the organization for using the           -->
<!--             MathML DTD                                        -->
<!--                                                               -->
<!-- CONTAINS:   1) Overrides to standard parameter entities used  -->
<!--                in the MathML 2.0 DTD                          -->
<!--             2) Call to MathML 2.0 DTD                         -->
<!--                                                               -->
<!-- MODULES REQUIRED:                                             -->
<!--             1) mathml2.dtd           MathML 2.0 DTD           -->
<!--                                                               -->
<!-- CREATED FOR:                                                  -->
<!--             Digital archives and publishers who wish to       -->
<!--             create a custom XML DTD for original markup of    -->
<!--             journal literature, books, and related material,  -->
<!--             or for archiving and transferring such material   -->
<!--             between archives.                                 -->
<!--                                                               -->
<!--             This DTD is in the public domain. An organization -->
<!--             that wishes to create its own DTD from the suite  -->
<!--             may do so without permission from NLM.            -->
<!--                                                               -->
<!--             The suite has been set up to be extended using a  -->
<!--             new DTD file and a new DTD-specific customization -->
<!--             module to redefine the many Parameter Entities.   -->
<!--             Do not modify the suite directly or redistribute  -->
<!--             modified versions of the suite.                   -->
<!--                                                               -->
<!--             In the interest of maintaining consistency and    -->
<!--             clarity for potential users, NLM requests:        -->
<!--                                                               -->
<!--             1. If you create a DTD from the Archiving and     -->
<!--                Interchange DTD Suite and intend to stay       -->
<!--                compatible with the suite, then please include -->
<!--                the following statement as a comment in all of -->
<!--                your DTD modules:                              -->
<!--                   "Created from, and fully compatible with,   -->
<!--                    the Archiving and Interchange DTD Suite."  -->
<!--                                                               -->
<!--             2. If you alter one or more modules of the suite, -->
<!--                then please rename your version and all its    -->
<!--                modules to avoid any confusion with the        -->
<!--                original suite. Also, please include the       -->
<!--                following statement as a comment in all your   -->
<!--                DTD modules:                                   -->
<!--                   "Based in part on, but not fully compatible -->
<!--                    with, the Archiving and Interchange DTD    --> 
<!--                    Suite."                                    -->
<!--                                                               -->
<!--             Suggestions for refinements and enhancements to   -->
<!--             the DTD suite should be sent in email to:         -->
<!--                 <EMAIL>                  -->
<!--                                                               -->
<!-- ORIGINAL CREATION DATE:                                       -->
<!--             December 2002                                     -->
<!--                                                               -->
<!-- CREATED BY: Jeff Beck       (NCBI)                            -->
<!--             Deborah Lapeyre (Mulberry Technologies, Inc.)     -->
<!--             Bruce Rosenblum (Inera Inc.)                      -->
<!--                                                               -->
<!--             NLM thanks the Harvard University Libraries, both -->
<!--             for proposing that a draft archiving NLM DTD for  --> 
<!--             life sciences journals be extended to accommodate -->
<!--             journals in all disciplines and for sponsoring    -->
<!--             Bruce Rosenblum's collaboration with other DTD    -->
<!--             authors in completing Version 1.0. The Andrew W.  --> 
<!--             Mellon Foundation provided support for these      --> 
<!--             important contributions.                          -->
<!--                                                               -->
<!-- ============================================================= -->


<!-- ============================================================= -->
<!--                    DTD VERSION/CHANGE HISTORY                 -->
<!-- ============================================================= -->
<!--
     =============================================================

Version  Reason/Occasion                   (who) vx.x (yyyy-mm-dd)

     =============================================================
     Version 2.0                       (DAL/BTU) v2.0 (2004-08-30)
          
     Major requirement changes led to the new release, producing
     DTD version "2.0":
       a) The splitting of the Archival and Interchange Tag Set 
          DTDs into three DTDs from two: an authoring DTD, an
          archive regularization and interchange DTD (the
          current Blue Publishing DTD), and a preservationist 
          archive DTD (the current Green Archiving and Interchange 
          DTD).
       b) AIT Working Group suggestions from the June 04 meeting 
          and June/July 2004 followup discussions
       c) Suite remodularization to meet new (and newly articulated)
          modularization requirements
       d) New or renamed classes and mixes to make modifications
          easier and more consistent
  
  2. Updated public identifier to "v2.0 20040830"          

     =============================================================
     Version 1.1                           (TRG) v1.1 (2003-11-01)

  1. Added attribute "alternate-form-of" to <mml:math> 
     (Note: parameter entity %math.qname; resolves to <mml:math>.)
     Rationale: Where multiple formats of an item (e.g., graphic 
     file, media object, chemical structure) are available, this 
     attribute indicates that a format is a secondary one and 
     provides a link to the primary format, so that only one 
     format of an item is displayed.
                                                                   -->


<!-- ============================================================= -->
<!--                    SET UP FOR THE MathML MODULE               -->
<!-- ============================================================= -->


<!--                    MathML DTD                                 -->
<!--                    The official version of the MathML 2.0 can 
                        be found at: http://www.w3.org/TR/MathML2/

                        See also Mathematical Markup Language  
                        (MathML) Version 2.0
                       
                        A. Parsing MathML
                        A.6 The MathML DTD
            http://www.w3.org/TR/MathML2/appendixa.html#parsing-dtd

                        A zip file with entity declarations is 
                        available from
            http://www.w3.org/TR/MathML2/DTD-MathML-20010221.zip   -->


<!-- ============================================================= -->
<!--                    PARAMETER ENTITY OVERRIDES FOR MathML 2.0  -->
<!-- ============================================================= -->


<!ENTITY % MATHML.prefixed "INCLUDE"                                 >


<!ENTITY % MATHML.prefix   "mml"                                     >


<![%MATHML.prefixed;[
<!ENTITY % MATHML.pfx      "%MATHML.prefix;:"                        >
]]>


<!ENTITY % math.qname      "%MATHML.pfx;math"                        >


<!--         alternate-form-of
                        Where multiple formats of an item (e.g., 
                        graphic file, media object, chemical 
                        structure) are available, this attribute 
                        indicates that a format is a secondary 
                        one and provides a link to the primary 
                        format, so that only one format of an 
                        item is displayed.                         -->
<!ATTLIST  %math.qname;
             alternate-form-of
                        IDREF                              #IMPLIED  >


<!-- ============================================================= -->
<!--                    MathML 2.0 INVOCATION                      -->
<!-- ============================================================= -->

%mathml.dtd;


<!-- ================== End MATHML Setup Module ================== -->

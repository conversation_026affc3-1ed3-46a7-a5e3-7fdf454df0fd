<!-- ============================================================= -->
<!--  MODULE:    Journal Archiving DTD Customize Mixes Module      -->
<!--  VERSION:   ANSI/NISO JATS Version 1.3 (Z39.96-2021)          -->
<!--  DATE:      June 2021                                         -->
<!--                                                               -->
<!-- ============================================================= -->

<!-- ============================================================= -->
<!--                    PUBLIC DOCUMENT TYPE DEFINITION            -->
<!--                        TYPICAL INVOCATION                     -->
<!--
"-//NLM//DTD JATS (Z39.96) Journal Archiving DTD Customize Mixes Module v1.3 20210610//EN"
Delivered as file "JATS-archivecustom-mixes1-3.ent"                -->
<!-- ============================================================= -->

<!-- ============================================================= -->
<!-- SYSTEM:     Journal Archiving and Interchange DTD of the      --> 
<!--             JATS DTD Suite                                    -->         
<!--                                                               -->
<!-- PURPOSE:    To declare the Parameter Entities (PEs) used to   -->
<!--             over-ride the JATS DTD Suite default named,       -->
<!--             general purpose mixes. (Mixes for particular      -->
<!--             elements are declared in the Archive Custom       -->
<!--             Models module.)                                   -->
<!--                                                               -->
<!--             Note: Since PEs must be declared before they      -->
<!--             are used, this module must be called before the   -->
<!--             default mixes modules (%default-mixes;)           -->
<!--                                                               -->
<!-- TAG SET SPONSOR                                               -->
<!--             National Center for Biotechnology                 -->
<!--                Information (NCBI)                             -->
<!--             National Library of Medicine (NLM)                -->
<!--                                                               -->
<!-- CREATED FOR:                                                  -->
<!--             This DTD was created from the JATS DTD Suite.     -->
<!--             Digital archives and publishers may use the       -->
<!--             DTD as is for markup of journal literature or     -->
<!--             related material for archiving and transferring   -->
<!--             such material between archives or create a        -->
<!--             custom XML DTD from the Suite for                 -->
<!--             these purposes.                                   -->
<!--                                                               -->
<!--             This DTD is in the public domain. An organization -->
<!--             that wishes to create its own DTD from the suite  -->
<!--             may do so without permission from NLM.            -->
<!--                                                               -->
<!--             The suite has been set up to be extended using a  -->
<!--             new DTD file and a new DTD-specific customization -->
<!--             module to redefine the many Parameter Entities.   -->
<!--             Do not modify the suite directly or redistribute  -->
<!--             modified versions of the suite.                   -->
<!--                                                               -->
<!--             In the interest of maintaining consistency and    -->
<!--             clarity for potential users, NLM requests:        -->
<!--                                                               -->
<!--             1. If you create a DTD from the JATS DTD Suite    -->
<!--                and intend to stay compatible with the suite,  -->
<!--                then please include the following statement    -->
<!--                as a comment in all of your DTD modules:       -->
<!--                   "Created from, and fully compatible with,   -->
<!--                    the ANSI/NISO Z39.96 Journal Article Tag   -->
<!--                    Suite (JATS)."                             -->
<!--                                                               -->
<!--             2. If you alter one or more modules of the suite, -->
<!--                then please rename your version and all its    -->
<!--                modules to avoid any confusion with the        -->
<!--                original suite. Also, please include the       -->
<!--                following statement as a comment in all your   -->
<!--                DTD modules:                                   -->
<!--                   "Based in part on, but not fully compatible -->
<!--                    with, the ANSI/NISO Z39.96 Journal Article -->
<!--                    Tag Suite (JATS)."                         -->
<!--                                                               -->
<!-- ORIGINAL CREATION DATE:                                       -->
<!--             August 2004                                       -->
<!--                                                               -->
<!-- CREATED BY: Mulberry Technologies, Inc. for the NISO Z39.96   -->
<!--             Working Group. Mulberry Technologies was          -->
<!--             supported by National Center for Biotechnology    -->
<!--             Information (NCBI), a center of the US National   -->
<!--             Library of Medicine (NLM).                        -->
<!--                                                               -->
<!--             The Journal Archiving and Interchange DTD is      -->
<!--             built from the JATS DTD Suite.                    -->
<!--                                                               -->
<!--             This DTD and the Suite are a continuation of      -->
<!--             the work done by NCBI, Mulberry Technologies,     -->
<!--             and Inera Inc. on the NLM Journal Archiving       -->
<!--             and Interchange DTD Suite, which was originally   -->
<!--             released in December, 2002.                       -->
<!--                                                               -->
<!--             NLM thanks the Harvard University Libraries, both -->
<!--             for proposing that a draft archiving NLM DTD for  -->
<!--             life sciences journals be extended to accommodate -->
<!--             journals in all disciplines and for sponsoring    -->
<!--             Inera Inc.'s collaboration with other DTD         -->
<!--             authors in completing NLM Version 1.0. The        -->
<!--             Andrew W. Mellon Foundation provided support for  -->
<!--             these important contributions.                    -->
<!--                                                               -->
<!--             Suggestions for refinements and enhancements to   -->
<!--             this DTD should be sent in email to:              -->
<!--                 <EMAIL>                         -->
<!-- ============================================================= -->


<!-- ============================================================= -->
<!--                    DTD VERSION/CHANGE HISTORY                 -->
<!-- ============================================================= -->
<!--
Version  Reason/Occasion                   (who) vx.x (yyyy-mm-dd)
   ==============================================================
     JATS Version 1.3 (ANSI/NISO Z39.96-2021)
                                  (DAL/BTU) v1.3 (2021-06-10)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by both the NISO and ANSI memberships 
     to be changed. This version of the NISO JATS was approved 
     by ANSI vote on June 10, 2021, so it supersedes all Committee 
     Drafts as well as the draft version JATS 1.3 2021 (the version
     submitted for vote), becoming NISO JATS v1.3 20210610 
     (ANSI/NISO Z39.96-2019).

     The file names and formal public identifiers have been 
     changed in the modules and the catalogs.

 19. JATS 'v1.3d2 20201130' becomes 'v1.3 20210610' 
     (final ANSI vote date)
     'ANSI/NISO Z39.96-2019' becomes 'ANSI/NISO Z39.96-2021'

  ==============================================================
     JATS Version 1.3d2 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.3d2 (2020-11-30)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by the NISO and ANSI memberships 
     to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     of the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting; it is for Standing Committee review and
     testing. The formal public identifiers have been changed 
     in the modules and the catalogs.
 
 18. TABLE CELLS - Added new elements to <td> using %inside.cell;
     and <entry> using %Flowmix;:
        <disp-quote>, <speech>, <statement>, <verse-group>

 17. JATS "v1.3d2 20201130" becomes 'v1.3 20210610' (second 
     committee draft following the final ANSI/NISO 1.2 vote)

   ==============================================================
     JATS Version 1.3d1 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.3.d1 (2019-08-31)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by the NISO and ANSI memberships 
     to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     of the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
    
 16. QUESTIONS AND ANSWERS

     - NEW PE for ANSWERS  - Added new mix "answer para level"
       to name the elements that may be used at the same
       structural level as a paragraph, when used inside an 
       <answer>. This rather awkward parameter entity was created
       to overcome the DTD determinism issues when using 
       <explanation> inside an <answer>. In order for 
       <explanation> to be used at the end of an answer, 
       it must not be allowed at the paragraph level
       inside <answer>.
     
     - INSIDE TABLE CELLS - Added the question answer class
       to the contents of table cell using inside cell mix.
 
 15. JATS "1.2" becomes "v1.3d1 20190831" (first committee
     draft following the final ANSI/NISO vote) 

   ==============================================================
     JATS Version 1.2 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.2     (2019-02-08)
    
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the both the NISO and ANSI membership 
     to be changed. This version of the NISO JATS was approved 
     by ANSI vote on Feb 08, 2019, so it supersedes all Committee 
     Drafts as well as the draft version JATS 1.2 2018 (the version
     submitted for vote), becoming NISO JATS v1.2 20190208 
     (ANSI/NISO Z39.96-2021).
  
 14. JATS "1.2" becomes "v1.2 20190208" (final ANSI vote date)
     "ANSI/NISO Z39.96-2015" becomes "ANSI/NISO Z39.96-2019"

   ==============================================================
     JATS Version 1.2 (ANSI/NISO Z39.96-2018)
                                   (DAL/BTU) v1.2     (2018-11-30)
    
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the both the NISO and ANSI membership 
     to be changed. This version of the NISO JATS was approved 
     by ANSI and NISO vote, so it supersedes all Committee Drafts
     and becoming NISO JATS 1.2 (ANSI/NISO Z39.96-2018).
  
 13. JATS "1.2d2" and "v1.2d2 20180401" became
     JATS "1.2" and "v1.2 20181130" 

    =============================================================
     JATS Version 1.2d2              (DAL/BTU) v1.2d2  (2018-04-01)

     JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
     BITS is under continuous maintenance, and is modified at the
     discretion of the working group.
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.

 12. BITS "2.0" and "v2.0 20151225" remain unchanged
      
   
     JATS "1.2d1" and "v1.2d1 20171231" became
     JATS "1.2d2" and "v1.2d2 20180401". 

     No module names were changed. 
     
    =============================================================
     JATS Version 1.2d1 (ANSI/NISO Z39.96-2015)
                                   (DAL/BTU) v1.2d1  (2017-12-31)
 
     NISO JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
  
 11. JATS "1.2d1" and "v1.2d1 20170631" became
     JATS "1.2d1" and "v1.2d1 20171231". 

    =============================================================
     JATS Version 1.2d1              (DAL/BTU) v1.2d1  (2017-06-31)

     NISO JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
 
 10. JATS became version "1.2d1" and "v1.2d1 20170631" 

    =============================================================
     JATS Version 1.1                (DAL/BTU) v1.1  (2015-12-15)

     JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
     JATS 1.1 was approved in late 2015, so the formal public 
     identifiers were changed in the modules and the catalogs.
     No model or attribute changes were made at this time. 
 
  9. JATS became version "1.1" and "v1.1 20151215"
     BITS remained version "2.0" but becomes "v2.0 20151225"

     =============================================================
     JATS Version 1.1d3             (DAL/BTU) v1.1d3  (2015-03-01)
   
     The changes in this release are in response to NISO 
     License and Indicators Recommended Practice.

     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the NISO membership to be changed. This
     Committee Draft 1.1d3 will be sent to the NISO voting
     membership, to become (if approved) NISO JATS 1.1.
     
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee.

  8. JATS became version "1.1d3" and "v1.1d3 20150301"

     =============================================================
     JATS Version 1.1d2              (DAL/BTU) v1.1d2 (2014-09-30)
   
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the NISO membership to be changed. This
     Committee Draft 1.1d2 will be sent to the NISO voting
     membership, to become (if approved) NISO JATS 1.1.
     
     This catalog represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee.

  7. JATS became version "1.1d2" and "v1.1d2 20140930//EN"
   
     =============================================================
     NISO JATS Version 1.1d1           (DAL/BTU) v1.1 (2013-11-15)
   
     ANSI/NISO Z39.96-2012 (pre-release for V1.0 BITS; Version 1.1d1) 

     NISO JATS Standing Committee met and answered the requests and
     suggestions from the NISO request forms.

     Details concerning ANSI/NISO Z39.96-2012 JATS-based DTDs,  
     XSDs, RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.3d2/

  6. PARAGRAPH IN TABLE CELL - Added <p> to the contents of
     table cells, through %inside-cell; (using
     %nothing-but-para.class;).
  
     =============================================================
     NISO JATS Version 1.0             (DAL/BTU) v1.0 (2012-xx-xx)
   
     ANSI/NISO Z39.96-2012 (Version 1.0) 

     Details concerning ANSI/NISO Z39.96-2012 JATS-based DTDs,  
     XSDs, RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.3d2/
   
  5. RELATED OBJECT - Added <related-object> everywhere
     <related-article> was used.
   
  4. PARA-LEVEL PARAMETER ENTITY CONFLICT - Changed the parameter
     entity %para-level; to use the PE %nothing-but-para.class;,
     removing the similar PE "just-para.class;". It is possible to 
     add element(s) to %just-para.class;, so that the added element(s)
     may be used everywhere <p> is used. Such elements would also be
     named in %block-display.class;. This would lead to a conflict
     in %para-level; if both %just-para.class; and
     %block-display.class; named <p>. So %just-para.class; is replaced
     here in %para-level;. This is backward compatible for all XML
     documents. If a JATS customization has modified %just-para.class;
     they may need to change their customization to accommodate.

     =============================================================
     Version 0.4                       (DAL/BTU) v0.4 (2011-01-31)
   
     This Tag Set is in the process of becoming a NISO standard.
     The version numbers are starting over from 0.4", as a Trial
     Use Draft, to be made into "Version 1.0" when the Tag Suite 
     becomes a NISO standard. Thus, the version number that would
     have been "NLM Version 3.1 Draft" has become NISO JATS
     "Version 0.4".

     Details on NISO Trial Use Draft Version 0.4 are available at
           http://jats.nlm.nih.gov/JATS-0.4.
   
  3. Updated the DTD-version attribute to "0.4" 
   
  2. Updated the public identifier to "v0.4 20110131", 
     modified the formal public identifier to include "JATS (Z39.96)",
     and the filename as delivered to include "JATS" and the
     new version number "0".
           http://jats.nlm.nih.gov/0.4.

     =============================================================
     Version 3.0                       (DAL/BTU) v3.0 (2007-10-31)

     Version 3.0 is the first non-backward-compatible release.
     In addition to the usual incremental changes, some
     elements and attributes have been renamed and/or remodeled
     to better meet user needs and to increase tag set consistency.
     All module change histories are available through the Tag Suite
     web site at http://dtd.nlm.nih.gov.

     Details on version 3.0 are available at
           http://dtd.nlm.nih.gov/3.0.

  1. Updated public identifier to "v3.0 20071031//EN"              -->

<!-- ============================================================= -->
<!-- Details concerning 
     ANSI/NISO Z39.96-2015 JATS-based DTDs, XSDs, 
     RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.1/                            -->

<!-- ============================================================= -->
<!--                    ELEMENT MIXES FOR USE IN CONTENT MODELS    -->
<!--                    (MIXES ARE COMPOSED USING CLASSES)         -->
<!-- ============================================================= -->


<!-- ============================================================= -->
<!--                    EXCEPTION: A MIX USED IN OTHER MIXES       -->
<!-- ============================================================= -->


<!--                    ALL PHRASE-LEVEL ELEMENTS                  -->
<!--                    This Parameter Entity contains all of the
                        phrase-level elements in the entire
                        Archival Tag Set EXCEPT THE <break> element.
                        MAINTENANCE NOTE:
                        Since this is used inside other mixes
                        (like a class and unlike all other mixes)
                        all-phrase must
                          - be declared first in this module.
                          - does not start with an OR bar, as all
                            other inline mixes do                  -->
<!ENTITY % all-phrase   "%address-link.class; | %article-link.class; |
                         %appearance.class; | %emphasis.class;  |
                         %inline-display.class; |
                         %inline-math.class; | %math.class; |
                         %phrase.class; | %simple-link.class; |
                         %subsup.class; | %x.class;"                 >


<!-- ============================================================= -->
<!--                    TABLE ELEMENT MIXES                        -->
<!-- ============================================================= -->


<!--                    INSIDE TABLE CELL ELEMENTS                 -->
<!--                    Mixed with #PCDATA inside a table cell, such
                        as a <td> or <th> element in the XHTML table
                        model, the <entry> element in the OASIS CALS
                        table model, etc.  This PE will be used as the
                        value of %Flow.mix;, %paracon;, etc.
                        MAINTENANCE NOTE: Inside cell is an exception,
                        an inline mix that does not start with an OR
                        bar. This is because is used within the
                        PE -%Flow.mix;, which is an inline mix
                        defined in the course of the XHTML Table DTD,
                        a DTD not under control of this DTD Suite. -->
<!ENTITY % inside-cell  "%all-phrase; | %block-math.class; |
                         %break.class; | %chem-struct-wrap.class; |
                         %citation.class; | %intable-para.class; |
                         %list.class; | %nothing-but-para.class; | 
                         %question-answer.class; |
                         %simple-display-noalt.class;"               >


<!-- ============================================================= -->
<!--                    BACK MATTER ELEMENT MIXES(%backmatter.ent;)-->
<!-- ============================================================= -->


<!--                    DOCUMENT BACK MATTER ELEMENTS              -->
<!--                    Back Matter Elements used by a full document
                        such as a journal article. This is an element
                        grouping rather than a class. These
                        elements may also appear in the content models
                        of structural elements, such as back matter.
                                                                   -->
<!ENTITY % doc-back-matter-mix
                        "%back.class; | %front-back.class; |
                         %sec.class;"                                >


<!-- ============================================================= -->
<!--                    PARAGRAPH-LEVEL ELEMENT MIXES              -->
<!-- ============================================================= -->


<!--                    PARAGRAPH-LEVEL ELEMENTS                   -->
<!--                    Elements that may be used at the same
                        structural level as a paragraph, for
                        example inside a Section
                        Note: There a major overlap between this
                        parameter entity and that for the elements
                        that are at the same level as a paragraph.
                        Inline elements appear only inside a
                        paragraph, but block elements such as quotes
                        and lists may appear either within a
                        paragraph or at the same level as a
                        paragraph. This serves a requirement in a
                        repository DTD, since some incoming material
                        will have restricted such elements to only
                        inside a paragraph,  some incoming material
                        will have restricted them to only outside a
                        paragraph and some may allow them in both
                        places. Thus the DTD must allow for them to
                        be in either or both.                      -->
<!ENTITY % para-level   "%block-display.class; | %block-math.class; |
                         %list.class; | %math.class; | 
                         %nothing-but-para.class; |
                         %related-article.class; |
                         %rest-of-para.class; | %x.class;"           >


<!--                    INSIDE AN ANSWER PARAGRAPH-LEVEL ELEMENTS  -->
<!--                    The elements that may be used at the same
                        structural level as a paragraph, when 
                        used inside an <answer>.
                        Remarks: This rather awkward parameter entity
                        was created to overcome the DTD determinism
                        issues when using <explanation> inside
                        an <answer>. In order for <explanation> to
                        be used at the end of an answer, it must
                        not be allowed at the paragraph level inside
                        <answer>.                                  -->
<!ENTITY % answer-para-level   
                        "%block-display-minus-explanation.class; | 
                         %block-math.class; |
                         %list.class; | %math.class; |
                         %nothing-but-para.class; |
                         %related-article.class; |
                         %rest-of-para.class; | %x.class;"           >



<!-- ============================================================= -->
<!--                    INLINE ELEMENT MIXES                       -->
<!-- ============================================================= -->


<!--                    EMPHASIS MIX ELEMENTS                      -->
<!--                    Elements that may be used inside most of the
                        emphasis class elements                    -->
<!ENTITY % emphasized-text
                        "| %all-phrase; | %break.class;"             >


<!--                    JUST RENDITION                             -->
<!--                    Only the simplest of the typographic
                        emphasis elements, as well as subscript and
                        superscript.  Usually used in a model that
                        allows #PCDATA and this restricted mixture.
                        This mix may be stripped down to only
                        subscript and superscript by some, more
                        restrictive DTDs.
                        MAINTENANCE NOTE:  This Parameter Entity
                        and the related PE "rendition-plus" have
                        been put in place to restrict the amount of
                        variability that a person modifying the DTD
                        through PE redefinition can achieve. Some
                        elements have been set #PCDATA plus one PE
                        and some have been set to #PCDATA plus the
                        other in an effort to allow designers to
                        modify entire groups of elements, but not
                        to change similar models individually .    -->
<!ENTITY % just-rendition
                        "| %all-phrase;"                             >


<!--                    RENDITION MARKUP PLUS                      -->
<!--                    Only the simplest of the typographic
                        emphasis elements, as well as subscript and
                        superscript.  Usually used in a model that
                        allows #PCDATA and this restricted mixture.
                        This mix may be enhanced slightly in some
                        more permissive DTDs, and should always
                        contain at least typographic emphasis,
                        subscript, and superscript.
                        MAINTENANCE NOTE: This Parameter Entity
                        and the related PE "just-rendition" have
                        been put in place to restrict the amount of
                        variability that a person modifying the DTD
                        through PE redefinition can achieve. Some
                        elements have been set #PCDATA plus one PE
                        and some have been set to #PCDATA plus the
                        other in an effort to allow designers to
                        modify entire groups of elements, but not
                        to individually change similar models.
                        modify entire groups of elements, but not
                        to change similar models individually .    -->
<!ENTITY % rendition-plus
                        "| %all-phrase;"                             >


<!--                    SIMPLE PHRASE-LEVEL TEXTUAL ELEMENTS       -->
<!--                    Elements that may be used almost anywhere
                        text is used, for example, inside a title.
                        Simple text plus inline display and math
                        elements.                                  -->
<!ENTITY % simple-phrase
                        "| %all-phrase;"                             >


<!--                    SIMPLE TEXTUAL CONTENT                     -->
<!--                    Elements that may be used inside elements
                        that are really expected to be #PCDATA and
                        not to contain any of these things.
                        Note that in the original, this contained
                        no math and no links, thus is was even
                        simpler than %simple-phrase; (As of 2004,
                        the two are the same.) s                    -->
<!ENTITY % simple-text  "| %all-phrase;"                             >


<!-- ================== End Archiving DTD Mixes Customization ==== -->

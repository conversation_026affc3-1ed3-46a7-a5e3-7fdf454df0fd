# system_test.py
"""
系统完整性测试
测试新的专业写作方法与所有组件的集成
验证语言纯净性和真实数据处理
"""

import os
import logging
import time
from typing import Dict, List, Any

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_professional_writing_integration():
    """测试专业写作器的完整集成"""
    print("🧪 开始系统完整性测试")
    print("=" * 50)
    
    try:
        # 1. 测试模块导入
        print("\n📦 测试模块导入...")
        from llm_manager import LLMManager
        from ebm_generator import EBMGenerator
        from professional_ebm_writer import ProfessionalEBMWriter
        from literature_search import SearchService
        print("✅ 所有核心模块导入成功")
        
        # 2. 测试LLM管理器
        print("\n🤖 测试LLM管理器...")
        llm_manager = LLMManager()
        
        # 测试generate_content方法
        test_prompt = "你是医学专家。请简要介绍循证医学的概念。"
        try:
            response = llm_manager.generate_content(test_prompt, "zhipuai", "glm-4-flash")
            if response and not response.startswith("ERROR"):
                print("✅ LLM内容生成功能正常")
            else:
                print(f"⚠️ LLM响应异常: {response}")
        except Exception as e:
            print(f"❌ LLM测试失败: {e}")
        
        # 3. 测试专业写作器
        print("\n✍️ 测试专业写作器...")
        writer = ProfessionalEBMWriter(llm_manager)
        
        # 创建测试数据
        test_studies = create_test_studies()
        
        # 测试标题生成
        try:
            title_cn = writer.generate_professional_title("脓毒血症治疗", test_studies, True)
            title_en = writer.generate_professional_title("Sepsis Treatment", test_studies, False)
            
            print(f"✅ 中文标题: {title_cn}")
            print(f"✅ 英文标题: {title_en}")
            
            # 检查语言纯净性
            if writer._contains_english(title_cn):
                print("⚠️ 中文标题包含英文内容")
            if writer._contains_chinese(title_en):
                print("⚠️ 英文标题包含中文内容")
                
        except Exception as e:
            print(f"❌ 标题生成测试失败: {e}")
        
        # 4. 测试EBM生成器集成
        print("\n📊 测试EBM生成器集成...")
        ebm_generator = EBMGenerator(llm_manager)
        
        # 检查新方法是否存在
        required_methods = [
            'generate_professional_report',
            '_generate_narrative_with_professional_writer',
            'professional_writer'
        ]
        
        for method in required_methods:
            if hasattr(ebm_generator, method):
                print(f"✅ {method} 方法存在")
            else:
                print(f"❌ {method} 方法缺失")
        
        # 5. 测试完整报告生成流程
        print("\n📝 测试完整报告生成...")
        try:
            # 测试综述生成（避免重复质量评估）
            narrative_content = ebm_generator._generate_narrative_with_professional_writer(
                "脓毒血症治疗研究", test_studies, "zhipuai", "glm-4-flash", True
            )
            
            if narrative_content and len(narrative_content) > 100:
                print("✅ 综述内容生成成功")
                
                # 检查语言纯净性
                if writer._contains_english(narrative_content):
                    print("⚠️ 中文综述包含英文内容")
                else:
                    print("✅ 中文综述语言纯净")
                    
            else:
                print("⚠️ 综述内容生成异常")
                
        except Exception as e:
            print(f"❌ 报告生成测试失败: {e}")
        
        # 6. 测试数据流完整性
        print("\n🔄 测试数据流完整性...")
        try:
            # 测试数据特征提取
            characteristics = writer._extract_study_characteristics(test_studies)
            
            print(f"✅ 提取研究特征: {characteristics.total_studies}项研究")
            print(f"✅ 研究设计: {list(characteristics.study_designs.keys())}")
            print(f"✅ 样本量统计: {characteristics.sample_sizes}")
            
        except Exception as e:
            print(f"❌ 数据流测试失败: {e}")
        
        # 7. 测试可视化集成
        print("\n📈 测试可视化集成...")
        try:
            import sys
            sys.path.append('extensions/visualization')
            from data_visualizer import DataVisualizer
            
            visualizer = DataVisualizer()
            print("✅ 数据可视化模块加载成功")
            
        except Exception as e:
            print(f"⚠️ 可视化模块测试: {e}")
        
        print("\n🎉 系统完整性测试完成！")
        return True
        
    except Exception as e:
        print(f"\n❌ 系统测试失败: {e}")
        return False


def create_test_studies() -> List[Dict[str, Any]]:
    """创建测试用的研究数据"""
    return [
        {
            'id': 'study_001',
            'title': 'Efficacy of early goal-directed therapy in sepsis patients: a randomized controlled trial',
            'authors': ['Smith J', 'Johnson A', 'Brown K'],
            'year': '2023',
            'abstract': 'Background: Sepsis remains a leading cause of mortality in intensive care units. Early goal-directed therapy (EGDT) has shown promise in improving outcomes. Methods: We conducted a randomized controlled trial with 200 sepsis patients. Results: EGDT group showed significant reduction in 28-day mortality (p<0.05). Conclusions: Early intervention improves sepsis outcomes.',
            'clinical_data': {
                'study_design': 'Randomized Controlled Trial',
                'population': 'Adult sepsis patients in ICU',
                'intervention': 'Early goal-directed therapy',
                'comparison': 'Standard care',
                'outcomes': 'Mortality, length of stay',
                'sample_size': 200
            },
            'journal': 'Critical Care Medicine',
            'doi': '10.1000/test.001'
        },
        {
            'id': 'study_002',
            'title': 'Biomarkers for early detection of sepsis: a prospective cohort study',
            'authors': ['Davis M', 'Wilson R'],
            'year': '2023',
            'abstract': 'Objective: To evaluate biomarkers for early sepsis detection. Design: Prospective cohort study of 150 patients. Main results: Procalcitonin and lactate showed high sensitivity for sepsis diagnosis. Conclusion: Combined biomarkers improve early detection.',
            'clinical_data': {
                'study_design': 'Cohort Study',
                'population': 'Emergency department patients',
                'intervention': 'Biomarker testing',
                'comparison': 'Clinical assessment alone',
                'outcomes': 'Diagnostic accuracy',
                'sample_size': 150
            },
            'journal': 'Emergency Medicine Journal',
            'doi': '10.1000/test.002'
        },
        {
            'id': 'study_003',
            'title': 'Antimicrobial stewardship in sepsis management: systematic review',
            'authors': ['Lee S', 'Chen L', 'Wang X'],
            'year': '2024',
            'abstract': 'Background: Appropriate antimicrobial use is crucial in sepsis. Objective: To review antimicrobial stewardship programs. Methods: Systematic review of 25 studies. Results: Stewardship programs reduced mortality and antibiotic resistance. Conclusions: Implementation improves outcomes.',
            'clinical_data': {
                'study_design': 'Systematic Review',
                'population': 'Sepsis patients',
                'intervention': 'Antimicrobial stewardship',
                'comparison': 'Standard antimicrobial therapy',
                'outcomes': 'Mortality, resistance rates',
                'sample_size': 2500
            },
            'journal': 'Infectious Diseases Review',
            'doi': '10.1000/test.003'
        }
    ]


def test_language_purity():
    """测试语言纯净性功能"""
    print("\n🌐 测试语言纯净性...")
    
    try:
        from professional_ebm_writer import ProfessionalEBMWriter
        from llm_manager import LLMManager
        
        writer = ProfessionalEBMWriter(LLMManager())
        
        # 测试中文内容检测
        chinese_text = "这是一篇关于脓毒血症的研究，包含了RCT和GRADE评估。"
        english_text = "This is a study about sepsis treatment with RCT design and GRADE assessment."
        mixed_text = "这是一篇关于sepsis treatment的研究，使用了randomized controlled trial设计。"
        
        print(f"中文文本包含英文: {writer._contains_english(chinese_text)}")
        print(f"英文文本包含中文: {writer._contains_chinese(english_text)}")
        print(f"混合文本包含英文: {writer._contains_english(mixed_text)}")
        print(f"混合文本包含中文: {writer._contains_chinese(mixed_text)}")
        
        print("✅ 语言纯净性检测功能正常")
        
    except Exception as e:
        print(f"❌ 语言纯净性测试失败: {e}")


def generate_test_report():
    """生成测试报告"""
    print("\n📋 生成测试报告...")
    
    try:
        from ebm_generator import EBMGenerator
        from llm_manager import LLMManager
        
        llm_manager = LLMManager()
        ebm_generator = EBMGenerator(llm_manager)
        
        test_studies = create_test_studies()
        
        # 生成测试报告
        md_file, html_file = ebm_generator.generate_professional_report(
            topic="脓毒血症早期诊断和治疗",
            articles_for_report=test_studies,
            report_type="narrative",
            provider="zhipuai",
            model="glm-4-flash",
            is_chinese=True
        )
        
        print(f"✅ 测试报告已生成: {md_file}")
        
        # 检查文件内容
        if os.path.exists(md_file):
            with open(md_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print(f"📄 报告长度: {len(content)} 字符")
            
            # 检查语言纯净性
            from professional_ebm_writer import ProfessionalEBMWriter
            writer = ProfessionalEBMWriter(llm_manager)
            
            if writer._contains_english(content):
                print("⚠️ 中文报告包含英文内容")
            else:
                print("✅ 中文报告语言纯净")
        
        return md_file
        
    except Exception as e:
        print(f"❌ 测试报告生成失败: {e}")
        return None


if __name__ == "__main__":
    print("🚀 启动系统完整性测试")
    
    # 运行主要测试
    success = test_professional_writing_integration()
    
    # 测试语言纯净性
    test_language_purity()
    
    # 生成测试报告
    test_file = generate_test_report()
    
    print("\n📊 测试总结:")
    print(f"✅ 主要集成测试: {'通过' if success else '失败'}")
    print(f"✅ 测试报告: {'已生成' if test_file else '生成失败'}")
    
    if success and test_file:
        print("\n🎉 所有测试通过！新的专业写作方法已完美集成。")
    else:
        print("\n⚠️ 部分测试失败，需要进一步调试。")

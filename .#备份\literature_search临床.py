# literature_search.py
import logging
import re
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
import concurrent.futures

import arxiv
from Bio import Entrez
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type, wait_random_exponential, retry_if_exception
import requests

# 确保这些模块存在且可导入
from config import settings
from llm_manager import LLMManager
from prompts import PROMPTS_EN

# --- Concurrent Execution Imports ---
from concurrent.futures import ThreadPoolExecutor, as_completed, Future

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class SmartQuery:
    def __init__(self, raw_query: str, llm_manager: LLMManager, llm_provider: str, llm_model: str):
        self.raw_query = raw_query
        self.llm = llm_manager
        self.llm_provider = llm_provider
        self.llm_model = llm_model
        self.strategies = self._create_strategies()
        self.core_keyword = self._extract_core_keyword(self.raw_query)

    def _create_strategies(self) -> List[str]:
        queries = []
        base_terms = re.split(r'\s+AND\s+(?=(?:"[^"]*"|[^"])*$)', self.raw_query)
        quoted_terms = [
            f'"{term.strip()}"' if ' ' in term.strip() and not (term.strip().startswith('"') and term.strip().endswith('"')) else term.strip() 
            for term in base_terms
        ]
        basic_query = ' AND '.join(filter(None, quoted_terms))
        
        if basic_query:
            # 1. 基础临床研究查询（包含5年时间限制）
            current_year = datetime.now().year
            date_filter = f"({current_year}[pdat] OR {current_year-1}[pdat] OR {current_year-2}[pdat] OR {current_year-3}[pdat] OR {current_year-4}[pdat])"
            
            # 临床研究相关术语
            clinical_terms = [
                'clinical trial[Title/Abstract]',
                'randomized controlled trial[Title/Abstract]',
                'clinical study[Title/Abstract]',
                'clinical research[Title/Abstract]',
                'randomized[Title/Abstract]',
                'placebo[Title/Abstract]',
                'patients[Title/Abstract]',
                'humans[MeSH Terms]',
                '"clinical trial"[Publication Type]',
                'controlled clinical trial[Publication Type]',
                'randomized controlled trial[Publication Type]',
                'multicenter study[Publication Type]',
                'evaluation study[Publication Type]'
            ]
            clinical_filter = ' OR '.join(clinical_terms)
            
            # 策略1：基础查询 + 临床术语 + 时间筛选
            clinical_query = f'({basic_query}) AND ({clinical_filter}) AND {date_filter}'
            queries.append(clinical_query)
            logger.info(f"  -> Strategy 1 (Clinical + 5 years): '{clinical_query}'")
            
            # 策略2：MeSH词表查询 + 时间筛选
            mesh_terms = [
                'Clinical Trial[MeSH Terms]',
                'Randomized Controlled Trial[MeSH Terms]',
                'Clinical Study[MeSH Terms]',
                'Clinical Research[MeSH Terms]',
                'Multicenter Study[MeSH Terms]',
                'Evaluation Study[MeSH Terms]'
            ]
            for mesh_term in mesh_terms:
                mesh_query = f"({basic_query}) AND {mesh_term} AND {date_filter}"
                queries.append(mesh_query)
                logger.info(f"  -> Strategy 2 (MeSH + 5 years): '{mesh_query}'")
        
        if self.llm and self.llm_provider and self.llm_model:
            try:
                llm_query = self.llm.generate(
                    self.llm_provider, self.llm_model, 
                    PROMPTS_EN['extract_keywords'], 
                    self.raw_query, 
                    temperature=0.0
                ).strip().replace('"', '')
                
                if llm_query and llm_query not in queries:
                    queries.append(llm_query)
                    logger.info(f"  -> Strategy 4 (LLM-optimized): '{llm_query}'")
            except Exception as e:
                logger.warning(f"LLM query generation failed: {e}. Skipping LLM optimization.")
        
        current_year = datetime.now().year
        date_filters = [f"{current_year - i}[pdat]" for i in range(5)]
        date_filter = f"({' OR '.join(date_filters)})"
        
        dated_queries = [f"({q}) AND {date_filter}" for q in queries]
        return list(dict.fromkeys(dated_queries))

    def _extract_core_keyword(self, query: str) -> str:
        match = re.search(r'["\']([^"\']+)["\']|\b\w+\b', query)
        return match.group(1) or match.group(0) if match else query
        
    def _create_fallback_strategies(self, core_keyword: str) -> List[str]:
        strategies = []
        clinical_terms = [
            'clinical', 'trial', 'study', 'patients', 'participants', 'randomized', 
            'cohort', 'case-control', 'cross-sectional', 'treatment', 'therapy', 
            'intervention', 'human', 'humans'
        ]
        clinical_query = ' OR '.join(clinical_terms)
        current_year = datetime.now().year
        date_filter = f"({current_year}[pdat] OR {current_year-1}[pdat] OR {current_year-2}[pdat] OR {current_year-3}[pdat] OR {current_year-4}[pdat])"
        
        strategy1 = f'"{core_keyword}" AND ({clinical_query}) AND {date_filter}'
        strategies.append(strategy1)
        logger.info(f"  -> Fallback Strategy 1 (Core + Clinical + 5 years): '{strategy1}'")
        
        strategy2 = f'"{core_keyword}" AND {date_filter}'
        strategies.append(strategy2)
        logger.info(f"  -> Fallback Strategy 2 (Core + 5 years): '{strategy2}'")
        
        return strategies
        
    def get_queries(self) -> List[str]:
        return self.strategies

class SearchProvider:
    def __init__(self, max_results: int, years: int):
        self.max_results = max_results
        self.years = years
        self.provider_name = self.__class__.__name__

    def search(self, query: str) -> List[Dict[str, Any]]:
        raise NotImplementedError

    def _get_start_year(self) -> int:
        return datetime.now().year - self.years

    @staticmethod
    def _is_clinical_study(title: str, abstract: str) -> bool:
        if not abstract and not title:
            return False
            
        text = f"{title.lower() if title else ''} {abstract.lower() if abstract else ''}"
        
        # 必须包含的术语（至少一个）
        required_terms = [
            'clinical trial', 'randomized controlled trial', 'rct', 
            'clinical study', 'clinical research', 'patients', 'participants',
            '疗效', '临床试验', '临床研究', '患者', '受试者'
        ]
        
        # 加分术语
        bonus_terms = [
            'randomized', 'placebo', 'controlled', 'cohort', 'case-control',
            'prospective', 'retrospective', 'multicenter', 'double-blind',
            'single-blind', 'masked', 'intervention', 'treatment', 'therapy',
            'randomised', 'randomization', 'randomisation',
            '随机', '对照', '双盲', '单盲', '多中心', '干预', '治疗'
        ]
        
        # 排除术语（如果包含这些术语，则不是临床研究）
        exclude_terms = [
            'in vitro', 'in vivo', 'animal', 'mice', 'rat', 'mouse', 'rat', 'rabbit',
            'cell line', 'cell culture', 'in silico', 'computational', 'simulation',
            'review', 'meta-analysis', 'systematic review', 'protocol', 'study protocol',
            '体外', '体内', '动物', '小鼠', '大鼠', '兔子', '细胞系', '细胞培养',
            '计算机模拟', '模拟', '综述', '荟萃分析', '系统评价', '研究方案'
        ]
        
        # 检查排除术语
        if any(term in text for term in exclude_terms):
            return False
            
        # 检查必须包含的术语
        has_required = any(term in text for term in required_terms)
        if not has_required:
            return False
            
        # 计算加分术语数量
        bonus_count = sum(1 for term in bonus_terms if term in text)
        
        # 至少需要1个必须术语和1个加分术语，或者2个必须术语
        return bonus_count >= 1 or (has_required and len(required_terms) >= 2)

    @classmethod
    def extract_ebm_data(cls, abstract: str, title: str = "") -> Dict[str, Any]:
        if not abstract or not abstract.strip():
            return {
                "is_clinical": False,
                "pico": {"population": None, "intervention": None, "comparison": None, "outcome": None},
                "results": {"effect_size": None, "effect_value": None, "ci_lower": None, "ci_upper": None, "p_value": None},
                "study_design": None,
                "sample_size": None,
                "quality_indicators": {"randomized": None, "blinded": None, "controlled": None}
            }
        
        abstract_lower = abstract.lower()
        is_clinical = cls._is_clinical_study(title, abstract)
        
        ebm_data = {
            "is_clinical": is_clinical,
            "pico": {}, "results": {}, "study_design": None,
            "sample_size": None, "quality_indicators": {}
        }
        
        design_patterns = {
            "rct": r"randomi[sz]ed\s+controlled\s+trial|rct|randomi[sz]ed\s+clinical\s+trial",
            "cohort": r"cohort\s+study|prospective\s+study|longitudinal\s+study",
            "case_control": r"case[\s-]control|case\s+series",
            "cross_sectional": r"cross[\s-]sectional|prevalence\s+study",
            "meta_analysis": r"meta[\s-]?analysis|systematic\s+review"
        }
        for design, pattern in design_patterns.items():
            if re.search(pattern, abstract_lower):
                ebm_data["study_design"] = design
                break
        
        sample_matches = re.finditer(r'(\d{1,3}(?:,\d{3})*)\s*(?:patients|participants|subjects|cases|individuals)', abstract, re.IGNORECASE)
        samples = [int(m.group(1).replace(',', '')) for m in sample_matches]
        if samples:
            ebm_data["sample_size"] = max(samples)
        
        pico_patterns = {
            "population": r'(?:patients?|participants?|subjects?|cases?|cohort)\s+(?:with|of|having|diagnosed with|suffering from)\s+([^.,;:()]+?)(?=[.,;:()]|\s+and\s+|\s+or\s+|$)',
            "intervention": r'(?:treated with|received|administered|intervention(?: group)?|treatment(?: group)?|arm(?:\s*[A-Za-z]?))\s*:?\s*([^.,;:()]+?)(?=[.,;:()]|\s+and\s+|\s+or\s+|\s+vs\.?\s+|\s+versus\s+|$)',
            "comparison": r'(?:vs\.?|versus|compared with|compared to|control(?: group)?|placebo(?: group)?|standard(?: care| therapy)?(?: group)?|\bSOC\b)\s*:?\s*([^.,;()]+?)(?=[.,;()]|\s+and\s+|\s+or\s+|$)',
            "outcome": r'(?:primary\s+outcome|secondary\s+outcome|endpoint|evaluated|measured|assessed|analy[sz]ed)\s*:?\s*([^.,;()]+?)(?=[.,;()]|\s+and\s+|\s+or\s+|$)'
        }
        for key, pattern in pico_patterns.items():
            terms = re.findall(pattern, abstract, re.IGNORECASE)
            if terms:
                ebm_data["pico"][key] = "; ".join([t.strip() for t in terms if len(t.split()) < 20])
        
        effect_match = re.search(r'(HR|OR|RR|MD|SMD)\s*[=:]?\s*([\d.]+)\s*\(.*?([\d.]+)\s*[,-]?\s*([\d.]+)\)', abstract, re.IGNORECASE)
        if effect_match:
            ebm_data["results"]["effect_size"] = effect_match.group(1).upper()
            ebm_data["results"]["effect_value"] = float(effect_match.group(2))
            ebm_data["results"]["ci_lower"] = float(effect_match.group(3))
            ebm_data["results"]["ci_upper"] = float(effect_match.group(4))
            
        p_value_match = re.search(r'\b(p\s*[<>=]\s*0\.\d+)', abstract, re.IGNORECASE)
        if p_value_match:
            ebm_data["results"]["p_value"] = p_value_match.group(0).replace(" ", "")

        ebm_data["quality_indicators"]["randomized"] = bool(re.search(r'randomi[sz]ed', abstract_lower))
        ebm_data["quality_indicators"]["blinded"] = bool(re.search(r'\b(double[\s-]?blind|single[\s-]?blind|masked)\b', abstract_lower))
        ebm_data["quality_indicators"]["controlled"] = bool(re.search(r'\b(control(?:\s+group)?|placebo|standard\s+(?:care|therapy))\b', abstract_lower))
        
        for section in ebm_data:
            if isinstance(ebm_data[section], dict):
                for key, value in list(ebm_data[section].items()):
                    if not value: ebm_data[section][key] = None
        
        return ebm_data

class PubMedProvider(SearchProvider):
    def __init__(self, max_results: int, years: int, email: str):
        super().__init__(max_results, years)
        # Set up Entrez with just the email (required by NCBI)
        if not email or "example.com" in email:
            email = "<EMAIL>"  # Fallback email if not provided
            logger.warning(f"{self.provider_name}: Using default email. Please set a valid email in settings.")
        
        Entrez.email = email
        Entrez.sleep_between_tries = 5  # Increased wait time between retries
        
        # Rate limiting variables - be more conservative without API key
        self.last_request_time = 0
        self.min_request_interval = 0.34  # ~3 requests per second
        self.max_retries = 3

    def _enforce_rate_limit(self):
        """Enforce minimum time between API calls to respect rate limits."""
        import time
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.min_request_interval:
            sleep_time = self.min_request_interval - time_since_last
            logger.debug(f"Rate limiting: Waiting {sleep_time:.2f}s between requests")
            time.sleep(sleep_time)
        
        self.last_request_time = time.time()
    
    @retry(
        stop=stop_after_attempt(3),  # Reduced retry attempts
        wait=wait_exponential(multiplier=1, min=2, max=30),  # Shorter wait times
        retry=(
            retry_if_exception_type(requests.exceptions.RequestException) |
            retry_if_exception_type(IOError) |
            retry_if_exception_type(ValueError) |
            retry_if_exception_type(Exception)  # Catch all other exceptions
        ),
        before_sleep=lambda retry_state: logger.warning(
            f"Retrying PubMed API call (attempt {retry_state.attempt_number}): "
            f"{str(retry_state.outcome.exception()) if hasattr(retry_state.outcome, 'exception') else 'Error'}"
        )
    )
    def _make_entrez_request(self, func, **kwargs):
        """
        Make an Entrez API request with rate limiting and error handling.
        Returns a tuple of (handle, needs_parsing) where needs_parsing is a boolean
        indicating if the handle needs to be parsed with Entrez.read().
        """
        import time
        from urllib.error import HTTPError
        from http.client import IncompleteRead
        
        self._enforce_rate_limit()
        
        try:
            # Make the API call
            handle = func(**kwargs)
            
            # For esearch, we need to read the handle to get the WebEnv and QueryKey
            if func.__name__ == 'esearch':
                try:
                    result = Entrez.read(handle)
                    handle.close()
                    return result, False
                except Exception as e:
                    logger.error(f"Failed to parse esearch response: {str(e)}")
                    if hasattr(handle, 'read'):
                        response_text = handle.read()
                        logger.debug(f"Raw esearch response: {response_text[:500]}...")
                    raise
            
            # For efetch, we'll return the handle for batch processing
            return handle, True
            
        except HTTPError as e:
            # Handle HTTP errors
            if e.code == 429:  # Too Many Requests
                retry_after = int(e.headers.get('Retry-After', 30))  # Default to 30 seconds
                logger.warning(f"Rate limited by PubMed API. Waiting {retry_after} seconds...")
                time.sleep(retry_after)
            elif e.code >= 500:
                logger.error(f"PubMed server error (HTTP {e.code}): {e.reason}")
            raise
            
        except IncompleteRead as e:
            logger.error(f"Incomplete read from PubMed API: {str(e)}")
            raise
            
        except Exception as e:
            logger.error(f"Unexpected error in _make_entrez_request: {str(e)}")
            raise
            
        finally:
            # Don't close the handle here as we might need to read it later
            # The caller is responsible for closing the handle
            pass
    
    def _parse_article_data(self, article_data: Dict) -> Optional[Dict[str, Any]]:
        """
        Parse a single article from the PubMed API response.
        
        Args:
            article_data: Raw article data from Entrez.read()
            
        Returns:
            Dict containing parsed article data, or None if parsing fails
        """
        if not article_data or not isinstance(article_data, dict):
            logger.debug("Invalid article data: not a dictionary or empty")
            return None
            
        try:
            # Safely extract the MedlineCitation section
            medline_citation = article_data.get("MedlineCitation", {})
            if not medline_citation:
                logger.debug("Missing MedlineCitation in article data")
                return None
                
            # Extract PMID - handle different possible formats
            pmid = ""
            pmid_data = medline_citation.get("PMID")
            if isinstance(pmid_data, dict):
                pmid = str(pmid_data.get("#text", "")).strip()
            elif pmid_data:
                pmid = str(pmid_data).strip()
                
            if not pmid:
                logger.debug("Missing or invalid PMID in article data")
                return None
                
            # Extract article info with safe access
            article_info = medline_citation.get("Article", {})
            if not article_info:
                logger.debug(f"Missing Article section in MedlineCitation for PMID {pmid}")
                return None
                
            # Extract title with safe handling
            title = ""
            title_data = article_info.get("ArticleTitle")
            if isinstance(title_data, dict):
                title = str(title_data.get("#text", "")).strip()
            elif title_data is not None:
                title = str(title_data).strip()
                
            # Extract abstract text with safe handling
            abstract = ""
            try:
                abstract_data = article_info.get("Abstract", {})
                abstract_parts = abstract_data.get("AbstractText", [])
                
                if isinstance(abstract_parts, list):
                    abstract_pieces = []
                    for part in abstract_parts:
                        if part is None:
                            continue
                        if isinstance(part, dict):
                            part_text = part.get("#text", "")
                            if part_text:  # Only add non-empty parts
                                abstract_pieces.append(str(part_text))
                        else:
                            part_str = str(part).strip()
                            if part_str:  # Only add non-empty strings
                                abstract_pieces.append(part_str)
                    abstract = " ".join(abstract_pieces)
                elif abstract_parts:
                    abstract = str(abstract_parts)
            except Exception as abstract_error:
                logger.warning(f"Error parsing abstract for PMID {pmid}: {str(abstract_error)}")
                abstract = ""
            
            # Extract authors with safe handling
            authors = []
            try:
                author_list = article_info.get("AuthorList", [])
                if isinstance(author_list, list):
                    for author in author_list:
                        if not isinstance(author, dict):
                            continue
                        try:
                            forename = str(author.get("ForeName", "")).strip()
                            lastname = str(author.get("LastName", "")).strip()
                            if forename or lastname:
                                author_name = f"{forename} {lastname}".strip()
                                if author_name:  # Only add non-empty names
                                    authors.append(author_name)
                        except Exception as author_error:
                            logger.debug(f"Error parsing author for PMID {pmid}: {str(author_error)}")
                            continue
            except Exception as author_list_error:
                logger.warning(f"Error parsing author list for PMID {pmid}: {str(author_list_error)}")
            
            # Extract journal and publication date with safe handling
            journal_title = ""
            year = ""
            try:
                journal = article_info.get("Journal", {})
                if isinstance(journal, dict):
                    # Get journal title
                    journal_title = str(journal.get("Title", "")).strip()
                    
                    # Extract publication date
                    journal_issue = journal.get("JournalIssue", {})
                    if isinstance(journal_issue, dict):
                        pub_date = journal_issue.get("PubDate", {})
                        if isinstance(pub_date, dict):
                            year = str(pub_date.get("Year", "")).strip()
                            if not year and "MedlineDate" in pub_date:
                                medline_date = str(pub_date["MedlineDate"])
                                match = re.search(r'\d{4}', medline_date)
                                if match:
                                    year = match.group(0)
            except Exception as journal_error:
                logger.warning(f"Error parsing journal info for PMID {pmid}: {str(journal_error)}")
            
            # Extract DOI with safe handling
            doi = ""
            try:
                pubmed_data = article_data.get("PubmedData", {})
                if isinstance(pubmed_data, dict):
                    article_id_list = pubmed_data.get("ArticleIdList", [])
                    if isinstance(article_id_list, list):
                        for item in article_id_list:
                            if not isinstance(item, dict):
                                continue
                            id_type = str(item.get("@IdType", "")).lower()
                            if id_type == "doi":
                                doi_candidate = str(item.get("#text", "")).strip()
                                if doi_candidate:  # Only set if not empty
                                    doi = doi_candidate
                                    break
            except Exception as doi_error:
                logger.warning(f"Error extracting DOI for PMID {pmid}: {str(doi_error)}")
            
            # Extract EBM data with safe handling
            ebm_data = {}
            try:
                if abstract or title:  # Only try to extract EBM data if we have content
                    ebm_data = self.extract_ebm_data(abstract, title)
                else:
                    logger.debug(f"Skipping EBM data extraction for PMID {pmid} - no abstract or title")
                    ebm_data = {
                        "is_clinical": False,
                        "pico": {"population": None, "intervention": None, "comparison": None, "outcome": None},
                        "results": {"effect_size": None, "effect_value": None, "ci_lower": None, "ci_upper": None, "p_value": None},
                        "study_design": None,
                        "sample_size": None,
                        "quality_indicators": {"randomized": None, "blinded": None, "controlled": None}
                    }
            except Exception as ebm_error:
                logger.warning(f"Error extracting EBM data for PMID {pmid}: {str(ebm_error)}")
                # Provide default EBM data structure on error
                ebm_data = {
                    "is_clinical": False,
                    "pico": {"population": None, "intervention": None, "comparison": None, "outcome": None},
                    "results": {"effect_size": None, "effect_value": None, "ci_lower": None, "ci_upper": None, "p_value": None},
                    "study_design": None,
                    "sample_size": None,
                    "quality_indicators": {"randomized": None, "blinded": None, "controlled": None}
                }
            
            # Assemble final article data
            try:
                article = {
                    "id": pmid,
                    "source": "PubMed",
                    "title": title,
                    "abstract": abstract,
                    "authors": authors,
                    "journal": journal_title,
                    "year": year,
                    "url": f"https://pubmed.ncbi.nlm.nih.gov/{pmid}/" if pmid else "",
                    "doi": doi,
                    "is_clinical": bool(ebm_data.get("is_clinical", False)),
                    "ebm_data": ebm_data
                }
                
                # Log a warning if we have an article with no title or abstract
                if not (title or abstract):
                    logger.warning(f"Article {pmid} has no title or abstract")
                
                return article
                
            except Exception as assembly_error:
                logger.error(f"Error assembling article data for PMID {pmid}: {str(assembly_error)}")
                return None
            
        except Exception as e:
            logger.error(f"Error parsing article data: {str(e)}", exc_info=True)
            return None
    
    def search(self, query: str) -> List[Dict[str, Any]]:
        """Search PubMed with rate limiting and retry logic."""
        logger.info(f"{self.provider_name}: Searching for: '{query}'")
        
        try:
            # Set timeout for all Entrez operations
            Entrez.timeout = 30  # Increased timeout
            
            # Prepare search parameters (without API key)
            search_params = {
                'db': 'pubmed',
                'term': query,
                'retmax': min(100, self.max_results),  # Reduced to 100 results max
                'usehistory': 'y',
                'sort': 'relevance',
                'retmode': 'xml'
            }
            
            # Get the search results
            try:
                search_results = self._make_entrez_request(Entrez.esearch, **search_params)
                if not isinstance(search_results, tuple):
                    logger.error("Unexpected search results format")
                    return []
                    
                search_data, _ = search_results
                id_list = search_data.get('IdList', [])
                
                if not id_list:
                    logger.info(f"{self.provider_name}: No results found for query: '{query}'")
                    return []
                    
                logger.info(f"{self.provider_name}: Found {len(id_list)} results for query: '{query}'")
                
            except Exception as e:
                logger.error(f"{self.provider_name}: Search failed for query '{query}': {str(e)}")
                return []
            
            # Fetch article details in smaller batches to be more reliable
            batch_size = 20  # Reduced batch size for better reliability
            articles = []
            
            for i in range(0, len(id_list), batch_size):
                batch_ids = id_list[i:i + batch_size]
                batch_num = (i // batch_size) + 1
                total_batches = (len(id_list) + batch_size - 1) // batch_size
                
                logger.debug(f"Fetching batch {batch_num}/{total_batches} with {len(batch_ids)} articles")
                
                try:
                    # Fetch the batch of articles
                    fetch_params = {
                        'db': 'pubmed',
                        'id': ','.join(batch_ids),
                        'retmode': 'xml',
                        'rettype': 'abstract'
                    }
                    
                    # Get the fetch handle and parse the results
                    fetch_result = self._make_entrez_request(Entrez.efetch, **fetch_params)
                    if not isinstance(fetch_result, tuple):
                        logger.error("Unexpected fetch result format")
                        continue
                        
                    fetch_handle, needs_parsing = fetch_result
                    
                    if needs_parsing and hasattr(fetch_handle, 'read'):
                        try:
                            # Parse the articles
                            articles_data = Entrez.read(fetch_handle)
                            
                            # Handle both single article and multiple articles cases
                            if 'PubmedArticle' in articles_data:
                                for article_data in articles_data['PubmedArticle']:
                                    if not article_data:
                                        continue
                                    parsed_article = self._parse_article_data(article_data)
                                    if parsed_article:
                                        articles.append(parsed_article)
                                        
                            elif isinstance(articles_data, dict):
                                # Handle case where it's a single article
                                parsed_article = self._parse_article_data(articles_data)
                                if parsed_article:
                                    articles.append(parsed_article)
                                    
                            logger.debug(f"Successfully processed {len(articles)} articles in batch {batch_num}")
                            
                        except Exception as parse_error:
                            logger.error(f"Error parsing batch {batch_num}: {str(parse_error)}")
                        
                except Exception as e:
                    logger.error(f"Error fetching batch {batch_num}: {str(e)}")
                    
                finally:
                    # Ensure the handle is always closed
                    if 'fetch_handle' in locals() and hasattr(fetch_handle, 'close'):
                        try:
                            fetch_handle.close()
                        except Exception as close_error:
                            logger.warning(f"Error closing fetch handle: {str(close_error)}")
                    
                    # Be nice to the PubMed servers
                    if i + batch_size < len(id_list):
                        import time
                        time.sleep(1)  # Add a small delay between batches
            
            logger.info(f"{self.provider_name}: Found {len(articles)} valid articles for query: '{query}'")
            return articles
            
        except Exception as e:
            logger.error(f"{self.provider_name}: Search failed for query '{query}': {str(e)}", exc_info=True)
            return []

class ArxivProvider(SearchProvider):
    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=2, min=3, max=20))
    def search(self, query: str) -> List[Dict[str, Any]]:
        logger.info(f"{self.provider_name}: Searching for: '{query}'")
        try:
            # 提取基础查询（不包含日期部分）
            base_query = re.sub(r'\s+AND\s+\([^)]*pdat[^)]*\)', '', query, flags=re.IGNORECASE).strip()
            
            # 提取日期部分（如果存在）
            date_match = re.search(r'AND\s+\(([^)]*pdat[^)]*)\)', query, re.IGNORECASE)
            date_filter = f"AND ({date_match.group(1)})" if date_match else ""
            
            # 添加临床研究相关的关键词
            clinical_terms = [
                'clinical trial', 'randomized controlled trial', 'clinical study', 
                'clinical research', 'patients', 'participants', 'randomized', 'placebo',
                'cohort', 'case-control', 'prospective', 'retrospective'
            ]
            
            # 构建最终查询
            clinical_terms_str = ' OR '.join(clinical_terms[:4])
            arxiv_query = f"{base_query} AND ({clinical_terms_str}) {date_filter}".strip()
            
            search = arxiv.Search(
                query=arxiv_query, 
                max_results=self.max_results * 3,  # 获取更多结果用于筛选
                sort_by=arxiv.SortCriterion.Relevance,
                sort_order=arxiv.SortOrder.Descending
            )
            
            client = arxiv.Client(page_size=50, delay_seconds=3, num_retries=3)
            
            results = []
            start_year = self._get_start_year()
            
            # 收集并过滤结果
            for result in client.results(search):
                if len(results) >= self.max_results:
                    break
                    
                # 时间筛选
                if result.published.year < start_year:
                    continue
                    
                # 解析结果
                parsed = self._parse_result(result)
                if not parsed:
                    continue
                    
                # 确保是临床研究
                if not parsed.get('is_clinical', False):
                    logger.debug(f"Skipping non-clinical study: {parsed.get('title', 'Untitled')}")
                    continue
                    
                results.append(parsed)
                
            logger.info(f"{self.provider_name}: Found {len(results)} clinical studies")
            return results
            
        except Exception as e:
            logger.error(f"{self.provider_name}: Search failed: {e}", exc_info=True)
            return []

    def _parse_result(self, result: arxiv.Result) -> Optional[Dict[str, Any]]:
        try:
            title = result.title.strip()
            abstract = result.summary.replace("\n", " ").strip()
            ebm_data = self.extract_ebm_data(abstract, title)
            return {
                "id": result.entry_id.split('/')[-1], "source": "ArXiv", "title": title, "abstract": abstract,
                "authors": [str(a) for a in result.authors], "journal": "ArXiv", "year": str(result.published.year),
                "url": result.pdf_url, "doi": result.doi,
                "is_clinical": ebm_data.get("is_clinical", False),
                "ebm_data": ebm_data
            }
        except Exception as e:
            logger.error(f"Error parsing ArXiv result: {e}", exc_info=True)
            return None

class SearchService:
    def __init__(self, llm_manager: LLMManager):
        self.providers: List[SearchProvider] = []
        if settings.PUBMED_EMAIL:
            self.providers.append(PubMedProvider(settings.MAX_RESULTS_PER_SOURCE, settings.SEARCH_YEARS, settings.PUBMED_EMAIL))
        self.providers.append(ArxivProvider(settings.MAX_RESULTS_PER_SOURCE, settings.SEARCH_YEARS))
        self.llm_manager = llm_manager
        self.llm_provider: Optional[str] = None
        self.llm_model: Optional[str] = None
        
    def set_llm_config(self, provider: str, model: str):
        self.llm_provider, self.llm_model = provider, model
        
    def _process_query(self, query: str) -> str:
        if not query.strip():
            return query
            
        logger.info(f"Processing query: {query}")
        if not self.llm_manager or not self.llm_provider or not self.llm_model:
            logger.warning("LLM not configured, using raw query.")
            return query
            
        try:
            if any('\u4e00' <= char <= '\u9fff' for char in query):
                logger.info("Chinese query detected, starting LLM processing.")
                
                # Step 1: Split the query into individual medical terms
                split_prompt = f"""请将以下中文医学查询拆分为独立的医学术语，每个术语用逗号分隔。只返回术语，不要额外的解释或标点符号。
                
                查询: {query}
                
                请只返回用逗号分隔的术语列表，不要其他内容。"""
                
                terms_str = self.llm_manager.generate(
                    self.llm_provider, 
                    self.llm_model, 
                    "你是一个专业的医学助手，擅长将医学查询拆分为独立的术语。请确保只返回术语，不要任何解释或额外的文本。",
                    split_prompt, 
                    temperature=0.1
                ).strip()
                
                # Clean up the response and split into terms
                terms = [t.strip() for t in re.split(r'[,\s，、]+', terms_str) if t.strip() and len(t.strip()) > 1]
                logger.info(f"Split into terms: {terms}")
                
                if not terms:
                    logger.warning("No terms extracted, using original query")
                    return f'"{query}"'
                
                # Step 2: Translate each term to English
                translated_terms = []
                for term in terms:
                    if not any('\u4e00' <= char <= '\u9fff' for char in term):
                        # If term doesn't contain Chinese, use as is
                        translated = term
                    else:
                        # Translate Chinese term to English
                        translation_prompt = f"""请将以下中文医学术语翻译成英文。只返回英文翻译，不要解释或额外的文本。
                        
                        中文术语: {term}
                        
                        英文翻译:"""
                        
                        translated = self.llm_manager.generate(
                            self.llm_provider,
                            self.llm_model,
                            "你是一个专业的医学翻译，请将中文医学术语准确翻译成英文。只返回翻译结果，不要解释。",
                            translation_prompt,
                            temperature=0.1
                        ).strip()
                        
                        # Clean up the translation
                        translated = re.sub(r'^[\'\"`]|[\'\"`]$', '', translated.strip())
                        translated = re.sub(r'[^\w\s-]', ' ', translated)  # Remove special chars except spaces and hyphens
                        translated = ' '.join(translated.split())  # Normalize whitespace
                        
                        if not translated or 'sorry' in translated.lower() or 'unable' in translated.lower():
                            logger.warning(f"Translation failed for '{term}'. Using original term.")
                            translated = term
                        else:
                            logger.info(f"Translated term: '{term}' -> '{translated}'")
                    
                    # Add quotes if the term contains spaces
                    if ' ' in translated and not (translated.startswith('"') and translated.endswith('"')):
                        translated = f'"{translated}"'
                    translated_terms.append(translated)
                
                # Combine terms with AND for the final query
                query = ' AND '.join(translated_terms)
                logger.info(f"Constructed English search query: {query}")
                
            return query
            
        except Exception as e:
            logger.error(f"Error processing query with LLM: {e}", exc_info=True)
            return f'"{query}"'

    def search_all(self, query: str) -> List[Dict[str, Any]]:
        if not query.strip(): return []
        
        processed_query = self._process_query(query)
        if not processed_query:
            logger.warning("Query processing resulted in an empty string.")
            return []

        smart_query = SmartQuery(processed_query, self.llm_manager, self.llm_provider, self.llm_model)
        query_strategies = smart_query.get_queries()
        
        if not query_strategies:
            logger.warning("No valid search strategies could be generated.")
            return []
            
        all_articles = []
        seen_titles = set()
        
        def add_unique_articles(articles: List[Dict[str, Any]], source: str) -> int:
            """添加唯一文章到结果中，返回添加的数量"""
            if not articles:
                return 0
                
            new_articles = []
            for article in articles:
                if not article or not article.get('title'):
                    continue
                    
                title_lower = article['title'].lower().strip()
                if title_lower not in seen_titles:
                    seen_titles.add(title_lower)
                    new_articles.append(article)
            
            if new_articles:
                all_articles.extend(new_articles)
                logger.info(f"Added {len(new_articles)} new unique articles from {source}")
                
            return len(new_articles)
        
        # 1. 首先执行PubMed检索
        pubmed_provider = next((p for p in self.providers if isinstance(p, PubMedProvider)), None)
        if pubmed_provider:
            logger.info("Executing PubMed search...")
            for strategy in query_strategies:
                try:
                    results = pubmed_provider.search(strategy)
                    if results:
                        add_unique_articles(results, "PubMed")
                except Exception as e:
                    logger.error(f"PubMed search failed: {e}")
        
        # 2. 如果PubMed结果不足30篇，执行arXiv检索
        if len(all_articles) < 30:
            logger.info(f"PubMed returned {len(all_articles)} articles, proceeding with arXiv search...")
            arxiv_provider = next((p for p in self.providers if isinstance(p, ArxivProvider)), None)
            if arxiv_provider:
                # 为arXiv构建更严格的临床研究查询
                core_terms = ' AND '.join(re.findall(r'"([^"]+)"|(\b\w+\b)', processed_query)[0])
                clinical_terms = [
                    'clinical trial', 'randomized controlled trial', 'RCT',
                    'clinical study', 'clinical research', 'patients', 'participants'
                ]
                arxiv_query = f'({core_terms}) AND ({" OR ".join(clinical_terms)})'
                
                try:
                    results = arxiv_provider.search(arxiv_query)
                    if results:
                        # 确保是临床研究并且未重复
                        clinical_results = [a for a in results if a and a.get('is_clinical', False)]
                        add_unique_articles(clinical_results, "arXiv")
                except Exception as e:
                    logger.error(f"arXiv search failed: {e}")
        else:
            logger.info(f"Skipping arXiv search as PubMed returned sufficient results ({len(all_articles)} articles)")
        
        logger.info(f"Search complete. Total unique clinical studies found: {len(all_articles)}")
        
        # 按临床相关性和年份排序
        all_articles.sort(key=lambda x: (
            -x.get('is_clinical', False),  # 临床研究优先
            -int(x.get('year', 0) or 0)    # 最新研究优先
        ))
        
        return all_articles
        
    def _execute_searches_concurrently(self, strategies: List[str], all_articles: List[Dict],
                                   seen_titles: set, is_fallback: bool = False) -> bool:
        """
        并发执行搜索策略（当前已弃用，改为顺序执行以控制流程）
        保留此方法以保持向后兼容性
        """
        logger.warning("Concurrent search is deprecated. Using sequential search instead.")
        initial_count = len(all_articles)
        
        # 顺序执行搜索
        for provider in self.providers:
            for strategy in strategies:
                try:
                    results = provider.search(strategy)
                    if results:
                        new_articles = [a for a in results 
                                     if a.get('title') 
                                     and a['title'].lower().strip() not in seen_titles
                                     and (isinstance(provider, PubMedProvider) or a.get('is_clinical', False))]
                        if new_articles:
                            all_articles.extend(new_articles)
                            seen_titles.update(a['title'].lower().strip() for a in new_articles)
                            logger.info(f"Added {len(new_articles)} new unique articles from {provider.provider_name}")
                except Exception as e:
                    logger.error(f"Search failed for {provider.provider_name}: {e}")
        
        return len(all_articles) > initial_count
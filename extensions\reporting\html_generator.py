"""
HTML报告生成器
将可视化图表和文本内容组合成HTML报告
"""
import os
import base64
from pathlib import Path
from typing import Dict, Any, Optional, List, Union
import jinja2
import markdown2

class HTMLReportGenerator:
    """
    HTML报告生成器类
    """
    
    def __init__(self, template_dir: Optional[str] = None, static_dir: Optional[str] = None):
        """
        初始化HTML报告生成器
        
        Args:
            template_dir: 模板目录路径
            static_dir: 静态文件目录路径
        """
        self.template_dir = template_dir or str(Path(__file__).parent.parent.parent / 'templates')
        self.static_dir = static_dir or str(Path(__file__).parent.parent.parent / 'static')
        
        # 确保目录存在
        os.makedirs(self.template_dir, exist_ok=True)
        os.makedirs(self.static_dir, exist_ok=True)
        
        # 初始化Jinja2环境
        self.env = jinja2.Environment(
            loader=jinja2.FileSystemLoader(self.template_dir),
            autoescape=jinja2.select_autoescape(['html', 'xml']),
            trim_blocks=True,
            lstrip_blocks=True
        )
        
        # 确保基础模板存在
        self._ensure_base_template()
    
    def _ensure_base_template(self) -> None:
        """确保基础模板存在"""
        base_template = Path(self.template_dir) / 'base.html'
        if not base_template.exists():
            self._create_default_base_template()
    
    def _create_default_base_template(self) -> None:
        """创建默认的基础模板"""
        base_template = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - 系统评价报告</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Plotly.js -->
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    
    <!-- 自定义样式 -->
    <style>
        :root {
            --primary-color: #2c3e50;
            --secondary-color: #3498db;
            --success-color: #27ae60;
            --info-color: #2980b9;
            --warning-color: #f39c12;
            --danger-color: #e74c3c;
            --light-color: #ecf0f1;
            --dark-color: #2c3e50;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            padding-top: 2rem;
            padding-bottom: 4rem;
        }
        
        .report-container {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 0 15px rgba(0,0,0,0.1);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        
        .report-header {
            border-bottom: 2px solid var(--primary-color);
            margin-bottom: 2rem;
            padding-bottom: 1rem;
        }
        
        .report-title {
            color: var(--primary-color);
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .report-subtitle {
            color: #6c757d;
            font-size: 1.1rem;
        }
        
        .section {
            margin-bottom: 2.5rem;
        }
        
        .section-title {
            color: var(--primary-color);
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 0.5rem;
            margin-bottom: 1.5rem;
            font-weight: 600;
        }
        
        .plot-container {
            margin: 2rem 0;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 1.5rem;
            background-color: white;
        }
        
        .plot-title {
            color: var(--primary-color);
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 0.5rem;
            margin-bottom: 1.5rem;
            font-weight: 600;
        }
        
        .table {
            font-size: 0.9rem;
        }
        
        .table thead th {
            background-color: var(--primary-color);
            color: white;
            border-color: #2c3e50;
        }
        
        .footer {
            text-align: center;
            margin-top: 3rem;
            padding: 1.5rem 0;
            color: #6c757d;
            font-size: 0.9rem;
            border-top: 1px solid #dee2e6;
        }
        
        /* 打印样式 */
        @media print {
            body {
                padding: 0;
                background: white;
                font-size: 10pt;
            }
            
            .report-container {
                box-shadow: none;
                padding: 0;
                margin: 0;
            }
            
            .no-print {
                display: none !important;
            }
            
            .section {
                page-break-inside: avoid;
            }
            
            .plot-container {
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="report-container">
            <!-- 页眉 -->
            <header class="report-header">
                <h1 class="report-title">{{ title }}</h1>
                {% if subtitle %}
                <p class="report-subtitle">{{ subtitle }}</p>
                {% endif %}
                <div class="text-muted small">
                    生成日期: {{ generated_at }}
                </div>
            </header>
            
            <!-- 主要内容 -->
            <main>
                {% block content %}
                <!-- 内容将由子模板填充 -->
                {% endblock %}
            </main>
            
            <!-- 页脚 -->
            <footer class="footer">
                <p>本报告由系统自动生成，仅供参考和研究使用</p>
                <p class="text-muted small">生成于 {{ generated_at }}</p>
            </footer>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- 自定义脚本 -->
    <script>
        // 响应式调整图表大小
        window.addEventListener('resize', function() {
            if (typeof Plotly !== 'undefined') {
                var plots = document.querySelectorAll('.js-plotly-plot');
                plots.forEach(function(plot) {
                    Plotly.Plots.resize(plot);
                });
            }
        });
        
        // 打印按钮功能
        document.addEventListener('DOMContentLoaded', function() {
            var printButton = document.getElementById('print-button');
            if (printButton) {
                printButton.addEventListener('click', function() {
                    window.print();
                });
            }
        });
    </script>
</body>
</html>"""
        
        # 确保模板目录存在
        os.makedirs(self.template_dir, exist_ok=True)
        
        # 写入基础模板
        with open(Path(self.template_dir) / 'base.html', 'w', encoding='utf-8') as f:
            f.write(base_template)
    
    def generate_report(
        self,
        title: str,
        content_md: str,
        plots: Optional[Dict[str, str]] = None,
        output_file: Optional[str] = None,
        **context
    ) -> str:
        """
        生成HTML报告
        
        Args:
            title: 报告标题
            content_md: Markdown格式的报告内容
            plots: 图表字典，键为图表ID，值为HTML字符串或Plotly图表对象
            output_file: 输出文件路径，如果为None则不保存到文件
            **context: 其他模板变量
            
        Returns:
            str: 生成的HTML内容
        """
        # 准备上下文
        context.update({
            'title': title,
            'content_md': content_md,
            'plots': plots or {},
            'generated_at': context.get('generated_at', self._get_current_time())
        })
        
        # 确保报告模板存在
        report_template = Path(self.template_dir) / 'report.html'
        if not report_template.exists():
            self._create_default_report_template()
        
        # 渲染模板
        template = self.env.get_template('report.html')
        html_content = template.render(**context)
        
        # 保存到文件
        if output_file:
            os.makedirs(os.path.dirname(os.path.abspath(output_file)), exist_ok=True)
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(html_content)
        
        return html_content
    
    def _create_default_report_template(self) -> None:
        """创建默认的报告模板"""
        report_template = """{% extends "base.html" %}

{% block content %}
<div class="no-print text-end mb-4">
    <button id="print-button" class="btn btn-primary me-2">
        <i class="bi bi-printer"></i> 打印报告
    </button>
    <a href="#" class="btn btn-success" id="download-pdf">
        <i class="bi bi-file-earmark-pdf"></i> 导出PDF
    </a>
</div>

<!-- 摘要部分 -->
<section class="section">
    <h2 class="section-title">摘要</h2>
    <div class="content">
        {{ content_md | safe }}
    </div>
</section>

<!-- 图表部分 -->
{% if plots %}
<section class="section">
    <h2 class="section-title">图表</h2>
    
    {% if plots.forest %}
    <div class="plot-container">
        <h3 class="plot-title">森林图</h3>
        <div class="plot-content">
            {{ plots.forest | safe }}
        </div>
    </div>
    {% endif %}
    
    {% if plots.funnel %}
    <div class="plot-container">
        <h3 class="plot-title">漏斗图</h3>
        <div class="plot-content">
            {{ plots.funnel | safe }}
        </div>
    </div>
    {% endif %}
    
    {% if plots.rob %}
    <div class="plot-container">
        <h3 class="plot-title">偏倚风险评估</h3>
        <div class="plot-content">
            {{ plots.rob | safe }}
        </div>
    </div>
    {% endif %}
</section>
{% endif %}

<!-- 参考文献部分 -->
{% if references %}
<section class="section">
    <h2 class="section-title">参考文献</h2>
    <div class="references">
        <ol class="list-unstyled">
            {% for ref in references %}
            <li class="mb-2" id="ref-{{ loop.index }}">
                {{ ref }}
            </li>
            {% endfor %}
        </ol>
    </div>
</section>
{% endif %}

<script>
// 添加PDF导出功能
document.addEventListener('DOMContentLoaded', function() {
    const downloadPdfBtn = document.getElementById('download-pdf');
    if (downloadPdfBtn) {
        downloadPdfBtn.addEventListener('click', function(e) {
            e.preventDefault();
            window.print(); // 临时解决方案，实际应使用后端PDF生成
        });
    }
});
</script>
{% endblock %}"""
        
        # 写入报告模板
        with open(Path(self.template_dir) / 'report.html', 'w', encoding='utf-8') as f:
            f.write(report_template)
    
    def _get_current_time(self) -> str:
        """获取当前时间字符串"""
        from datetime import datetime
        return datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    def render_template(self, template_name: str, **context) -> str:
        """
        渲染模板
        
        Args:
            template_name: 模板文件名
            **context: 模板变量
            
        Returns:
            str: 渲染后的内容
        """
        template = self.env.get_template(template_name)
        return template.render(**context)

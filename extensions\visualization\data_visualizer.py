#!/usr/bin/env python3
"""
数据可视化模块
为EBM报告生成各种图表和可视化内容
"""

import os
import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path

# 尝试导入可视化库，如果失败则使用文本替代
try:
    import matplotlib.pyplot as plt
    import matplotlib.patches as mpatches
    import seaborn as sns
    import pandas as pd
    import numpy as np
    import matplotlib.font_manager as fm

    # 配置中文字体
    def setup_chinese_font():
        """设置中文字体"""
        try:
            # 尝试多种中文字体
            chinese_fonts = [
                'Microsoft YaHei',  # 微软雅黑
                'SimHei',           # 黑体
                'SimSun',           # 宋体
                'KaiTi',            # 楷体
                'FangSong',         # 仿宋
                'Arial Unicode MS', # Arial Unicode MS
                'DejaVu Sans'       # 备用字体
            ]

            available_fonts = [f.name for f in fm.fontManager.ttflist]

            for font in chinese_fonts:
                if font in available_fonts:
                    plt.rcParams['font.sans-serif'] = [font]
                    plt.rcParams['axes.unicode_minus'] = False
                    logging.getLogger(__name__).info(f"使用中文字体: {font}")
                    return font

            # 如果没有找到中文字体，使用默认设置
            plt.rcParams['font.sans-serif'] = ['DejaVu Sans']
            plt.rcParams['axes.unicode_minus'] = False
            logging.getLogger(__name__).warning("未找到中文字体，使用默认字体")
            return 'DejaVu Sans'

        except Exception as e:
            logging.getLogger(__name__).error(f"字体设置失败: {e}")
            return 'DejaVu Sans'

    # 设置字体
    CURRENT_FONT = setup_chinese_font()
    sns.set_style("whitegrid")

    MATPLOTLIB_AVAILABLE = True
except ImportError as e:
    MATPLOTLIB_AVAILABLE = False
    logging.getLogger(__name__).warning(f"Matplotlib不可用，将使用文本替代: {e}")

import warnings
import datetime
from datetime import datetime
warnings.filterwarnings('ignore')

logger = logging.getLogger(__name__)

class DataVisualizer:
    """数据可视化器"""
    
    def __init__(self, output_dir: str = "output", topic: str = "report"):
        self.output_dir = Path(output_dir)
        self.topic = "_".join(topic.split()[:3])  # Use first 3 words of topic for filename
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Create output directories
        self.output_dir.mkdir(exist_ok=True)
        self.images_dir = self.output_dir / "images"
        self.images_dir.mkdir(exist_ok=True)

        # Ensure Chinese font is set up
        self._ensure_chinese_font()

    def _ensure_chinese_font(self):
        """确保中文字体设置正确"""
        if MATPLOTLIB_AVAILABLE:
            try:
                # 重新设置字体
                chinese_fonts = [
                    'Microsoft YaHei',
                    'SimHei',
                    'SimSun',
                    'KaiTi',
                    'FangSong',
                    'Arial Unicode MS',
                    'DejaVu Sans'
                ]

                import matplotlib.font_manager as fm
                available_fonts = [f.name for f in fm.fontManager.ttflist]

                for font in chinese_fonts:
                    if font in available_fonts:
                        plt.rcParams['font.sans-serif'] = [font]
                        plt.rcParams['axes.unicode_minus'] = False
                        logger.info(f"设置字体: {font}")
                        return

            except Exception as e:
                logger.warning(f"字体设置失败: {e}")
        
    def generate_prisma_flow_chart(self, search_stats: Dict[str, Any]) -> str:
        """生成PRISMA流程图"""
        try:
            if not MATPLOTLIB_AVAILABLE:
                return self._generate_text_prisma_chart(search_stats)

            fig, ax = plt.subplots(figsize=(12, 10))
            ax.set_xlim(0, 10)
            ax.set_ylim(0, 12)
            ax.axis('off')
            
            # 使用真实数据，确保数据有效性和一致性
            identified = max(0, search_stats.get('identified', search_stats.get('identified_count', 0)))
            after_duplicates = max(0, search_stats.get('after_duplicates', identified))
            screened = max(0, search_stats.get('screened', after_duplicates))
            full_text_assessed = max(0, search_stats.get('full_text_assessed', screened))
            included_qualitative = max(0, search_stats.get('included_qualitative', search_stats.get('included_count', full_text_assessed)))
            included_quantitative = max(0, search_stats.get('included_quantitative', included_qualitative))

            # 定义框的位置和大小
            boxes = [
                {"text": f"数据库检索识别的记录\n(n = {identified})",
                 "xy": (5, 11), "width": 3, "height": 0.8, "color": "lightblue"},
                {"text": f"去除重复后的记录\n(n = {after_duplicates})",
                 "xy": (5, 9.5), "width": 3, "height": 0.8, "color": "lightgreen"},
                {"text": f"筛选的记录\n(n = {screened})",
                 "xy": (5, 8), "width": 3, "height": 0.8, "color": "lightyellow"},
                {"text": f"全文评估的文章\n(n = {full_text_assessed})",
                 "xy": (5, 6.5), "width": 3, "height": 0.8, "color": "lightcoral"},
                {"text": f"纳入定性综合的研究\n(n = {included_qualitative})",
                 "xy": (5, 5), "width": 3, "height": 0.8, "color": "lightpink"},
                {"text": f"纳入定量综合的研究\n(荟萃分析)\n(n = {included_quantitative})",
                 "xy": (5, 3.5), "width": 3, "height": 1, "color": "lavender"}
            ]
            
            # 绘制框和文本
            for box in boxes:
                rect = mpatches.FancyBboxPatch(
                    (box["xy"][0] - box["width"]/2, box["xy"][1] - box["height"]/2),
                    box["width"], box["height"],
                    boxstyle="round,pad=0.1",
                    facecolor=box["color"],
                    edgecolor="black",
                    linewidth=1
                )
                ax.add_patch(rect)
                ax.text(box["xy"][0], box["xy"][1], box["text"], 
                       ha='center', va='center', fontsize=10, weight='bold')
            
            # 绘制箭头
            arrows = [
                ((5, 10.6), (5, 10.3)),  # 识别 -> 去重
                ((5, 9.1), (5, 8.8)),    # 去重 -> 筛选
                ((5, 7.6), (5, 7.3)),    # 筛选 -> 全文评估
                ((5, 6.1), (5, 5.8)),    # 全文评估 -> 定性综合
                ((5, 4.5), (5, 4.5))     # 定性 -> 定量综合
            ]
            
            for start, end in arrows:
                ax.annotate('', xy=end, xytext=start,
                           arrowprops=dict(arrowstyle='->', lw=2, color='black'))
            
            # 计算排除的数量，确保逻辑一致性
            duplicates_removed = max(0, search_stats.get('duplicates_removed', identified - after_duplicates))
            excluded_screening = max(0, search_stats.get('excluded_screening', screened - full_text_assessed))
            excluded_full_text = max(0, search_stats.get('excluded_full_text', full_text_assessed - included_qualitative))

            # 添加排除框
            exclusion_boxes = [
                {"text": f"排除重复记录\n(n = {duplicates_removed})",
                 "xy": (8.5, 9.5), "width": 2.5, "height": 0.6},
                {"text": f"排除不相关记录\n(n = {excluded_screening})",
                 "xy": (8.5, 8), "width": 2.5, "height": 0.6},
                {"text": f"排除的全文文章\n(n = {excluded_full_text})",
                 "xy": (8.5, 6.5), "width": 2.5, "height": 0.6}
            ]
            
            for box in exclusion_boxes:
                rect = mpatches.FancyBboxPatch(
                    (box["xy"][0] - box["width"]/2, box["xy"][1] - box["height"]/2),
                    box["width"], box["height"],
                    boxstyle="round,pad=0.05",
                    facecolor="mistyrose",
                    edgecolor="red",
                    linewidth=1,
                    linestyle='--'
                )
                ax.add_patch(rect)
                ax.text(box["xy"][0], box["xy"][1], box["text"], 
                       ha='center', va='center', fontsize=9)
            
            plt.title("PRISMA 2020 流程图", fontsize=16, weight='bold', pad=20)
            
            # 保存图片，添加主题和时间戳
            filename = f"{self.topic}_prisma_flow_chart_{self.timestamp}.png"
            filepath = self.images_dir / filename
            plt.savefig(filepath, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()
            
            logger.info(f"PRISMA流程图已保存: {filepath}")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"生成PRISMA流程图失败: {e}")
            return ""
    
    def generate_risk_of_bias_summary(self, bias_data: Dict[str, Any]) -> str:
        """生成偏倚风险总结图"""
        try:
            if not bias_data or 'bias_risk_summary' not in bias_data:
                logger.warning("偏倚风险数据为空，跳过图表生成")
                return ""

            bias_summary = bias_data['bias_risk_summary']
            if not bias_summary:
                logger.warning("偏倚风险总结数据为空")
                return ""

            if not MATPLOTLIB_AVAILABLE:
                return self._generate_text_bias_summary(bias_summary)
            
            # 准备数据
            domains = list(bias_summary.keys())
            risk_levels = ['Low', 'Unclear', 'High']
            colors = {'Low': 'green', 'Unclear': 'yellow', 'High': 'red'}
            
            fig, ax = plt.subplots(figsize=(12, 8))
            
            # 创建堆叠条形图数据
            data_matrix = []
            for domain in domains:
                domain_data = bias_summary[domain]
                row = [domain_data.get(level, 0) for level in risk_levels]
                data_matrix.append(row)
            
            data_matrix = np.array(data_matrix)
            
            # 绘制堆叠条形图
            bottom = np.zeros(len(domains))
            for i, level in enumerate(risk_levels):
                values = data_matrix[:, i]
                ax.barh(domains, values, left=bottom, 
                       color=colors[level], label=f'{level} risk', alpha=0.8)
                bottom += values
            
            ax.set_xlabel('研究数量', fontsize=12)
            ax.set_title('偏倚风险评估总结', fontsize=14, weight='bold')
            ax.legend(loc='upper right')
            
            # 美化图表
            ax.grid(axis='x', alpha=0.3)
            plt.tight_layout()
            
            # 保存图片，添加主题和时间戳
            filename = f"{self.topic}_risk_of_bias_summary_{self.timestamp}.png"
            filepath = self.images_dir / filename
            
            # Fix overflow issue by adjusting figure size and layout
            plt.tight_layout()
            plt.subplots_adjust(left=0.3)  # Add more space on the left for y-axis labels
            plt.savefig(filepath, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()
            
            logger.info(f"偏倚风险总结图已保存: {filepath}")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"生成偏倚风险总结图失败: {e}")
            return ""
    
    def generate_evidence_quality_chart(self, quality_data: Dict[str, Any]) -> str:
        """生成证据质量分布图"""
        try:
            if not quality_data or 'quality_distribution' not in quality_data:
                logger.warning("证据质量数据为空，跳过图表生成")
                return ""

            quality_dist = quality_data['quality_distribution']
            if not quality_dist:
                logger.warning("证据质量分布数据为空")
                return ""

            # 准备数据，过滤掉None值和0值，并处理"Not assessed"
            filtered_quality_dist = {}
            for quality, count in quality_dist.items():
                if quality and count is not None and count > 0:
                    # 标准化质量等级名称
                    if quality.lower() in ['not assessed', 'not_assessed', 'unknown', '未评估']:
                        filtered_quality_dist['Not assessed'] = count
                    else:
                        filtered_quality_dist[quality] = count

            if not filtered_quality_dist:
                logger.warning("过滤后的证据质量分布数据为空")
                return ""

            qualities = list(filtered_quality_dist.keys())
            counts = list(filtered_quality_dist.values())

            # 为不同质量等级分配颜色
            color_map = {
                'High': '#2E8B57',      # 深绿色
                'Moderate': '#FFD700',   # 金色
                'Low': '#FF6347',        # 橙红色
                'Very Low': '#8B0000',   # 深红色
                'Not assessed': '#808080' # 灰色
            }
            colors = [color_map.get(q, '#808080') for q in qualities]

            fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

            # 饼图
            ax1.pie(counts, labels=qualities, colors=colors,
                   autopct='%1.1f%%', startangle=90)
            ax1.set_title('证据质量分布', fontsize=14, weight='bold')

            # 条形图
            bars = ax2.bar(qualities, counts, color=colors, alpha=0.8)
            ax2.set_xlabel('证据质量等级', fontsize=12)
            ax2.set_ylabel('研究数量', fontsize=12)
            ax2.set_title('证据质量分布', fontsize=14, weight='bold')

            # 在条形图上添加数值标签
            for bar, count in zip(bars, counts):
                height = bar.get_height()
                if height > 0:  # 只为非零值添加标签
                    ax2.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                            f'{count}', ha='center', va='bottom', fontsize=10,
                            fontweight='bold')

            # 旋转x轴标签以避免重叠
            ax2.tick_params(axis='x', rotation=45)

            plt.tight_layout()

            # 保存图片，添加主题和时间戳
            filename = f"{self.topic}_evidence_quality_chart_{self.timestamp}.png"
            filepath = self.images_dir / filename
            plt.savefig(filepath, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()

            logger.info(f"证据质量分布图已保存: {filepath}")
            return str(filepath)

        except Exception as e:
            logger.error(f"生成证据质量分布图失败: {e}")
            return ""
    
    def generate_study_characteristics_table(self, studies: List[Dict[str, Any]]) -> str:
        """生成研究特征表格的可视化"""
        try:
            if not studies:
                logger.warning("研究数据为空，跳过表格生成")
                return ""
            
            # 准备表格数据 - 显示所有研究以确保数据一致性
            table_data = []
            for study in studies:  # 显示所有研究
                # 获取质量评估信息
                quality_assessment = study.get('quality_assessment', 'Not assessed')

                row = {
                    '研究ID': study.get('id', 'N/A')[:15],
                    '发表年份': study.get('year', 'N/A'),
                    '研究类型': study.get('study_type', 'N/A'),
                    '样本量': study.get('sample_size', 'N/A'),
                    '质量评估': quality_assessment,
                    '干预措施': str(study.get('intervention', 'N/A'))[:15] + '...' if len(str(study.get('intervention', 'N/A'))) > 15 else str(study.get('intervention', 'N/A'))
                }
                table_data.append(row)
            
            df = pd.DataFrame(table_data)
            
            # 检查数据是否为空
            if df.empty:
                logger.warning("DataFrame为空，无法生成表格")
                return ""

            # 创建表格可视化，根据数据量调整大小
            num_rows = len(table_data)
            fig_height = max(8, num_rows * 0.4 + 2)
            fig, ax = plt.subplots(figsize=(18, fig_height))
            ax.axis('tight')
            ax.axis('off')

            # 创建表格，调整列宽以适应新的质量评估列
            num_cols = len(df.columns)
            if num_cols == 6:  # 包含质量评估列
                col_widths = [0.15, 0.08, 0.12, 0.08, 0.15, 0.25]
            else:  # 原始5列
                col_widths = [0.15, 0.1, 0.15, 0.1, 0.3]

            table = ax.table(cellText=df.values, colLabels=df.columns,
                           cellLoc='center', loc='center',
                           colWidths=col_widths)
            
            # 美化表格
            table.auto_set_font_size(False)
            table.set_fontsize(9)
            table.scale(1.2, 1.5)
            
            # 设置表头样式
            for i in range(len(df.columns)):
                table[(0, i)].set_facecolor('#4CAF50')
                table[(0, i)].set_text_props(weight='bold', color='white')
            
            # 设置交替行颜色
            for i in range(1, len(df) + 1):
                for j in range(len(df.columns)):
                    if i % 2 == 0:
                        table[(i, j)].set_facecolor('#f0f0f0')
            
            plt.title(f'纳入研究特征总结表 (共{num_rows}项研究)', fontsize=16, weight='bold', pad=20)
            
            # 保存图片，添加主题和时间戳
            filename = f"{self.topic}_study_characteristics_{self.timestamp}.png"
            filepath = self.images_dir / filename
            plt.savefig(filepath, dpi=300, bbox_inches='tight', facecolor='white')
            plt.close()
            
            logger.info(f"研究特征表格已保存: {filepath}")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"生成研究特征表格失败: {e}")
            return ""
    
    def generate_all_visualizations(self, data: Dict[str, Any]) -> Dict[str, str]:
        """生成所有可视化图表"""
        visualizations = {}

        try:
            logger.info(f"开始生成可视化图表，matplotlib可用: {MATPLOTLIB_AVAILABLE}")

            # 生成PRISMA流程图
            if 'search_stats' in data:
                logger.info("生成PRISMA流程图...")
                prisma_path = self.generate_prisma_flow_chart(data['search_stats'])
                if prisma_path:
                    visualizations['prisma_flow_chart'] = prisma_path
                    logger.info(f"PRISMA流程图已生成: {prisma_path}")

            # 生成偏倚风险总结图
            if 'quality_summary' in data:
                logger.info("生成偏倚风险总结图...")
                bias_path = self.generate_risk_of_bias_summary(data['quality_summary'])
                if bias_path:
                    visualizations['risk_of_bias_summary'] = bias_path
                    logger.info(f"偏倚风险总结图已生成: {bias_path}")

            # 生成证据质量分布图
            if 'quality_summary' in data:
                logger.info("生成证据质量分布图...")
                quality_path = self.generate_evidence_quality_chart(data['quality_summary'])
                if quality_path:
                    visualizations['evidence_quality_chart'] = quality_path
                    logger.info(f"证据质量分布图已生成: {quality_path}")

            # 生成研究特征表格
            if 'studies' in data:
                logger.info("生成研究特征表格...")
                table_path = self.generate_study_characteristics_table(data['studies'])
                if table_path:
                    visualizations['study_characteristics_table'] = table_path
                    logger.info(f"研究特征表格已生成: {table_path}")

            logger.info(f"成功生成了 {len(visualizations)} 个可视化图表")
            return visualizations

        except Exception as e:
            logger.error(f"生成可视化图表时出错: {e}", exc_info=True)
            return visualizations

    def _generate_text_prisma_chart(self, search_stats: Dict[str, Any]) -> str:
        """生成文本版PRISMA流程图（当matplotlib不可用时）"""
        try:
            # 创建文本版本的PRISMA图表
            filename = "prisma_flow_chart_text.md"
            filepath = self.images_dir / filename

            content = f"""# PRISMA 2020 流程图

## 文献筛选流程

1. **数据库检索识别的记录**: {search_stats.get('identified', 0)} 篇
2. **去除重复后的记录**: {search_stats.get('after_duplicates', 0)} 篇
3. **筛选的记录**: {search_stats.get('screened', 0)} 篇
4. **全文评估的文章**: {search_stats.get('full_text_assessed', 0)} 篇
5. **纳入定性综合的研究**: {search_stats.get('included_qualitative', 0)} 篇
6. **纳入定量综合的研究（荟萃分析）**: {search_stats.get('included_quantitative', 0)} 篇

## 排除情况

- **排除重复记录**: {search_stats.get('duplicates_removed', 0)} 篇
- **排除不相关记录**: {search_stats.get('excluded_screening', 0)} 篇
- **排除的全文文章**: {search_stats.get('excluded_full_text', 0)} 篇
"""

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)

            logger.info(f"文本版PRISMA流程图已保存: {filepath}")
            return str(filepath)

        except Exception as e:
            logger.error(f"生成文本版PRISMA流程图失败: {e}")
            return ""

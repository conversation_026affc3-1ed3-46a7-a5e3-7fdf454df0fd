<!-- ============================================================= -->
<!--  MODULE:    Journal Article Metadata Elements                 -->
<!--  VERSION:   ANSI/NISO JATS Version 1.3 (Z39.96-2021)          -->
<!--  DATE:      June 2021                                         -->
<!--                                                               -->
<!-- ============================================================= -->

<!-- ============================================================= -->
<!--                    PUBLIC DOCUMENT TYPE DEFINITION            -->
<!--                        TYPICAL INVOCATION                     -->
<!--
"-//NLM//DTD JATS (Z39.96) JATS DTD Suite Journal Article Metadata Elements v1.3 20210610//EN"
     Delivered as file "JATS-articlemeta1-3.ent"                   -->
<!-- ============================================================= -->

<!-- ============================================================= -->
<!-- SYSTEM:     JATS DTD Suite                                    -->
<!--                                                               -->
<!-- PURPOSE:    Names all elements used to describe the journal   -->
<!--             in which the journal article is published.        -->
<!--                                                               -->
<!-- TAG SET SPONSOR                                               -->
<!--             National Center for Biotechnology                 -->
<!--                Information (NCBI)                             -->
<!--             National Library of Medicine (NLM)                -->
<!--                                                               -->
<!-- CREATED FOR:                                                  -->
<!--             This module was created for the JATS DTD Suite.   -->
<!--             Digital archives and publishers may use the       -->
<!--             DTD as is for markup of journal literature or     -->
<!--             related material for archiving and transferring   -->
<!--             such material between archives or create a        -->
<!--             custom XML DTD from the Suite for                 -->
<!--             these purposes.                                   -->
<!--                                                               -->
<!--             This is in the public domain. An organization -->
<!--             that wishes to create its own DTD from the suite  -->
<!--             may do so without permission from NLM.            -->
<!--                                                               -->
<!--             The suite has been set up to be extended using a  -->
<!--             new DTD file and a new DTD-specific customization -->
<!--             module to redefine the many Parameter Entities.   -->
<!--             Do not modify the suite directly or redistribute  -->
<!--             modified versions of the suite.                   -->
<!--                                                               -->
<!--             In the interest of maintaining consistency and    -->
<!--             clarity for potential users, NLM requests:        -->
<!--                                                               -->
<!--             1. If you create a DTD from the JATS DTD Suite    -->
<!--                and intend to stay compatible with the suite,  -->
<!--                then please include the following statement    -->
<!--                as a comment in all of your DTD modules:       -->
<!--                   "Created from, and fully compatible with,   -->
<!--                    the ANSI/NISO Z39.96 Journal Article Tag   -->
<!--                    Suite (JATS)."                             -->
<!--                                                               -->
<!--             2. If you alter one or more modules of the suite, -->
<!--                then please rename your version and all its    -->
<!--                modules to avoid any confusion with the        -->
<!--                original suite. Also, please include the       -->
<!--                following statement as a comment in all your   -->
<!--                DTD modules:                                   -->
<!--                   "Based in part on, but not fully compatible -->
<!--                    with, the ANSI/NISO Z39.96 Journal Article -->
<!--                    Tag Suite (JATS)."                         -->
<!--                                                               -->
<!-- ORIGINAL CREATION DATE:                                       -->
<!--             December 2002                                     -->
<!--                                                               -->
<!-- CREATED BY: Mulberry Technologies, Inc. for the NISO Z39.96   -->
<!--             Working Group. Mulberry Technologies was          -->
<!--             supported by National Center for Biotechnology    -->
<!--             Information (NCBI), a center of the US National   -->
<!--             Library of Medicine (NLM).                        -->
<!--                                                               -->
<!--             This module is part of the JATS DTD Suite. The    -->
<!--             Suite is a continuation of work done by NCBI,     -->
<!--             Mulberry Technologies, and Inera Inc. on the NLM  -->
<!--             Journal Archiving and Interchange DTD Suite, which-->
<!--             was originally released in December, 2002.        -->
<!--                                                               -->
<!--             NLM thanks the Harvard University Libraries, both -->
<!--             for proposing that a draft archiving NLM DTD for  -->
<!--             life sciences journals be extended to accommodate -->
<!--             journals in all disciplines and for sponsoring    -->
<!--             Inera Inc.'s collaboration with other DTD         -->
<!--             authors in completing NLM Version 1.0. The        -->
<!--             Andrew W. Mellon Foundation provided support for  -->
<!--             these important contributions.                    -->
<!--                                                               -->
<!--             Suggestions for refinements and enhancements to   -->
<!--             this DTD should be sent in email to:              -->
<!--                 <EMAIL>                         -->
<!-- ============================================================= -->


<!-- ============================================================= -->
<!--                    DTD VERSION/CHANGE HISTORY                 -->
<!-- ============================================================= -->
<!--
Version  Reason/Occasion                   (who) vx.x (yyyy-mm-dd)
   ==============================================================
     JATS Version 1.3 (ANSI/NISO Z39.96-2021)
                                  (DAL/BTU) v1.3 (2021-06-10)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by both the NISO and ANSI memberships 
     to be changed. This version of the NISO JATS was approved 
     by ANSI vote on June 10, 2021, so it supersedes all Committee 
     Drafts as well as the draft version JATS 1.3 2021 (the version
     submitted for vote), becoming NISO JATS v1.3 20210610 
     (ANSI/NISO Z39.96-2019).

     The file names and formal public identifiers have been 
     changed in the modules and the catalogs.

 55. JATS 'v1.3d2 20201130' becomes 'v1.3 20210610' 
     (final ANSI vote date)
     'ANSI/NISO Z39.96-2019' becomes 'ANSI/NISO Z39.96-2021'

  ==============================================================
     JATS Version 1.3d2 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.3d2 (2020-11-30)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by the NISO and ANSI memberships 
     to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     of the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting; it is for Standing Committee review and
     testing. The formal public identifiers have been changed 
     in the modules and the catalogs.
         
 54. PROCESSING METADATA - A new container element was added as
     a child of <article>, <sub-article>, and <response> to 
     describe processing information to help determine the 
     exact tagset flavor used by this document instance. 
     (<processing-meta>)
     Current contents of <processing-meta>:
      - <restricted-by> - Names tighter schemas, subsets, or
           Guidelines followed by this document.
      - <extended-by> - Names extension or superset schemas
           followed by this document, such as TaxPub.
      - <custom-meta-group>
     Several new attributes were added to describe more 
     completely the flavor/version of JATS in which the 
     document was composed:
       - @tagset-family	 (JATS | BITS | STS)
       - @base-tagset    (Archiving | Publishing | Authoring)
       - @table-model	 (XHTML |OASIS-CALS | both | none)
       - @mathml-version (MathML 2.0 | MathML 3.0)
       - @dtd-version was also revised to be an optional
           value list (still on <article>)
       - math-representation - States the ways mathematical
            expressions appear in this document. Values are 
            an open, space-separated list, with values 
            probably one or more of the following words:
                mathml   tex   latex   images   plain-text 
            For example, both TeX and images (tex  images)
            For example, both MathML and TeX (mathml tex)
            Attribute order is irrelevant. For no math at 
            all, just skip this optional attribute.
  
 53. EXPANSION OF FIXED ATTRIBUTES LIST: There are four  
     content-defined attributes which are CDATA values 
     (undetermined) in Archiving, but which are named value 
     lists in Publishing/Authoring:
       - @fn-type 
       - @person-group-type
       - @pub-id-type (set up in this module)
       - @ref-type 
     In order to give users of Publishing and Authoing
     more flexibility to name new values without 
     destroying the benefit of named value lists:
       - A new value “custom” has been added to the named
           value list of each of these four attributes.
       - A new attribute @custom-type has been added to
           each element that takes one of these attributes.
     The new attribute @custom-type was also added to
     Green, so that documents valid to Blue or Pumpkin 
     would also be also valid to Green.
     In this module:
      - The element <article-id> was given the new 
        attribute @custom-type and "custom" was added to
        %pub-id-types (defined in the common module).
     The Best Practice rule (unenforceable in DTDs, but  
     enforceable in Schematron) is, that if you use the 
     value “custom” from one of these lists, you should 
     also record what type of 'custom' in the 
     @custom-type attribute.                        
    
 52. ISSUE SUBTITLE - Added <issue-subtitle> to 
     <issue-title-group> optional and repeatable

 51. ADDED ISSUE TITLE GROUP container element, for associating
     issue titles, subtitles, and translated titles, when there
     is more than one <issue>.

 50. ADDED <issue-title-group> added to <article-meta>
     (optional, repeatable)

 49. ASSIGNING AUTHORITY - Added @assigning-authority to the
     following elements, so that the type-of-identifier and
     the party-responsible-for-identifier could both be
     recorded.     
        - <self-uri>
        - <pub-date>
 
 48. JATS "v1.3d2 20201130" becomes 'v1.3 20210610' (second 
     committee draft following the final ANSI/NISO 1.2 vote)

   ==============================================================
     JATS Version 1.3d1 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.3.d1 (2019-08-31)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by the NISO and ANSI memberships 
     to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     of the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.

 47. ASSIGNING AUTHORITY - Added @assigning-authority to the
     following elements, so that the type-of-identifier and
     the party-responsible-for-identifier could both be
     recorded.     
        - article-id
        - article-version
        - award-id
        - compound-kwd
        - compound-subject
        - kwd 
        - kwd-group
        - nested-kwd
        - subject
        - subject-group
        - unstructured-kwd-group
        - Note: <pub-id> already had @assigning-authority
 
 46. JATS "1.2" becomes "v1.3d1 20190831" (first committee
     draft following the final ANSI/NISO vote)
    ==============================================================
     JATS Version 1.2 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.2     (2019-02-08)
    
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the both the NISO and ANSI membership 
     to be changed. This version of the NISO JATS was approved 
     by ANSI vote on Feb 08, 2019, so it supersedes all Committee 
     Drafts as well as the draft version JATS 1.2 2018 (the version
     submitted for vote), becoming NISO JATS v1.2 20190208 
     (ANSI/NISO Z39.96-2021).
  
 45. JATS "1.2" becomes "v1.2 20190208" (final ANSI vote date)
     "ANSI/NISO Z39.96-2015" becomes "ANSI/NISO Z39.96-2019"
     
   ==============================================================
     JATS Version 1.2 (ANSI/NISO Z39.96-2018)
                                   (DAL/BTU) v1.2     (2018-11-30)
    
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the both the NISO and ANSI membership 
     to be changed. This version of the NISO JATS was approved 
     by ANSI and NISO vote, so it supersedes all Committee Drafts
     and becoming NISO JATS 1.2 (ANSI/NISO Z39.96-2018).
  
 44. JATS "1.2d2" and "v1.2d2 20180401" became
     JATS "1.2" and "v1.2 20181130" 

    =============================================================
     JATS Version 1.2d2              (DAL/BTU) v1.2d2  (2018-04-01)

     JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
     BITS is under continuous maintenance, and is modified at the
     discretion of the working group.
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
  
 43. PUB-ID-TYPE - @pub-id-type changed back to a fixed list from 
     CDATA, reversing the decision of Version 1.2d1. Although 
     the value list is unchanged, best practice will be documented
     as using @pub-id-type for type and @assigning-authority
     for the responsible agency.
       - for <article-id>

 42. ASSIGNING AUTHORITY - Added @assigning-authority to the
     following elements, so that the type-of-identifier and
     the party-responsible-for-identifier could both be
     recorded. Previously, these values were both assigned to
     the attribute @pub-id-type.
         - <article-id>

 41. NON-MONETARY SUPPORT - Inside <article-meta> added new
     <support-group> to hold both funding and
     non-monetary support descriptions. <support-group> is 
     both a peer to <funding-group> (backward compatibility)
     and contains <funding-group>.
 
 40. PUBLICATION DATE-NOT-AVAILABLE - Inside <article-meta>, 
     <front-stub>, <event>, and <event-desc>
     added new element <pub-date-not-available>, as an 
     alternative to <pub-date>. The meaning is that a 
     publication date was (for whatever reason) not available. 
     Presence of the element says nothing about publication 
     status.
   
 39. VOCABULARY/TAXONOMY ATTRIBUTES - Added 2 new attributes for
     naming (and possibly linking to) general or controlled
     taxonomy, vocabulary, index, database or other source of
     terms (@vocab, and @vocab-identifier) to these
     elements (which could already take the attributes
     @vocab-term and @vocab-term-identifier):
        - <kwd>
        - <compound-kwd>
        - <nested-kwd>
        - <subject>
        - <compound-subject>

 38. BITS "2.0" and "v2.0 20151225" remains unchanged
   
     JATS "1.2d1" and "v1.2d1 20171231" became
     JATS "1.2d2" and "v1.2d2 20180401". 

     No module names were changed.
 
    =============================================================
     JATS Version 1.2d1 (ANSI/NISO Z39.96-2015)
                                   (DAL/BTU) v1.2d1  (2017-12-31)
 
     NISO JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
  
 37. JATS "1.2d1" and "v1.2d1 20170631" became
     JATS "1.2d1" and "v1.2d1 20171231". 

    =============================================================
     JATS Version 1.2d1              (DAL/BTU) v1.2d1  (2017-06-31)

     NISO JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.

 36. EVENTS - 
      - Added <event>, which will reside only inside
        <pub-history>. 
      - Added <pub-history> to hold <event>s. Unlike in BITS, 
        <pub-history> will contain ONLY <event>s.
      - Took <event> attributes from BITS. 
      - Added <article-version> to model of <event>.
  
 35. PUB-ID-TYPE - @pub-id-type changed from a fixed list to
     CDATA for all usages of the attribute.

 34. ARTICLE META MODEL
     - NO DATE - <pub-date> (optional repeatable) is now
       followed by the optional new element 
       <pub-date-not-available>. 

     - ARTICLE VERSION - Added new element <article-version>
       inside <article-meta> to hold one version number for  
       the article. <article-version> element may repeat inside
       new element <article-version-alternatives> to hold 
       version numbers for this article from different systems.
 
     - PUBLICATION HISTORY - Added new element based on BITS,
       <pub-history>, to hold <event>s as <history> holds
       <date>s. New internal elements: <event>, <event-desc>
   
 33. VOCABULARY/TAXONOMY ATTRIBUTES - Added 4 new attributes for
     naming (and possibly linking to) a general or controlled
     taxonomy, vocabulary, index, database or other source of
     terms: @vocab, @vocab-identifier, @vocab-term, and
     @vocab-term-identifier

      - Attributes @vocab and @vocab-identifier were 
        added to the elements:
        - <kwd-group>
        - <subj-group>
        - <unstructured-kwd-group>

      - Attributes @vocab-term and @vocab-term-identifier were
        added to the elements:
        - <kwd>
        - <compound-kwd>
        - <nested-kwd>
        - <subject>
        - <compound-subject>
 
 32. JATS became version "1.2d1" and "v1.2d1 20170631" 

     =============================================================
     JATS Version 1.1                (DAL/BTU) v1.1  (2015-12-15)

     JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
     JATS 1.1 was approved in late 2015, so the formal public 
     identifiers were changed in the modules and the catalogs.
     No model or attribute changes were made at this time. 
 
 31. JATS became version "1.1" and "v1.1 20151215"
     BITS remained version "2.0" but becomes "v2.0 20151225"

     =============================================================
     JATS Version 1.1d3             (DAL/BTU) v1.1d3  (2015-03-01)
   
     The changes in this release are in response to NISO 
     License and Indicators Recommended Practice.

     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the NISO membership to be changed. This
     Committee Draft 1.1d3 will be sent to the NISO voting
     membership, to become (if approved) NISO JATS 1.1.
     
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee.

 30. JATS became version "1.1d3" and "v1.1d3 20150301"
  
     =============================================================
     JATS Version 1.1d2              (DAL/BTU) v1.1d2 (2014-09-30)
   
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the NISO membership to be changed. This
     Committee Draft 1.1d2 will be sent to the NISO voting
     membership, to become (if approved) NISO JATS 1.1.
     
     This catalog represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee.
 
 29. <VOLUME> INSIDE <ARTICLE-META>

   - Allowed <volume> to repeat inside <article-meta>, the types to 
     be distinguished using @content-type

   - Added new optional element <volume-issue-group> inside 
     <article-meta) following all volume and issue elements
     to hold volume-issue pairs (or n-tuples) when a second
     and subsequent <volume> has its own related issue
     information.

   - Made <issue> repeatable, for those who choose not to use
     the new wrapper element.
   
 28. JATS became version "1.1d2" and "v1.1d2 20140930//EN"

     =============================================================
     NISO JATS Version 1.1d1           (DAL/BTU) v1.1 (2013-11-15)
   
     ANSI/NISO Z39.96-2012 (pre-release for V1.0 BITS; Version 1.1d1) 

     NISO JATS Standing Committee met and answered the requests and
     suggestions from the NISO request forms.

     Details concerning ANSI/NISO Z39.96-2012 JATS-based DTDs,  
     XSDs, RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.3d2/

 27. NEW <era> ELEMENT
     Added <era> through date-model
      - <pub-date>

 26. GLOBAL ATTRIBUTES - Added the new parameter entity 
         %jats-common-atts;
     to every element in this module. This PE adds (for now) the
     @id attribute and the @xml:base attribute to every element,
     whether metadata or narrative.
     Since the @id in this parameter entity is optional, a second
     parameter entity jats-common-atts-id-required was also added.
     The two are kept in sync with the jats-base-atts parameter 
     entity.
     This added a new attribute list to:
      - article-categories
      - article-meta
      - counts
      - history
      - title-group                          

 25. ABSTRACTS AND KEYWORDS
       Changed "abstract*" to "(%abstract.class;)*"
       and "kwd-group*" to "(%kwd-group.class;)*" since those
       classes now exist. Elements should have a limited number of
       ways to be invoked.
        - article-meta (through %article-meta-model;)

     =============================================================
     NISO JATS Version 1.0             (DAL/BTU) v1.0 (2012-xx-xx)
   
     ANSI/NISO Z39.96-2012 (Version 1.0) 

     Details concerning ANSI/NISO Z39.96-2012 JATS-based DTDs,  
     XSDs, RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.3d2/
   
 24. RELATED OBJECT - Added <related-object> everywhere
     <related-article> was used, including inside 
     <article-meta> using article-meta-model.

 23. CONTRIB-GROUP AND CONTRIB MODULE SHIFT - Moved <contrib>
     and <contrib-group> to common.ent, since they are now used
     in both article metadata and journal metadata.

 22. CONTRIBUTOR MODEL - Added "contrib-id.class" to the beginning
     at the <contrib> model, to hold identifiers for people,
     such as publishers-specific IDs, ORCIDs, and JST and NII 
     identifiers.
   
 21. COUNTS - Generic <count> element added to the <counts>
     wrapper. This element is allowed to repeat, to count
     anything a publisher or archive may wish to count.
     The @count-type attribute will name what is being counted
     (such as figures, tables, sections, contributors, images,
     etc.) The @count attribute will state how many of the 
     objects are in the document.
     
     Although a publisher or archive could choose to label
     all the counted objects in a document this way, the
     specific count elements (fig-count, equation-count,
     word-count, etc.) will be retained for backwards
     compatibility.
     
     New parameter entities: count-atts.
   
 20. PUBLICATION DATE ATTRIBUTES - Added to the attributes for
     <pub-date> (through @pub-date-atts):
       a) @iso-8601-date - ISO 8601 standard date format
          of the publication date. Format  YYYY-MM-DDThh:mm:ss 
       b) @calendar - Name of the calendar used (@calendar) in
          naming this publication date (such as "Gregorian", 
          "Japanese" (Emperor years), or "Islamic").
       c) @date-type to record lifecycle information as to which
          publication date is involved (such as retracted, corrected,
          preprint).
       d) @publication-format to hold what format or media was 
          published on this date (such as "print" or
          "electronic").
       e) @xml:lang (comments below said was this added for 0.4).
   
 19. COMPOUND KEYWORD PARTS - Allowed the individual compound 
     keyword parts (<compound-kwd-part> to be as complex as 
     ordinary keywords <kwd>), by giving them the same model 
     as <kwd>.
   
 18. NESTED KEYWORDS - Added new keyword type element for nested
     keywords (<nested-kwd>), to hold hierarchical keywords such
     as taxonomic structures. New PEs %nested-kwd-model  and
     %nested-kwd-atts 
   
 17. MODULE MOVEMENT (from increased reference content)

     a) Element <degrees> - Moved <degrees> and its parameter
        entities from this module from JATS-common.ent.
   
     b) Element <supplement> - Moved <supplement> and its parameter
        entities from this module from JATS-common.ent.
   
     c) Element <conf-sponsor> - Moved <conf-sponsor> and its parameter
        entities from this module from JATS-common.ent.
   
     d) Element <on-behalf-of> - Moved <on-behalf-of> and its parameter
        entities from this module from JATS-common.ent.
   
 16. Added name comment to %counts-model;

 15. Updated the DTD-version attribute to "1.0" and the formal
     public identifier to the date: "v1.0 20120330//EN".

     =============================================================
     Version 0.4                       (DAL/BTU) v0.4 (2011-01-31)
   
     This Tag Set is in the process of becoming a NISO standard.
     The version numbers are starting over from 0.4", as a Trial
     Use Draft, to be made into "Version 1.0" when the Tag Suite 
     becomes a NISO standard. Thus, the version number that would
     have been "NLM Version 3.1 Draft" has become NISO JATS
     "Version 0.4".

     Details on NISO Trial Use Draft Version 0.4 are available at
           http://jats.nlm.nih.gov/JATS-0.4.
   
 14. Updated the DTD-version attribute to "0.4" 
   
 13. Updated the public identifier to "v0.4 20110131", 
     modified the formal public identifier to include "JATS (Z39.96)",
     and the filename as delivered to include "JATS" and the
     new version number "0".
           http://jats.nlm.nih.gov/0.4.

 12. UNSTRUCTURED KEYWORD GROUP ATTRIBUTES - Made
     %unstructured-kwd-group-atts; into its own PE, as
     documentation stated had already been done.

 11. KEYWORD ATTRIBUTES - To make the tag set more consistent,
     added @content-type to <kwd>, since <compound-kwd> already
     took this attribute. Through %kwd-atts;

 10. I18N - At the request of the SBJ Working Group (Japan)
     made <series-text> repeatable inside <article-categories> using
     %article-categories-model;

  9. TITLE ELEMENTS - Removed the dependency which had both
     <subtitle> and <alt-title> modeled with the same parameter
     entity %title-elements;. Created new PEs for each element
     but set them (as the default) to %title-elements so that no
     customization would break. Models changed:
       - alt-title    %alt-title-elements; defined in common.ent
       - subtitle     %subtitle-elements; (moved to this
           module)

  8. AFFILIATION ALTERNATIVES - Added the element <aff-alternatives>
     to <article-meta> through %article meta-model;. This element
     will hold multiple <aff>s that are a representation of a
     single affiliation, for example, the name of an institution
     in two languages or two scripts.

  7. @SPECIFIC-USE and @XML:LANG - Added the @specific-use and
     @xml:lang to the following elements:
      - alt-title through alt-title-atts (both)
      - article-id through article-id-atts (@specific-use only)
      - author-comment through author-comment-atts (both)
      - author-notes through author-notes-atts (both)
      - article-id through article-id-atts (@xml:lang)
      - contrib through contrib-atts (@specific-use only)
      - contrib-group through contrib-group-atts (@xml:lang)
      - corresp through corresp-atts (@specific-use)
      - degrees through degrees-atts (both)
      - kwd-group through kwd-group-atts (@specific-use only
             @xml:lang already there)
      - on-behalf-of through on-behalf-of-atts (both) NEW PE
      - product through product-atts (both)
      - pub-date through pub-date-atts (@xml:lang)
      - self-uri through self-uri-atts (both)
      - series-text through series-text-atts (both)
      - series-title through series-title-atts (both)
      - string-conf through string-conf-atts (both)
      - subj-group through subj-group-atts (both)
      - subtitle through subtitle-atts (@specific only
             @xml:lang already there)
      - supplement through supplement-atts (both)

  6. TITLE ELEMENTS - Removed the dependency which had both
     <subtitle> and <alt-title> modeled with the same parameter
     entity %title-elements;. Created new PEs for each element
     but set them (as the default) to %title-elements so that no
     customization would break.

  5. COUNT ATTRIBUTES - Removed the dependency which had all of
     the count elements using a single attribute list (%count-atts;)
     Created new attributes list PEs for each of them: equation-count,
     fig-count, page-count, ref-count, table-count, and word-count.
     *****Customization Alert: New parameter entities could break
     some customizations. Check the attribute list parameter entity
     for each of the count elements. *****

  4. COMPOUND SUBJECT - Created a new element <compound-subject>
     that acts just like <compound-kwd>, in that it takes one or
     more <compound-subject-part>s to allow a complex, multi-
     part subject to be captured. New elements, attribute lists,
     and parameter entities. (An example of a compound subject
     is a code with a textual keyword (A11 Permeability).
     Allowed <compound-subject> as an alternative to <subject>
     inside a <subj-group>.

  3. SERIES TEXT ATTRIBUTES - Both <series-title> and
     <series-text> used to use the PE %series-title-atts;. Added the
     PE %series-text-atts;, which is the only one <series-text>
     uses. No documents need change.
     *****Customization Alert: New parameter entity could break some
     customizations. Check your <series-text> attributes. *****

  2. TRANSLATED ABSTRACT ATTRIBUTES - Both <abstract> and
     <trans-abstract> used to use the PE %abstract-atts;. Added the
     PE %trans-abstract-atts;, which is the only one <trans-abstract>
     uses. No documents need change.
     *****Customization Alert: New parameter entity could break some
     customizations. Check your <trans-abstract> attributes. *****

     =============================================================
     Version 3.0                       (DAL/BTU) v3.0 (2007-10-31)

     Version 3.0 is the first non-backward-compatible release.
     In addition to the usual incremental changes, some
     elements and attributes have been renamed and/or remodeled
     to better meet user needs and to increase tag set consistency.
     All module change histories are available through the Tag Suite
     web site at http://dtd.nlm.nih.gov.

     Details on version 3.0 are available at
           http://dtd.nlm.nih.gov/3.0.

  1. Updated public identifier to "v3.0 20071031//EN"              -->

<!-- ============================================================= -->
<!-- Details concerning 
     ANSI/NISO Z39.96-2015 JATS-based DTDs, XSDs, 
     RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.1/                            -->

<!-- ============================================================= -->
<!--                    PARAMETER ENTITIES FOR ATTRIBUTE LISTS     -->
<!-- ============================================================= -->


<!--                    ABSTRACT ATTRIBUTES                        -->
<!--                    Attributes for the <abstract> element      -->
<!ENTITY % abstract-atts
            "%jats-common-atts;                                       
             abstract-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    ALTERNATE TITLE ATTRIBUTES                 -->
<!--                    Attributes for the <alt-title> element     -->
<!ENTITY % alt-title-atts
            "%jats-common-atts;                                       
             alt-title-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    ARTICLE CATEGORIES ATTRIBUTES              -->
<!--                    Attributes for the <article-categories> 
                        element                                    -->
<!ENTITY % article-categories-atts
            "%jats-common-atts;"                                     >


<!--                    ARTICLE METADATA ATTRIBUTES                -->
<!--                    Attributes for the <article-meta> element  -->
<!ENTITY % article-meta-atts
            "%jats-common-atts;"                                     >


<!--                    ARTICLE VERSION ATTRIBUTES                 -->
<!--                    Attributes for the <article-version> 
                        element                                    -->
<!ENTITY % article-version-atts
            "%jats-common-atts;                                       
             article-version-type
                        CDATA                             #IMPLIED
             designator CDATA                             #IMPLIED
             assigning-authority
                        CDATA                             #IMPLIED
             vocab      CDATA                             #IMPLIED
             vocab-identifier
                        CDATA                             #IMPLIED
             vocab-term CDATA                             #IMPLIED
             vocab-term-identifier
                        CDATA                             #IMPLIED
             iso-8601-date
                        CDATA                             #IMPLIED
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             %might-link-atts;"                                      >


<!--                    ARTICLE VERSION ALTERNATIVE ATTRIBUTES     -->
<!--                    Attributes for the 
                        <article-version-alternatives> element     -->
<!ENTITY % article-version-alternatives-atts
            "%jats-common-atts;"                                     >



<!--                    AUTHOR COMMENT ATTRIBUTES                  -->
<!--                    Attributes for the <author-comment> element-->
<!ENTITY % author-comment-atts
            "%jats-common-atts;                                       
             content-type
                       CDATA                              #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    AUTHOR NOTES ATTRIBUTES                    -->
<!--                    Attributes for the <author-notes> element  -->
<!ENTITY % author-notes-atts
            "%jats-common-atts;                                       
             rid        IDREFS                            #IMPLIED
             specific-use
                        CDATA                             #IMPLIED"  >


<!--                    ARTICLE IDENTIFIER ATTRIBUTES              -->
<!--                    Attributes for the <article-id> element    -->
<!ENTITY % article-id-atts
            "%jats-common-atts;                                       
             pub-id-type
                        (%pub-id-types;)                  #IMPLIED
             custom-type
                        CDATA                             #IMPLIED
             assigning-authority                
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED"  >


<!--                    COMPOUND KEYWORD ATTRIBUTES                -->
<!--                    Attributes for the <compound-kwd> element
                                                                   -->
<!ENTITY % compound-kwd-atts
            "%jats-common-atts;
             assigning-authority
                        CDATA                             #IMPLIED
             vocab      CDATA                             #IMPLIED
             vocab-identifier
                        CDATA                             #IMPLIED
             vocab-term CDATA                             #IMPLIED
             vocab-term-identifier
                        CDATA                             #IMPLIED
             content-type
                        CDATA                             #IMPLIED"  >


<!--                    COMPOUND KEYWORD PART ATTRIBUTES           -->
<!--                    Attributes for the <compound-kwd-part>
                        element                                    -->
<!ENTITY % compound-kwd-part-atts
            "%jats-common-atts;                                       
             content-type
                        CDATA                             #IMPLIED"  >


<!--                    COMPOUND SUBJECT ATTRIBUTES                -->
<!--                    Attributes for the <compound-subject> element
                                                                   -->
<!ENTITY % compound-subject-atts
            "%jats-common-atts;
             assigning-authority
                        CDATA                             #IMPLIED                                        
             vocab      CDATA                             #IMPLIED
             vocab-identifier
                        CDATA                             #IMPLIED
             vocab-term CDATA                             #IMPLIED
             vocab-term-identifier
                        CDATA                             #IMPLIED
             content-type
                        CDATA                             #IMPLIED"  >


<!--                    COMPOUND SUBJECT PART ATTRIBUTES           -->
<!--                    Attributes for the <compound-subject-part>
                        element                                    -->
<!ENTITY % compound-subject-part-atts
            "%jats-common-atts;                                       
             content-type
                        CDATA                             #IMPLIED"  >


<!--                    CONFERENCE ACRONYM ATTRIBUTES              -->
<!--                    Attributes for the <conf-acronym> element  -->
<!ENTITY % conf-acronym-atts
            "%jats-common-atts;                                       
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    CONFERENCE NUMBER ATTRIBUTES               -->
<!--                    Attributes for the <conf-num> element      -->
<!ENTITY % conf-num-atts
            "%jats-common-atts;                                       
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    CONFERENCE THEME ATTRIBUTES                -->
<!--                    Attributes for the <conf-theme> element    -->
<!ENTITY % conf-theme-atts
            "%jats-common-atts;                                       
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    CONFERENCE ATTRIBUTES                      -->
<!--                    Attributes for the <conference> element    -->
<!ENTITY % conference-atts
            "%jats-common-atts;                                       
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED
             %might-link-atts;"                                      >


<!--                    CORRESPONDING ATTRIBUTES                   -->
<!--                    Attributes for the <corresp> element       -->
<!ENTITY % corresp-atts
            "%jats-common-atts;                                       
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    COUNT ATTRIBUTES                           -->
<!--                    Attributes for the <count> element         -->
<!ENTITY % count-atts
            "%jats-common-atts;                                       
             count-type CDATA                             #REQUIRED 
             count      NMTOKEN                           #REQUIRED" >


<!--                    COUNTS ATTRIBUTES                          -->
<!--                    Attributes for the <counts> element        -->
<!ENTITY % counts-atts
            "%jats-common-atts;"                                     >


<!--                    DATE NOT AVAILABLE ATTRIBUTES              -->
<!--                    Attributes for the <pub-date-not-available> 
                        element                                    -->
<!ENTITY % pub-date-not-available-atts
            "%jats-common-atts;                                       
             specific-use
                        CDATA                             #IMPLIED"  >


<!--                    EQUATION COUNT ATTRIBUTES                  -->
<!--                    Attributes for the <equation-count> element-->
<!ENTITY % equation-count-atts
            "%jats-common-atts;                                       
             count      NMTOKEN                          #REQUIRED"  >

<!--                    PUBLICATION EVENT ATTRIBUTES               -->
<!--                    Attributes for the <event> element         -->
<!ENTITY % event-atts
           "%jats-common-atts;
             event-type CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    EVENT DESCRIPTION ATTRIBUTES               -->
<!--                    Attributes used with the <event-desc>
                        element.                                   -->
<!ENTITY % event-desc-atts
           "%jats-common-atts;
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    EXTENDED BY ATTRIBUTES                     -->
<!--                    Attributes for the <extended-by> element   -->
<!ENTITY % extended-by-atts
            "%jats-common-atts;                                       
             assigning-authority
                        CDATA                             #IMPLIED
             designator CDATA                             #IMPLIED
             vocab      CDATA                             #IMPLIED
             vocab-identifier
                        CDATA                             #IMPLIED
             vocab-term CDATA                             #IMPLIED
             vocab-term-identifier
                        CDATA                             #IMPLIED
             %might-link-atts;"                                      >


<!--                    FIGURE COUNT ATTRIBUTES                    -->
<!--                    Attributes for the <fig-count> element     -->
<!ENTITY % fig-count-atts
            "%jats-common-atts;                                       
             count      NMTOKEN                           #REQUIRED" >


<!--                    HISTORY ATTRIBUTES                         -->
<!--                    Attributes for the <history> element       -->
<!ENTITY % history-atts
            "%jats-common-atts;"                                     >

<!--                    ISSUE TITLE GROUP ATTRIBUTES               -->
<!--                    Attributes for the <issue-title-group> 
                        element                                    -->
<!ENTITY % issue-title-group-atts
            "%jats-common-atts;                                       
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >
             

<!--                    KEYWORD ATTRIBUTES                         -->
<!--                    Attributes for the <kwd> element           -->
<!ENTITY % kwd-atts
            "%jats-common-atts;
             assigning-authority
                        CDATA                             #IMPLIED
             vocab      CDATA                             #IMPLIED
             vocab-identifier
                        CDATA                             #IMPLIED
             vocab-term CDATA                             #IMPLIED
             vocab-term-identifier
                        CDATA                             #IMPLIED
             content-type
                        CDATA                             #IMPLIED"  >


<!--                    KEYWORD GROUP ATTRIBUTES                   -->
<!--                    Attributes for the <kwd-group> element     -->
<!ENTITY % kwd-group-atts
            "%jats-common-atts;                                       
             kwd-group-type
                        CDATA                             #IMPLIED
             assigning-authority
                        CDATA                             #IMPLIED  
             vocab      CDATA                             #IMPLIED
             vocab-identifier
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    NESTED KEYWORD ATTRIBUTES                  -->
<!--                    Attributes for the <nested-kwd> element    -->
<!ENTITY % nested-kwd-atts
            "%jats-common-atts;
             assigning-authority
                        CDATA                             #IMPLIED                                       
             vocab      CDATA                             #IMPLIED
             vocab-identifier
                        CDATA                             #IMPLIED
             vocab-term CDATA                             #IMPLIED
             vocab-term-identifier
                        CDATA                             #IMPLIED
             content-type
                       CDATA                             #IMPLIED"  >


<!--                    PAGE COUNT ATTRIBUTES                      -->
<!--                    Attributes for the <page-count> element    -->
<!ENTITY % page-count-atts
            "%jats-common-atts;                                       
             count      NMTOKEN                           #REQUIRED" >


<!--                    PROCESSING METADATA CONTAINER ATTRIBUTES   -->
<!--                    Attributes for the <processing-meta>
                        element                                    -->
<!ENTITY % processing-meta-atts
            "%jats-common-atts;
             tagset-family
                        (jats | bits | sts)               #IMPLIED
             base-tagset 
                        (publishing | archiving | authoring)   
                                                          #IMPLIED
             table-model
                        (xhtml | oasis | both | none)     #IMPLIED
             math-representation
                        NMTOKENS                          #IMPLIED
             mathml-version
                        (2.0 | 3.0)                       #IMPLIED"  >
             
             
<!--                    PRODUCT ATTRIBUTES                         -->
<!--                    Attributes for the <product> element       -->
<!ENTITY % product-atts
            "%jats-common-atts;                                       
             product-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED
             %might-link-atts;"                                      >


<!--                    PUBLICATION DATE ATTRIBUTES                -->
<!--                    Attributes for the <pub-date> element      -->
<!ENTITY % pub-date-atts
            "%jats-common-atts;                                       
             pub-type   CDATA                             #IMPLIED
             publication-format
                        CDATA                             #IMPLIED
             assigning-authority
                        CDATA                             #IMPLIED
             date-type  CDATA                             #IMPLIED
             iso-8601-date
                        CDATA                             #IMPLIED
             calendar   CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    PUBLICATION HISTORY ATTRIBUTES             -->
<!--                    Attributes for the <pub-history> element   -->
<!ENTITY % pub-history-atts
           "%jats-common-atts;"                                      >


<!--                    REFERENCE (CITATION) COUNT ATTRIBUTES      -->
<!--                    Attributes for the <ref-count> element     -->
<!ENTITY % ref-count-atts
            "%jats-common-atts;                                       
             count      NMTOKEN                           #REQUIRED" >


<!--                    RESTRICTED BY ATTRIBUTES                   -->
<!--                    Attributes for the <restricted-by> element -->
<!ENTITY % restricted-by-atts
            "%jats-common-atts; 
             assigning-authority
                        CDATA                             #IMPLIED
             designator CDATA                             #IMPLIED
             vocab      CDATA                             #IMPLIED
             vocab-identifier
                        CDATA                             #IMPLIED
             vocab-term CDATA                             #IMPLIED
             vocab-term-identifier
                        CDATA                             #IMPLIED
             %might-link-atts;"                                      >


<!--                    SELF URI ATTRIBUTES                        -->
<!--                    Attributes for the <self-uri> element      -->
<!ENTITY % self-uri-atts
            "%jats-common-atts;                                       
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             assigning-authority                
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED
             %might-link-atts;"                                      >


<!--                    SERIES TEXT ATTRIBUTES                     -->
<!--                    Attributes for the <series-text> element   -->
<!ENTITY % series-text-atts
            "%jats-common-atts;                                       
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    SERIES TITLE ATTRIBUTES                    -->
<!--                    Attributes for the <series-title> element  -->
<!ENTITY % series-title-atts
            "%jats-common-atts;                                       
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    STRING CONFERENCE ATTRIBUTES               -->
<!--                    Attributes for the <string-conf> element   -->
<!ENTITY % string-conf-atts
            "%jats-common-atts;                                       
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    SUBJECT GROUP ATTRIBUTES                   -->
<!--                    Attributes for the <subj-group> element    -->
<!ENTITY % subj-group-atts
            "%jats-common-atts;                                       
             subj-group-type
                        CDATA                             #IMPLIED
             assigning-authority
                        CDATA                             #IMPLIED 
             vocab      CDATA                             #IMPLIED
             vocab-identifier
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >

<!--                    SUBJECT ATTRIBUTES                         -->
<!--                    Attributes for the <subject> element       -->
<!ENTITY % subject-atts
            "%jats-common-atts;
             assigning-authority
                        CDATA                             #IMPLIED                                        
             vocab      CDATA                             #IMPLIED
             vocab-identifier
                        CDATA                             #IMPLIED
             vocab-term CDATA                             #IMPLIED
             vocab-term-identifier
                        CDATA                             #IMPLIED
             content-type
                        CDATA                             #IMPLIED"  >


<!--                    SUBTITLE ATTRIBUTES                        -->
<!--                    Attributes for the <subtitle> element      -->
<!ENTITY % subtitle-atts
            "%jats-common-atts;                                       
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    TABLE COUNT ATTRIBUTES                     -->
<!--                    Attributes for the <table-count> element   -->
<!ENTITY % table-count-atts
            "%jats-common-atts;                                       
             count      NMTOKEN                           #REQUIRED" >


<!--                    TITLE GROUP ATTRIBUTES                     -->
<!--                    Attributes for the <title-group> element   -->
<!ENTITY % title-group-atts
            "%jats-common-atts;"                                     >


<!--                    TRANSLATED ABSTRACT ATTRIBUTES             -->
<!--                    Attributes for the <trans-abstract> element-->
<!ENTITY % trans-abstract-atts
            "%jats-common-atts;                                       
             abstract-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    UNSTRUCTURED KEYWORD GROUP ATTRIBUTES      -->
<!--                    Attributes for the <unstructured-kwd-group>
                                                       element     -->
<!ENTITY % unstructured-kwd-group-atts
            "%jats-common-atts;
             assigning-authority
                        CDATA                             #IMPLIED 
             kwd-group-type
                        CDATA                             #IMPLIED
             vocab      CDATA                             #IMPLIED
             vocab-identifier
                        CDATA                             #IMPLIED 
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    WORD COUNT ATTRIBUTES                      -->
<!--                    Attributes for the <word-count> element    -->
<!ENTITY % word-count-atts
            "%jats-common-atts;                                       
             count      NMTOKEN                           #REQUIRED" >


<!-- ============================================================= -->
<!--                    ARTICLE METADATA                           -->
<!-- ============================================================= -->


<!--                    ARTICLE METADATA MODEL                     -->
<!--                    Complete content model for the <article-meta>
                        element, which names the journal article
                        metadata                                   -->
<!ENTITY % article-meta-model
                       "(article-id*, (%article-version.class;)?, 
                         article-categories?, title-group?,
                         ( %contrib-group.class; |
                           %aff-alternatives.class;)*,
                         author-notes?,  ( (%pub-date.class;)* | 
                         pub-date-not-available?), 
                         volume*, volume-id*, volume-series?,
                         issue*, issue-id*, 
                         issue-title*, issue-title-group*, 
                         issue-sponsor*, issue-part?, 
                         volume-issue-group*, isbn*,
                         supplement?,
                         ( (fpage, lpage?, page-range?) |
                            elocation-id )?,
                         (%address-link.class; | product |
                          supplementary-material)*,
                         history?, pub-history?, permissions?, 
                         self-uri*, (%related-article.class;)*, 
                         (%abstract.class;)*,
                         trans-abstract*, (%kwd-group.class;)*, 
                         funding-group*, support-group*,   
                         conference*, counts?, 
                         custom-meta-group?)"                        >


<!--                    ARTICLE METADATA                           -->
<!--                    Metadata that identifies this article, for
                        example, bibliographic information such as
                        the title, author, and copyright year.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=article-meta
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=article-meta
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=article-meta
                                                                   -->
<!ELEMENT  article-meta %article-meta-model;                         >
<!ATTLIST  article-meta
             %article-meta-atts;                                     >


<!-- ============================================================= -->
<!--                    ARTICLE METADATA ELEMENTS                  -->
<!-- ============================================================= -->


<!--                    ARTICLE IDENTIFIER                         -->
<!--                    Optional element, used to hold one of the
                        "unique identifiers" that have been assigned
                        at various times to an article.  Such
                        identifiers may come from a publisher, a
                        jobber, from PubMed Central, etc.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=article-id
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=article-id
                                                                   -->
<!ELEMENT  article-id   (#PCDATA)                                    >
<!ATTLIST  article-id
             %article-id-atts;                                       >


<!--                    ARTICLE VERSION                            -->
<!--                    Optional element, used to hold one of the
                        "version" identifiers (such as version type, 
                        version status, version statement, or version
                        number) that have been assigned to the 
                        current article. 
                        Remarks: A typical version identifier might 
                        use terminology from the NISO Journal Article 
                        Versions Recommendations (such as "preprint"
                        or "copy-of-record", but it might also be a
                        publishers internal versioning number such as
                        "0.9".
                           The element is allowed to repeat inside a
                        <article-version-alternatives> element to hold
                         version numbers from more than one versioning
                         system. A given article should only be a
                         single version in any given system.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=article-version
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=article-version
                                                                   -->
<!ELEMENT  article-version
                        (#PCDATA)                                    >
<!ATTLIST  article-version
             %article-version-atts;                                  >


<!--                    ARTICLE VERSION ALTERNATIVES               -->
<!--                    Container element to hold multiple
                        <article-version> elements, when the version
                        number/identifier for more than one system
                        needs to be recorded. A given article should
                        only be a single version in any given system.
                        Details at:              
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=article-version-alternatives
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=article-version-alternatives 
                                                                   -->
<!ELEMENT  article-version-alternatives
                        (article-version+)                           >
<!ATTLIST  article-version-alternatives
             %article-version-alternatives-atts;                     >


<!-- ============================================================= -->
<!--                    ARTICLE GROUPING DATA (ARTICLE METADATA)   -->
<!-- ============================================================= -->


<!--                    ARTICLE CATEGORIES MODEL                   -->
<!--                    Complete content model for the
                        <article-categories> element               -->
<!ENTITY % article-categories-model
                        "(subj-group*, series-title*, series-text*)" >


<!--                    ARTICLE GROUPING DATA                      -->
<!--                    Container for elements that may be used to
                        group articles into related clusters
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=article-categories
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=article-categories
                                                                   -->
<!ELEMENT  article-categories
                        %article-categories-model;                   >
<!ATTLIST  article-categories
             %article-categories-atts;                               >


<!--                    SUBJECT GROUP MODEL                        -->
<!--                    Complete content model for the <subj-group>
                        element                                    -->
<!ENTITY % subj-group-model
                        "((subject | compound-subject)+, subj-group*)"
                                                                     >


<!--                    GROUPING ARTICLES IN TITLED CATEGORIES
                        Container element that collects multiple
                        subjects and/or hierarchically lower subject
                        groups.
                        Remarks: For some journals, articles are
                        grouped into categories, with the category
                        indicated in the article's display.
                        Sometimes the grouping or category refers
                        to the type of article, such as "Essay",
                        "Commentary", or "Article".  Sometimes the
                        grouping refers to subject areas, such as
                        "Physical Sciences", "Biological Sciences",
                        or "Social Sciences".
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=subj-group
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=subj-group
                                                                   -->
<!ELEMENT  subj-group   %subj-group-model;                           >
<!ATTLIST  subj-group
             %subj-group-atts;                                       >


<!--                    SUBJECT ELEMENTS                           -->
<!--                    Elements that may be used, along with data
                        characters inside the content model of the
                        <subject> element                          -->
<!ENTITY % subject-elements
                        "| %emphasis.class; | %inline-display.class; |
                         %inline-math.class; |
                         %phrase-content.class; | %subsup.class;"    >


<!--                    SUBJECT NAME                               -->
<!--                    The name of one of the subject groups used
                        to describe an article.  Such groups are
                        used, typically, to provide headings for
                        groups of articles in a printed or online
                        generated Table of Contents.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=subject
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=subject
                                                                   -->
<!ELEMENT  subject      (#PCDATA %subject-elements;)*                >
<!ATTLIST  subject
             %subject-atts;                                          >


<!--                    COMPOUND SUBJECT MODEL                     -->
<!--                    Complete content model for the
                        <compound-subject> element                 -->
<!ENTITY % compound-subject-model
                        "(compound-subject-part+)"                   >


<!--                    COMPOUND SUBJECT NAME                      -->
<!--                    Container element to hold all the parts of a
                        multi-part subject, for example, a subject that
                        is comprised of both a term and a code value
                        that represents that term.
                        Related Elements: If a subject is only a word
                        or phrase (Neuroscience, Physical Sciences),
                        the simple <subject> element can be used to
                        capture this information. If a subject is
                        logically both a term and a code
                        (A11 Permeability) but only one of those
                        objects needs to be captured, a <subject>
                        element may also be used. If it is useful to
                        record both the code and the term, they can
                        each be captured as one
                        <compound-subject-part>s inside a
                        <compound-subject>.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=compound-subject
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=compound-subject
                                                                   -->
<!ELEMENT  compound-subject
                        %compound-subject-model;                     >
<!ATTLIST  compound-subject
             %compound-subject-atts;                                 >


<!--                    COMPOUND SUBJECT PART ELEMENTS             -->
<!--                    Elements that may be used, along with data
                        characters inside the content model of the
                        <compound-subject> element                 -->
<!ENTITY % compound-subject-part-elements
                        "| %emphasis.class; | %inline-display.class; |
                         %inline-math.class; |
                         %phrase-content.class; | %subsup.class;"    >


<!--                    COMPOUND SUBJECT PART NAME                 -->
<!--                    The name of one of the subject groups used
                        to describe an article, when that article is
                        a multi-part rather than a simple subject.
                        Such groups are used, typically, to provide
                        headings for groups of articles in a printed
                        or online generated Table of Contents. The
                        @content type attribute should be used to
                        indicate the type of part ("text", "code",
                        "sponsor", etc.).
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=compound-subject-part
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=compound-subject-part
                                                                   -->
<!ELEMENT  compound-subject-part
                        (#PCDATA %compound-subject-part-elements;)*  >
<!ATTLIST  compound-subject-part
             %compound-subject-part-atts;                            >


<!-- ============================================================= -->
<!--                    SERIES INFORMATION                         -->
<!-- ============================================================= -->


<!--                    GROUPING ARTICLES IN SERIES
                        Series (as used in the <series-title> and
                        <series-text> elements described below) is
                        used in two different senses. Some issues of
                        journals are part of a series and will have
                        series information just as they have an
                        issue number as part of the article metadata,
                        to describe the issue of the journal in which
                        the article is published.  The second usage
                        is for groupings of articles within one
                        issue of a journal. For example, in some
                        journals, articles are grouped into a
                        series such as "From the Cover" and
                        identified as part of a series.
                        The Series Title element names the series
                        and the Series Text element provides textual
                        description (if any) describing the series.-->


<!--                    SERIES TITLE ELEMENTS                      -->
<!--                    Elements that may be used, along with data
                        characters inside the content model of the
                        <series-title> element                     -->
<!ENTITY % series-title-elements
                        "%rendition-plus;"                           >


<!--                    SERIES TITLE                               -->
<!--                    Title of the journal series (bibliographic
                        meaning) or the title of a  series of
                        articles internal to one issue of a journal
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=series-title
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=series-title
                                                                   -->
<!ELEMENT  series-title (#PCDATA %series-title-elements;)*           >
<!ATTLIST  series-title
             %series-title-atts;                                     >


<!--                    SERIES TEXT ELEMENTS                       -->
<!--                    Elements that may be used, along with data
                        characters inside the content model of the
                        <series-text> element                      -->
<!ENTITY % series-text-elements
                        "%rendition-plus;"                           >


<!--                    SERIES TEXT: HEADER TEXT to DESCRIBE       -->
<!--                    Textual description of the series of articles
                        that are named in a <series-title> element
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=series-text
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=series-text
                                                                   -->
<!ELEMENT  series-text  (#PCDATA %series-text-elements;)*            >
<!ATTLIST  series-text
             %series-text-atts;                                      >


<!-- ============================================================= -->
<!--                    TOP-LEVEL ARTICLE METADATA CONTINUED       -->
<!-- ============================================================= -->


<!--                    AUTHOR NOTES MODEL                         -->
<!--                    Content model for an <author-notes> element.
                                                                   -->
<!ENTITY % author-notes-model
                        "(label?, title?,
                          (%corresp.class; | %fn-link.class; |
                           %just-para.class;)+ )"                    >


<!--                    AUTHOR NOTE GROUP                          -->
<!--                    Footnotes to authors or notes about authors
                        (and, potentially other contributors) are
                        collected in the Author note group.
                        References to these footnotes are made
                        using the <xref> element.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=author-notes
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=author-notes
                                                                   -->
<!ELEMENT  author-notes %author-notes-model;                         >
<!ATTLIST  author-notes
             %author-notes-atts;                                     >


<!-- ============================================================= -->
<!--                 PRODUCT REVIEW INFORMATION (PRODUCT METADATA) -->
<!-- ============================================================= -->


<!--                    PRODUCT ELEMENTS                           -->
<!--                    Elements that may be used inside the
                        <product> element
                        Design Note: All inline mixes begin with an
                        OR bar, but since %simple-text; is an inline
                        mix, the OR bar is already there.          -->
<!ENTITY % product-elements
                        "| %article-link.class; | %break.class; |
                         %emphasis.class; | %inline-display.class; |
                         %inline-math.class; | %phrase.class; |
                         %price.class; | %references.class; |
                         %simple-link.class; | %subsup.class; "      >


<!--                    PRODUCT INFORMATION                        -->
<!--                    Used as a wrapper for metadata for a product
                        (such as a book, software package, hardware
                        component, web site, etc.) that is being
                        reviewed.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=product
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=product
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=product
                                                                   -->
<!ELEMENT  product      (#PCDATA %product-elements;)*                >
<!ATTLIST  product
             %product-atts;                                          >


<!-- ============================================================= -->
<!--                    FURTHER METADATA ELEMENTS                  -->
<!-- ============================================================= -->


<!--                    SELF-URI ELEMENTS                          -->
<!--                    Elements to be mixed with data characters
                        inside the <self-uri> element              -->
<!ENTITY % self-uri-elements
                        ""                                           >


<!--                    URI FOR THIS SAME ARTICLE ONLINE           -->
<!--                    Sometimes an article is available in several
                        forms, for example there is the version that
                        was published in print and there is the same
                        article (possibly expanded or with different
                        graphics) available online.
                           The URI (such as a URL) may be used as a
                        live link, typically naming a web site or the
                        element content may name the URL, e.g., and
                        use the link attributes to hold the real link:
                           <self-uri xlink:href="...">An expanded
                           version of this article is available
                           online</self-uri>
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=self-uri
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=self-uri
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=self-uri
                                                                   -->
<!ELEMENT  self-uri     (#PCDATA %self-uri-elements;)*               >
<!ATTLIST  self-uri
             %self-uri-atts;                                         >


<!-- ============================================================= -->
<!--                    ABSTRACTS                                  -->
<!-- ============================================================= -->


<!--                    ABSTRACT MODEL                             -->
<!--                    Content model for an <abstract> element    -->
<!ENTITY % abstract-model
                        "((%id.class;)*, %sec-opt-title-model;)"     >


<!--                    ABSTRACT                                   -->
<!--                    A short summation of the content of an
                        article.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=abstract
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=abstract
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=abstract
                                                                   -->
<!ELEMENT  abstract     %abstract-model;                             >
<!ATTLIST  abstract
             %abstract-atts;                                         >


<!--                    TRANSLATED ABSTRACT MODEL                  -->
<!--                    Content model for an <trans-abstract> element.
                                                                   -->
<!ENTITY % trans-abstract-model
                        "%sec-opt-title-model;"                      >


<!--                    TRANSLATED ABSTRACT                        -->
<!--                    An abstract that has been translated into
                        another language
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=trans-abstract
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=trans-abstract
                                                                   -->
<!ELEMENT  trans-abstract
                        %trans-abstract-model;                       >
<!ATTLIST  trans-abstract
             %trans-abstract-atts;                                   >


<!-- ============================================================= -->
<!--                    KEYWORD ELEMENTS                           -->
<!-- ============================================================= -->


<!--                    KEYWORD GROUP MODEL                        -->
<!--                    Content model for a <kwd-group> element    -->
<!ENTITY % kwd-group-model
                        "(label?, title?, (%kwd.class;)+ )"          >


<!--                    KEYWORD GROUP                              -->
<!--                    Container element for one set of keywords
                        <kwd>s used to describe a document.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=kwd-group
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=kwd-group
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=kwd-group
                                                                   -->
<!ELEMENT  kwd-group    %kwd-group-model;                            >
<!ATTLIST  kwd-group
             %kwd-group-atts;                                        >


<!--ELEM   title        Defined in %common.ent;                    -->


<!--                    KEYWORD CONTENT ELEMENTS                   -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        a keyword.                                 -->
<!ENTITY % kwd-elements
                        "| %emphasis.class; | %inline-display.class; |
                         %inline-math.class; |
                         %phrase-content.class; |
                         %simple-link.class; | %subsup.class;"       >


<!--                    KEYWORD                                    -->
<!--                    One subject term, critical expression, key
                        phrase, abbreviation, indexing word, etc.
                        that is associated with the whole document
                        and can be used for identification and
                        indexing purposes.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=kwd
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=kwd
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=kwd
                                                                   -->
<!ELEMENT  kwd          (#PCDATA %kwd-elements;)*                    >
<!ATTLIST  kwd
             %kwd-atts;                                              >


<!--                    COMPOUND KEYWORD MODEL                     -->
<!--                    The content model of the <compound-kwd>
                        element                                    -->
<!ENTITY % compound-kwd-model
                        "(compound-kwd-part+)"                       >


<!--                    COMPOUND KEYWORD                           -->
<!--                    Some keywords are simple, a text word or
                        phrase; such keywords can be tagged using the
                        <kwd> element. But other keywords are more
                        complicated:
                         - a code and a term
                         - an abbreviation and its expansion
                         - a hierarchical keyword that carries all
                           the parts of its hierarchy (as index
                           terms do)
                        This element was created to handle these
                        special cases.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=compound-kwd
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=compound-kwd
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=compound-kwd
                                                                   -->
<!ELEMENT  compound-kwd %compound-kwd-model;                         >
<!ATTLIST  compound-kwd
             %compound-kwd-atts;                                     >


<!--                    COMPOUND KEYWORD PART ELEMENTS             -->
<!--                    Elements to be mixed with data characters
                        inside the <compound-kwd-part> element     -->
<!ENTITY % compound-kwd-part-elements
                        "| %emphasis.class; | %inline-display.class; |
                         %inline-math.class; |
                         %phrase-content.class; |
                         %simple-link.class; | %subsup.class;"       >


<!--                    COMPOUND KEYWORD PART                      -->
<!--                    This element was created to hold one
                        component of a complex keyword, for example
                        one for the code and one for the term.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=compound-kwd-part
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=compound-kwd-part
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=compound-kwd-part
                                                                   -->
<!ELEMENT  compound-kwd-part
                        (#PCDATA %compound-kwd-part-elements;)*      >
<!ATTLIST  compound-kwd-part
             %compound-kwd-part-atts;                                >


<!--                    NESTED KEYWORD MODEL                       -->
<!--                    The content model of the <nested-kwd>
                        element                                    -->
<!ENTITY % nested-kwd-model
                        "((%nested-kwd.class;)+, nested-kwd*)"       >


<!--                    NESTED KEYWORD                             -->
<!--                    Hierarchical or nested keyword, in which the
                        levels of a taxonomy that describe the
                        article can be described.                  -->
<!ELEMENT  nested-kwd   %nested-kwd-model;                           >
<!ATTLIST  nested-kwd
            %nested-kwd-atts;                                        >


<!--                    UNSTRUCTURED KEYWORD GROUP ELEMENTS        -->
<!--                    Content model for a <kwd-group> element    -->
<!ENTITY % unstructured-kwd-group-elements
                        "%just-rendition;"                           >


<!--                    UNSTRUCTURED KEYWORD GROUP                 -->
<!--                    Container element for one set of keywords
                        used to describe a document where the
                        individual keywords are not tagged as
                        separate <kwd>s but instead are all run
                        together in one long text field.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=unstructured-kwd-group
                                                                   -->
<!ELEMENT  unstructured-kwd-group
                        (#PCDATA %unstructured-kwd-group-elements;)* >
<!ATTLIST  unstructured-kwd-group
             %unstructured-kwd-group-atts;                           >


<!-- ============================================================= -->
<!--                    STILL FURTHER ARTICLE METADATA             -->
<!-- ============================================================= -->


<!--                    CORRESPONDENCE INFORMATION ELEMENTS        -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the correspondence information.            -->
<!ENTITY % corresp-elements
                        "| %address.class; | %address-link.class;|
                         %emphasis.class; | %label.class; |
                         %phrase-content.class; | %subsup.class;"    >


<!--                    CORRESPONDENCE INFORMATION                 -->
<!--                    Optional element, used as a container for
                        information concerning which of the authors
                        (or other contributors) is the corresponding
                        contributor, to whom information requests
                        should be addressed.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=corresp
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=corresp
                                                                   -->
<!ELEMENT  corresp      (#PCDATA %corresp-elements;)*                >
<!ATTLIST  corresp
             %corresp-atts;                                          >


<!--                    PUBLICATION DATE NOT AVAILABLE MODEL       -->
<!--                    The content model for the EMPTY element 
                        <pub-date-not-available>.                  -->
<!ENTITY % pub-date-not-available-model
                        "EMPTY"                                      >


<!--                    DATE NOT AVAILABLE FLAG                    -->
<!--                    EMPTY element used as a flag to signal that
                        the publication date (<pub-date>) is not
                        available. This flag does not mean, necessarily,
                        that the article is unpublished, just that no
                        <pub-date> is available.
                        Details at:                   
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=pub-date-not-available
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=pub-date-not-available
                                                                   -->
<!ELEMENT  pub-date-not-available
                        %pub-date-not-available-model;               >
<!ATTLIST  pub-date-not-available
             %pub-date-not-available-atts;                           >


<!--                    PUBLICATION DATE MODEL                     -->
<!--                    The content model for the element <pub-date>
                                                                   -->
<!ENTITY % pub-date-model
                        "%date-model;"                               >


<!--                    PUBLICATION DATE                           -->
<!--                    Date of publication or release of the
                        material in one particular format.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=pub-date
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=pub-date
                                                                   -->
<!ELEMENT  pub-date     %pub-date-model;                             >
<!ATTLIST  pub-date
             %pub-date-atts;                                         >


<!-- ============================================================= -->
<!--                    CONFERENCE INFORMATION ELEMENTS            -->
<!-- ============================================================= -->


<!--                    CONFERENCE MODEL                           -->
<!--                    Content model for the <conference> element -->
<!ENTITY % conference-model
                        "(%conference.class;)*"                      >


<!--                    CONFERENCE INFORMATION                     -->
<!--                    The container element for the information
                        about a single conference and its
                        proceedings.
                        Design Note: Conference elements were largely
                        based on Cross-Ref.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=conference
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=conference
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=conference
                                                                   -->
<!ELEMENT  conference   %conference-model;                           >
<!ATTLIST  conference
             %conference-atts;                                       >


<!--ELEM   conf-date     Defined in %common.ent;                   -->
<!--ELEM   conf-loc      Defined in %common.ent;                   -->
<!--ELEM   conf-name     Defined in %common.ent;                   -->
<!--ELEM   conf-sponsor  Defined in %common.ent;                   -->


<!--                    CONFERENCE ACRONYM ELEMENTS                -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the conference acronym.
                        Design Note: All inline mixes begin with an
                        OR bar, but since %simple-text; is an online
                        mix, the OR bar is already there.          -->
<!ENTITY % conf-acronym-elements
                        "%simple-text;"                              >


<!--                    CONFERENCE ACRONYM                         -->
<!--                    The short name, popular name, or "jargon
                        name" for a conference, for example,
                        "Extreme" for "Extreme Markup Languages" or
                        "SIGGRAPH" for "Special Interest Group on
                        Computer Graphics".
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=conf-acronym
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=conf-acronym
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=conf-acronym
                                                                   -->
<!ELEMENT  conf-acronym (#PCDATA %conf-acronym-elements;)*           >
<!ATTLIST  conf-acronym
             %conf-acronym-atts;                                     >


<!--                    CONFERENCE NUMBER ELEMENTS                 -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the conference number.
                        Design Note: All inline mixes begin with an
                        OR bar, but since %simple-text; is an inline
                        mix, the OR bar is already there.          -->
<!ENTITY % conf-num-elements
                        "%simple-text;"                              >


<!--                    CONFERENCE NUMBER                          -->
<!--                    The sequential number of the conference.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=conf-num
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=conf-num
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=conf-num
                                                                   -->
<!ELEMENT  conf-num     (#PCDATA %conf-num-elements;)*               >
<!ATTLIST  conf-num
             %conf-num-atts;                                         >


<!--                    CONFERENCE THEME ELEMENTS                  -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the conference theme.
                        Design Note: All inline mixes begin with an
                        OR bar, but since %simple-text; is an online
                        mix, the OR bar is already there.          -->
<!ENTITY % conf-theme-elements
                        "%simple-text;"                              >


<!--                    CONFERENCE THEME                           -->
<!--                    The theme, slogan, or major subject area of
                        the conference.  For example, the name of an
                        annual conference may be "16th ACH Gathering"
                        but each year has different theme topic,
                        such as "Database Integration" or "Topic
                        Map Subject Access".
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=conf-theme
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=conf-theme
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=conf-theme
                                                                   -->
<!ELEMENT  conf-theme   (#PCDATA %conf-theme-elements;)*             >
<!ATTLIST  conf-theme
             %conf-theme-atts;                                       >


<!--                    STRING CONFERENCE NAME ELEMENTS            -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the extended text name of the conference
                        <string-conf>
                        Design Note: %simple-text; begins with an
                        OR bar.                                    -->
<!ENTITY % string-conf-elements
                        "%simple-text;| %conference.class;"          >


<!--                    STRING CONFERENCE NAME                     -->
<!--                    Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=string-conf
                                                                   -->
<!ELEMENT  string-conf  (#PCDATA %string-conf-elements;)*            >
<!ATTLIST  string-conf
             %string-conf-atts;                                      >


<!-- ============================================================= -->
<!--                    COUNTING INFORMATION (ARTICLE METADATA)    -->
<!-- ============================================================= -->


<!--                    COUNTS MODEL                               -->
<!--                    Content model for the <counts> element.    -->
<!ENTITY % counts-model "(count*, fig-count?, table-count?, 
                          equation-count?, ref-count?, page-count?, 
                          word-count?)"                              >


<!--                    COUNTS                                     -->
<!--                    Wrapper element to hold all metadata that
                        "counts how many of something appear in the
                        article
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=counts
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=counts
                                                                   -->
<!ELEMENT  counts       %counts-model;                               >
<!ATTLIST  counts
             %counts-atts;                                           >


<!--                    COUNT                                      -->
<!--                    Generic <count> element to count anything a 
                        publisher or archive may wish to count in a 
                        document. The @count-type attribute will name 
                        what is being counted (such as figures, 
                        tables, sections, contributors, images,
                        etc.) The @count attribute will state how 
                        many of the objects are in the document. 
     
                        Although a publisher or archive could choose 
                        to label all the counted objects in a document
                        using this element, the specific count 
                        elements (fig-count, equation-count,
                        word-count, etc.) will be retained for 
                        backwards compatibility, and may still be
                        used.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=count
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=count
                                                                   -->
<!ELEMENT  count        EMPTY                                        >
<!ATTLIST  count
             %count-atts;                                            >


<!--                    EQUATION COUNT                             -->
<!--                    Number of display equations <disp-formula>
                        that appear in the article. Inline-equations
                        <inline-formula> are not counted. No
                        distinction is made between numbered and
                        unnumbered equations, both are counted.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=equation-count
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=equation-count
                                                                   -->
<!ELEMENT  equation-count
                        EMPTY                                        >
<!ATTLIST  equation-count
             %equation-count-atts;                                   >


<!--                    FIGURE COUNT                               -->
<!--                    Number of Figures <fig> that appear in the
                        article. Loose <graphic>s that appear
                        outside figures are not counted.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=fig-count
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=fig-count
                                                                   -->
<!ELEMENT  fig-count    EMPTY                                        >
<!ATTLIST  fig-count
             %fig-count-atts;                                        >


<!--                    TABLE COUNT                                -->
<!--                    Number of tables (Table Wrapper <table-wrap>
                        elements that appear in the article. Arrays
                        are not counted as tables.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=table-count
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=table-count
                                                                   -->
<!ELEMENT  table-count  EMPTY                                        >
<!ATTLIST  table-count
             %table-count-atts;                                      >


<!--                    REFERENCE COUNT                            -->
<!--                    Number of reference citations (whether
                        <mixed-citation>s, <element-citation>s, or
                        <nlm-citation>s) that appear in the
                        bibliographic reference list <ref-list>
                        in the article
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=ref-count
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=ref-count
                                                                   -->
<!ELEMENT  ref-count    EMPTY                                        >
<!ATTLIST  ref-count
             %ref-count-atts;                                        >


<!--                    PAGE COUNT                                 -->
<!--                    Number of pages in a print article, counting
                        each page or partial page as one. Electronic
                        articles do not have page counts.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=page-count
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=page-count
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=page-count
                                                                   -->
<!ELEMENT  page-count   EMPTY                                        >
<!ATTLIST  page-count
             %page-count-atts;                                       >


<!--                    WORD COUNT                                 -->
<!--                    Approximate number of words that appear in
                        the textual portion of an
                        article (not including the words in the
                        metadata or header information)
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=word-count
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=word-count
                                                                   -->
<!ELEMENT  word-count   EMPTY                                        >
<!ATTLIST  word-count
             %word-count-atts;                                       >

             
             
<!-- ============================================================= -->
<!--                    TITLE GROUP ELEMENTS (BIBLIOGRAPHIC)       -->
<!-- ============================================================= -->


<!--                    TITLE GROUP MODEL                          -->
<!--                    Content model for the <title-group> element-->
<!ENTITY % title-group-model
                        "(article-title, subtitle*,
                          trans-title-group*, alt-title*, fn-group?)">


<!--                    TITLE GROUP                                -->
<!--                    Wrapper element to hold the various article
                        titles.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=title-group
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=title-group
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=title-group
                                                                   -->
<!ELEMENT  title-group  %title-group-model;                          >
<!ATTLIST  title-group
             %title-group-atts;                                      >


<!--ELEM   article-title
                        Defined in %common.ent;                    -->
<!--ELEM   trans-title  Defined in %common.ent;                    -->


<!--                    SUBTITLE ELEMENTS                          -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        a paragraph <subtitle>.
                        An earlier version of this tag set used the
                        parameter entity %title-elements; for many
                        title elements including <subtitle>.       -->
<!ENTITY % subtitle-elements
                        "%title-elements;"                           >


<!--                    ARTICLE SUBTITLE                           -->
<!--                    Secondary title of a journal article
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=subtitle
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=subtitle
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=subtitle
                                                                   -->
<!ELEMENT  subtitle     (#PCDATA %subtitle-elements;)*               >
<!ATTLIST  subtitle
             %subtitle-atts;                                         >


<!--                    ALTERNATE TITLE                            -->
<!--                    A "different" version of an article title,
                        usually created so that it can be processed
                        in a special way, for example a short
                        version of the title for use in a Table of
                        Contents, an ASCII title, a right-running-
                        head title, etc.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=alt-title
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=alt-title
                                                                   -->
<!ELEMENT  alt-title    (#PCDATA %alt-title-elements;)*              >
<!ATTLIST  alt-title
             %alt-title-atts;                                        >


<!-- ============================================================= -->
<!--                    AUTHOR COMMENTS                            -->
<!-- ============================================================= -->


<!--                    AUTHOR COMMENT MODEL                       -->
<!--                    Content model for the <author-comment>
                        element                                    -->
<!ENTITY % author-comment-model
                        "(title?, (%just-para.class;)+ )"            >


<!--                    AUTHOR COMMENT                             -->
<!--                    Used for extra textual material associated
                        with a contributor such as an author or
                        editor
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=author-comment
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=author-comment
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=author-comment
                                                                   -->
<!ELEMENT  author-comment
                        %author-comment-model;                       >
<!ATTLIST  author-comment
             %author-comment-atts;                                   >


<!-- ============================================================= -->
<!--                    PUBLICATION HISTORY DATE ELEMENTS          -->
<!-- ============================================================= -->


<!--                    HISTORY MODEL                              -->
<!--                    The content model for the <history> element
                                                                   -->
<!ENTITY % history-model
                        "(%date.class;)+"                            >


<!--                    HISTORY:  DOCUMENT HISTORY                 -->
<!--                    Used as a container for dates related to the
                        processing history of the document, such as
                        received date and accepted date.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=history
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=history
                                                                   -->
<!ELEMENT  history      %history-model;                              >
<!ATTLIST  history
             %history-atts;                                          >


<!-- ============================================================= -->
<!--                    PUBLICATION HISTORY EVENT ELEMENTS         -->
<!--                    More than a date (from BITS)               -->
<!-- ============================================================= -->


<!--                    EVENT DESCRIPTION ELEMENTS                 -->
<!--                    Elements that may be used, along with data
                        characters inside the content model of the
                        <event-desc> element.                      -->
<!ENTITY % event-desc-elements
                        "| %address-link.class; |
                         %article-identifier.class; |
                         %article-version.class; |  
                         %date.class; | %pub-date.class; |
                         pub-date-not-available"                     >


<!--                    EVENT DESCRIPTION                          -->
<!--                    This is a description of an event in the
                        publishing history of a book, for example,
                        reprinting or publication of the revised online
                        edition. This text field may be as
                        simple a few words or as complex as a
                        narrative with embedded markup.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=event-desc
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=event-desc
                                                                    -->
<!ELEMENT  event-desc   (#PCDATA %event-desc-elements;)*             >
<!ATTLIST  event-desc
             %event-desc-atts;                                       >


<!--                    EVENT MODEL                                -->
<!--                    The content model for the <event> element. -->
<!ENTITY % event-model  "(event-desc?, article-id*, 
                          (%article-version.class;)?,
                          ( (%pub-date.class;)* | 
                             pub-date-not-available?),
                          (%date.class;)*,
                          issn*, issn-l?, isbn*, permissions?,
                          notes*, self-uri*)"                        >


<!--                    EVENT IN PUBLISHING HISTORY                -->
<!--                    This is a description of an event in the
                        publishing history of a book, for example,
                        reprinting or publication of the revised online
                        edition. This text field may be as
                        simple a few words or as complex as a
                        narrative with embedded markup.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=event
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=event
                                                                   -->
<!ELEMENT  event        %event-model;                                >
<!ATTLIST  event
             %event-atts;                                            >


<!--                    PUBLICATION HISTORY MODEL                  -->
<!--                    The content model for the <pub-history>
                        element.                                   -->
<!ENTITY % pub-history-model
                        "((%event.class;)+ )"                        >


<!--                    PUBLICATION HISTORY                        -->
<!--                    Used as a container for events related to the
                        processing history of the document, such as
                        online publication date or reprint date.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=pub-history 
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=pub-history 
                                                                   -->
<!ELEMENT  pub-history  %pub-history-model;                          >
<!ATTLIST  pub-history
             %pub-history-atts;                                      >

<!-- ============================================================= -->
<!--                    ISSUE TITLE GROUP                          -->
<!-- ============================================================= -->


<!--                    ISSUE TITLE GROUP                          -->
<!--                    Complete content model for the
                        <issue-title-group> element                -->
<!ENTITY % issue-title-group-model
                        "(issue-title, issue-subtitle*,
                          trans-title-group*)"                       >


<!--                    ISSUE TITLE GROUP                          -->
<!--                    Grouping element to associate issue title
                        metadata such as issue title and subtitle
                        for the cases where there is more than one
                        issue. Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=issue-title-group
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=issue-title-group
                                                                   -->
<!ELEMENT  issue-title-group
                        %issue-title-group-model;                    >
<!ATTLIST  issue-title-group
             %issue-title-group-atts;                                >

<!-- ============================================================= -->
<!--                    PROCESSING METADATA ELEMENTS               -->
<!-- ============================================================= -->


<!--                    PROCESSING METADATA GROUP                  -->
<!--                    Complete content model for the
                        <processing-meta> element                  -->
<!ENTITY % processing-meta-model
                        "(restricted-by*, extended-by*, 
                          custom-meta-group*)"                       >


<!--                    PROCESSING METADATA MODEL                  -->
<!--                    Optional container element, used to hold the 
                        processing metadata elements, which describe
                        processing information descriptive of the
                        XML-tagged document (document instance).
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=processing-meta
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=processing-meta                                                                                       -->
<!ELEMENT  processing-meta
                        %processing-meta-model;                      >
<!ATTLIST  processing-meta
             %processing-meta-atts;                                  >


<!--                    EXTENDED-BY ELEMENTS                       -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        <extended-by>.
                        Design Note: This inline mix must begin with
                        an OR bar.                                 -->
<!ENTITY % extended-by-elements
                        ""                                           >

<!--                    EXTENDED-BY MODEL                          -->
<!--                    Identification of a JATS extension or superset
                        the document claims to be following. The 
                        element is repeatable so that more than one
                        extension can be claimed, odd as that seems.
                        The content may be a name, such as “taxpub”, 
                        or a URI.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=extended-by
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem= extended-by                                                                                          -->
<!ELEMENT  extended-by  (#PCDATA %extended-by-elements;)*            >
<!ATTLIST  extended-by
             %extended-by-atts;                                      >


<!--                    RESTRICTED-BY ELEMENTS                     -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        <restricted-by>.
                        Design Note: This inline mix must begin with
                        an OR bar.                                 -->
<!ENTITY % restricted-by-elements
                        ""                                           >

<!--                    RESTRICTED-BY MODEL                        -->
<!--                    Identification of one of the guidelines or 
                        other restrictions (such as a tighter subset
                        schema) the document claims to be following. 
                        The element is repeatable so that more than 
                        one set of restrictions can be claimed. The
                        content may be a name such as “jats4r” or 
                        “pmc” or a URI, for example the URL of a
                        particular JATS4R recommendation.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=restricted-by
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=restricted-by
                                                                   -->
<!ELEMENT  restricted-by
                        (#PCDATA %restricted-by-elements;)*          >
<!ATTLIST  restricted-by
             %restricted-by-atts;                                    >


<!-- ================== End Article Metadata Elements  =========== -->

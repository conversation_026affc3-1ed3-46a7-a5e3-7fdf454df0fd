<!-- ============================================================= -->
<!--  MODULE:    Math Element Classes                              -->
<!--  VERSION:   2.0                                               -->
<!--  DATE:      August 2004                                       -->
<!--                                                               -->
<!-- ============================================================= -->

<!-- ============================================================= -->
<!--                    PUBLIC DOCUMENT TYPE DEFINITION            -->
<!--                        TYPICAL INVOCATION                     -->
<!--
"-//NLM//DTD Archiving and Interchange DTD Suite Math Class Elements v2.0 20040830//EN"
     Delivered as file "math.ent"                                  -->
<!-- ============================================================= -->

<!-- ============================================================= -->
<!-- SYSTEM:     Archiving and Interchange DTD Suite               -->
<!--                                                               -->
<!-- PURPOSE:    Names all elements in the math classes            -->
<!--                                                               -->
<!-- CONTAINS:   1) Default definition of the math classes         -->
<!--             2) Inline formula <inline-formula>                -->
<!--             3) Display Formula <disp-formula>                 -->
<!--             4) TeX Math Equation <tex-math>                   -->
<!--                                                               -->
<!-- CREATED FOR:                                                  -->
<!--             Digital archives and publishers who wish to       -->
<!--             create a custom XML DTD for original markup of    -->
<!--             journal literature, books, and related material,  -->
<!--             or for archiving and transferring such material   -->
<!--             between archives.                                 -->
<!--                                                               -->
<!--             This DTD is in the public domain. An organization -->
<!--             that wishes to create its own DTD from the suite  -->
<!--             may do so without permission from NLM.            -->
<!--                                                               -->
<!--             The suite has been set up to be extended using a  -->
<!--             new DTD file and a new DTD-specific customization -->
<!--             module to redefine the many Parameter Entities.   -->
<!--             Do not modify the suite directly or redistribute  -->
<!--             modified versions of the suite.                   -->
<!--                                                               -->
<!--             In the interest of maintaining consistency and    -->
<!--             clarity for potential users, NLM requests:        -->
<!--                                                               -->
<!--             1. If you create a DTD from the Archiving and     -->
<!--                Interchange DTD Suite and intend to stay       -->
<!--                compatible with the suite, then please include -->
<!--                the following statement as a comment in all of -->
<!--                your DTD modules:                              -->
<!--                   "Created from, and fully compatible with,   -->
<!--                    the Archiving and Interchange DTD Suite."  -->
<!--                                                               -->
<!--             2. If you alter one or more modules of the suite, -->
<!--                then please rename your version and all its    -->
<!--                modules to avoid any confusion with the        -->
<!--                original suite. Also, please include the       -->
<!--                following statement as a comment in all your   -->
<!--                DTD modules:                                   -->
<!--                   "Based in part on, but not fully compatible -->
<!--                    with, the Archiving and Interchange DTD    --> 
<!--                    Suite."                                    -->
<!--                                                               -->
<!--             Suggestions for refinements and enhancements to   -->
<!--             the DTD suite should be sent in email to:         -->
<!--                 <EMAIL>                  -->
<!--                                                               -->
<!-- ORIGINAL CREATION DATE:                                       -->
<!--             December 2002                                     -->
<!--                                                               -->
<!-- CREATED BY: Jeff Beck       (NCBI)                            -->
<!--             Deborah Lapeyre (Mulberry Technologies, Inc.)     -->
<!--             Bruce Rosenblum (Inera Inc.)                      -->
<!--                                                               -->
<!--             NLM thanks the Harvard University Libraries, both -->
<!--             for proposing that a draft archiving NLM DTD for  --> 
<!--             life sciences journals be extended to accommodate -->
<!--             journals in all disciplines and for sponsoring    -->
<!--             Bruce Rosenblum's collaboration with other DTD    -->
<!--             authors in completing Version 1.0. The Andrew W.  --> 
<!--             Mellon Foundation provided support for these      --> 
<!--             important contributions.                          -->
<!--                                                               -->
<!-- ============================================================= -->


<!-- ============================================================= -->
<!--                    DTD VERSION/CHANGE HISTORY                 -->
<!-- ============================================================= -->
<!--
     =============================================================

Version  Reason/Occasion                   (who) vx.x (yyyy-mm-dd)

     =============================================================
     Version 2.0                       (DAL/BTU) v2.0 (2004-08-30)
     
     
     Major requirement changes led to the new release, producing
     DTD version "2.0":
       a) The splitting of the Archival and Interchange Tag Set 
          DTDs into three DTDs from two: an authoring DTD, an
          archive regularization and interchange DTD (the
          current Blue Publishing DTD), and a preservationist 
          archive DTD (the current Green Archiving and Interchange 
          DTD).
       b) AIT Working Group suggestions from the June 04 meeting 
          and June/July 2004 followup discussions
       c) Suite remodularization to meet new (and newly articulated)
          modularization requirements
       d) New or renamed classes and mixes to make modifications
          easier and more consistent

  7. INLINE FORMULA/DISPLAY FORMULA
     ### Customization Alert ###
     a. Inline Formula
        - Made the content model into a separate parameter entity
          %inline-formula-model;, which uses the Parameter
          Entity %inline-formula-elements;
        - Changed the Parameter Entities and models to use the 
          OR-bar-first mechanism. In other words: Changed 
          "%all-phrase;" to "| %all-phrase;" and removed the
          OR bar following #PCDATA in the content model to match.
        - Added %inline-math.class; to the model
     b. Display Formula - Similar changes made:
        - added new content model PE %disp-formula-model; 
        - changed %disp-formula-elements; and %disp-formula-model;
          to use the OR-bar-first
        - Added %inline-math.class;

  5. DEFAULT CLASSES - Were moved from this module to 
     %default-classes.ent;
  
  4. %tex-math-atts; - Added the value "LaTeX" to the "notation"
     attribute
  
  3. Updated public identifier to "v2.0 20040830"         

     =============================================================
     Version 1.1                           (TRG) v1.1 (2003-11-01)

  2. Added attribute "alternate-form-of" to:
       -  <disp-formula> (by modifying parameter entity 
          %disp-formula-atts;)
       -  <inline-formula>
       -  <tex-math> (by modifying parameter entity 
          %tex-math-atts;)
     Rationale: Where multiple formats of an item (e.g., graphic 
     file, media object, chemical structure) are available, this 
     attribute indicates that a format is a secondary one and 
     provides a link to the primary format, so that only one 
     format of an item is displayed.
     
  1. Added attribute "id" to element <inline-formula> 
     Rationale: To allow an <inline-formula> to be linked to, 
     especially by alternate forms of the same formula (which will
     use the "alternate-form-of" attribute to link to the primary)
                                                                   -->


<!-- ============================================================= -->
<!--                    PARAMETER ENTITY DEPENDENCIES             
                        Requires the following parameter entities
                        be defined before calling this module, 
                        usually accomplished in the Customization
                        Module for the specific DTD:
                          %access.class;
                          %break.class;
                          %emphasis.class;
                          %inline-display.class;
                          %inline-formula-elements;
                          %label.class;
                          %math.class;
                          %simple-display.class;
                          %subsup.class;
                                                                   -->
<!-- ============================================================= -->


<!-- ============================================================= -->
<!--                    PARAMETER ENTITIES FOR ATTRIBUTE LISTS     -->
<!-- ============================================================= -->
                                                                
                                                                 
<!--                    DISPLAY FORMULA ATTRIBUTES                 -->
<!--                    Attributes for the <disp-formula> element  -->
<!--         alternate-form-of
                        Where multiple formats of an item (e.g., 
                        graphic file, media object, chemical 
                        structure) are available, this attribute 
                        indicates that a format is a secondary 
                        one and provides a link to the primary 
                        format, so that only one format of an
                        item is displayed.
             id         Unique identifier so the element may be
                        referenced                                 -->
<!ENTITY % disp-formula-atts
            "alternate-form-of
                        IDREF                              #IMPLIED
             id         ID                                 #IMPLIED" >
                                                                
                                                                 
<!--                    TEX MATH ATTRIBUTES                        -->
<!--                    Attributes for the <disp-formula> element  -->
<!--         alternate-form-of
                        Where multiple formats of an item (e.g., 
                        graphic file, media object, chemical 
                        structure) are available, this attribute 
                        indicates that a format is a secondary 
                        one and provides a link to the primary 
                        format, so that only one format of an
                        item is displayed.
             id         Unique identifier so the element may be
                        referenced
             version    Which version of TeX used
             notation   Says that the content of this element will
                        be in TeX and needs to be surrounded by
                        a CDATA section.                           -->
<!ENTITY % tex-math-atts                                  
            "alternate-form-of
                        IDREF                             #IMPLIED
             id         CDATA                             #IMPLIED
             notation   NOTATION (LaTeX | tex | TEX | TeX)
                                                          #IMPLIED
             version    CDATA                             #IMPLIED"  >


<!-- ============================================================= -->
<!--                    MATH ELEMENTS (PARAGRAPH LEVEL)            -->
<!-- ============================================================= -->


<!--                    FORMULA, INLINE ELEMENTS                   -->
<!--                    Elements for use in the <inline-formula> 
                        element                                    -->
<!ENTITY % inline-formula-elements   
                        "| %emphasis.class; | %inline-display.class; |
                         %inline-math.class; |
                         %math.class; | %subsup.class;"              > 


<!--                    FORMULA, INLINE MODEL                      -->
<!--                    Content model for an <inline-formula>      -->
<!ENTITY % inline-formula-model   
                        "(#PCDATA %inline-formula-elements;)*"       > 


<!--                    FORMULA, INLINE                            -->
<!--                    Inline element for a mathematical
                        equation, expression, or formula           -->
<!ELEMENT  inline-formula            
                        %inline-formula-model;                       >
<!--         alternate-form-of
                        Where multiple formats of an item (e.g., 
                        graphic file, media object, chemical 
                        structure) are available, this attribute 
                        indicates that a format is a secondary 
                        one and provides a link to the primary 
                        format, so that only one format of an
                        item is displayed.
             id         Unique identifier so an inline-formula
                        can be referenced                          -->
<!ATTLIST  inline-formula
             alternate-form-of
                        IDREF                              #IMPLIED
             id         ID                                 #IMPLIED  >


<!--                    DISPLAY FORMULA ELEMENTS                   -->
<!--                    Elements for use in the <disp-formula> 
                        element                                    -->
<!ENTITY % disp-formula-elements  
                        "| %access.class; | %address-link.class; |
                         %break.class; | %emphasis.class; | 
                         %inline-display.class; |
                         %inline-math.class; | 
                         %label.class; | %math.class; | 
                         %simple-display.class; | %subsup.class;"    > 


<!--                    FORMULA, DISPLAY MODEL                     -->
<!--                    Content model for an <disp-formula>        -->
<!ENTITY % disp-formula-model   
                        "(#PCDATA %disp-formula-elements;)*"         > 


<!--                    FORMULA, DISPLAY                           -->
<!--                    Block-level (callout) element for a 
                        mathematical equation, expression, or 
                        formula.  The characters representing the
                        equation may be present, or the equation
                        could be a graphic.                        -->
<!ELEMENT  disp-formula %disp-formula-model;                         >
<!--         alternate-form-of
                        Where multiple formats of an item (e.g., 
                        graphic file, media object, chemical 
                        structure) are available, this attribute 
                        indicates that a format is a secondary 
                        one and provides a link to the primary 
                        format, so that only one format of an
                        item is displayed.
             id         Unique identifier so the element may be
                        referenced                                 -->
<!ATTLIST  disp-formula
             %disp-formula-atts;                                     >


<!--                    TEX MATH EQUATION                          -->
<!--                    Used to hold encoded math, expressed in TeX-->
<!ELEMENT  tex-math      (#PCDATA)                                   >
<!--         alternate-form-of
                        Where multiple formats of an item (e.g., 
                        graphic file, media object, chemical 
                        structure) are available, this attribute 
                        indicates that a format is a secondary 
                        one and provides a link to the primary 
                        format, so that only one format of an
                        item is displayed.
             id         Unique identifier so the element may be
                        referenced
             version    Which version of TeX or LaTeX used
             notation   Says that the content of this element will
                        be in TeX and needs to be surrounded by
                        a CDATA section.                           -->
<!ATTLIST  tex-math                                  
             %tex-math-atts;                                         >


<!-- ================== End List Class Module ==================== -->

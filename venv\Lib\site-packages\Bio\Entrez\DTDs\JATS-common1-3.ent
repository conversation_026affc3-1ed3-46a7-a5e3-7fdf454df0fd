<!-- ============================================================= -->
<!--  MODULE:    Common (Shared) Elements Module                   -->
<!--  VERSION:   ANSI/NISO JATS Version 1.3 (Z39.96-2021)          -->
<!--  DATE:      June 2021                                         -->
<!--                                                               -->
<!-- ============================================================= -->

<!-- ============================================================= -->
<!--                    PUBLIC DOCUMENT TYPE DEFINITION            -->
<!--                        TYPICAL INVOCATION                     -->
<!--
"-//NLM//DTD JATS (Z39.96) JATS DTD Suite Common (Shared) Elements Module v1.3 20210610//EN"
Delivered as file "JATS-common1-3.ent"                             -->
<!-- ============================================================= -->


<!-- ============================================================= -->
<!-- SYSTEM:     JATS DTD Suite                                    -->
<!--                                                               -->
<!-- PURPOSE:    Defines the common parameter entities, calls the  -->
<!--             shared modules (such as special characters and    -->
<!--             notations) and provides declarations for elements -->
<!--             that do not properly fit into one class, since    -->
<!--             they can be used at more than one structural level-->
<!--                                                               -->
<!-- MODULES REQUIRED:                                             -->
<!--             1) Standard XML Special Characters Module         -->
<!--                                       (%xmlspecchars.ent;)    -->
<!--             2) Custom XML Special Characters (%chars.ent;)    -->
<!--             3) Notation Declarations    (%notat.ent;)         -->
<!--                                                               -->
<!-- TAG SET SPONSOR                                               -->
<!--             National Center for Biotechnology                 -->
<!--                Information (NCBI)                             -->
<!--             National Library of Medicine (NLM)                -->
<!--                                                               -->
<!-- CREATED FOR:                                                  -->
<!--             This module was created for the JATS DTD Suite.   -->
<!--             Digital archives and publishers may use the       -->
<!--             DTD as is for markup of journal literature or     -->
<!--             related material for archiving and transferring   -->
<!--             such material between archives or create a        -->
<!--             custom XML DTD from the Suite for                 -->
<!--             these purposes.                                   -->
<!--                                                               -->
<!--             This DTD is in the public domain. An organization -->
<!--             that wishes to create its own DTD from the suite  -->
<!--             may do so without permission from NLM.            -->
<!--                                                               -->
<!--             The suite has been set up to be extended using a  -->
<!--             new DTD file and a new DTD-specific customization -->
<!--             module to redefine the many Parameter Entities.   -->
<!--             Do not modify the suite directly or redistribute  -->
<!--             modified versions of the suite.                   -->
<!--                                                               -->
<!--             In the interest of maintaining consistency and    -->
<!--             clarity for potential users, NLM requests:        -->
<!--                                                               -->
<!--             1. If you create a DTD from the JATS DTD Suite    -->
<!--                and intend to stay compatible with the suite,  -->
<!--                then please include the following statement    -->
<!--                as a comment in all of your DTD modules:       -->
<!--                   "Created from, and fully compatible with,   -->
<!--                    the ANSI/NISO Z39.96 Journal Article Tag   -->
<!--                    Suite (JATS)."                             -->
<!--                                                               -->
<!--             2. If you alter one or more modules of the suite, -->
<!--                then please rename your version and all its    -->
<!--                modules to avoid any confusion with the        -->
<!--                original suite. Also, please include the       -->
<!--                following statement as a comment in all your   -->
<!--                DTD modules:                                   -->
<!--                   "Based in part on, but not fully compatible -->
<!--                    with, the ANSI/NISO Z39.96 Journal Article -->
<!--                    Tag Suite (JATS)."                         -->
<!--                                                               -->
<!-- ORIGINAL CREATION DATE:                                       -->
<!--             December 2002                                     -->
<!--                                                               -->
<!-- CREATED BY: Mulberry Technologies, Inc. for the NISO Z39.96   -->
<!--             Working Group. Mulberry Technologies was          -->
<!--             supported by National Center for Biotechnology    -->
<!--             Information (NCBI), a center of the US National   -->
<!--             Library of Medicine (NLM).                        -->
<!--                                                               -->
<!--             This module is part of the JATS DTD Suite. The    -->
<!--             Suite is a continuation of work done by NCBI,     -->
<!--             Mulberry Technologies, and Inera, Inc. on the NLM -->
<!--             Journal Archiving and Interchange DTD Suite, which-->
<!--             was originally released in December, 2002.        -->
<!--                                                               -->
<!--             NLM thanks the Harvard University Libraries, both -->
<!--             for proposing that a draft archiving NLM DTD for  -->
<!--             life sciences journals be extended to accommodate -->
<!--             journals in all disciplines and for sponsoring    -->
<!--             Inera Inc.'s collaboration with other DTD         -->
<!--             authors in completing NLM Version 1.0. The        -->
<!--             Andrew W. Mellon Foundation provided support for  -->
<!--             these important contributions.                    -->
<!--                                                               -->
<!--             Suggestions for refinements and enhancements to   -->
<!--             this DTD should be sent in email to:              -->
<!--                 <EMAIL>                         -->
<!-- ============================================================= -->


<!-- ============================================================= -->
<!--                    DTD VERSION/CHANGE HISTORY                 -->
<!-- ============================================================= -->
<!--
Version  Reason/Occasion                   (who) vx.x (yyyy-mm-dd)
   ==============================================================
     JATS Version 1.3 (ANSI/NISO Z39.96-2021)
                                  (DAL/BTU) v1.3 (2021-06-10)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by both the NISO and ANSI memberships 
     to be changed. This version of the NISO JATS was approved 
     by ANSI vote on June 10, 2021, so it supersedes all Committee 
     Drafts as well as the draft version JATS 1.3 2021 (the version
     submitted for vote), becoming NISO JATS v1.3 20210610 
     (ANSI/NISO Z39.96-2019).

     The file names and formal public identifiers have been 
     changed in the modules and the catalogs.

 76. JATS 'v1.3d2 20201130' becomes 'v1.3 20210610' 
     (final ANSI vote date)
     'ANSI/NISO Z39.96-2019' becomes 'ANSI/NISO Z39.96-2021'

  ==============================================================
     JATS Version 1.3d2 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.3d2 (2020-11-30)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by the NISO and ANSI memberships 
     to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     of the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting; it is for Standing Committee review and
     testing. The formal public identifiers have been changed 
     in the modules and the catalogs.

75. Custom Metadata Group - Added the attribute @content-type, 
    @specific-use, and @xml:lang. Inside <processing-meta>,
    <custom-meta-group> will be allowed to repeat.
    Not allowed to repeat anywhere else for 1.3d2. Maybe later
       
74. OBJECT ID Added <object-id> to: 
        <alternatives>, <citation-alternatives> 

73. OBJECT ID <object-id> element added to <sec-meta>, which
      therefore added <object-id> to:
      - In Green: abstract, ack, app, bio, boxed-text, notes, 
                  question, sec, and trans-abstract  
      - In Blue:  app, bio, boxed-text, notes, question, sec
                  (added the others directly)
      In some elements (for example, <abstract> in Green)
      the model will be:
         abstract  (object-id*, (sec-meta?, label?, title?, 
                    (%para-level;)*, ...)  >
      This results in <object-id> being allowed in two places:
      both at the beginning of <abstract> and inside <sec-meta>
      within <abstract>. This is a known problem caused by
      backward compatibility requirements, and it will be
      what it will be.
 
 72. EXPANSION OF FIXED ATTRIBUTES LIST: There are four  
     content-defined attributes which are CDATA values 
     (undetermined) in Archiving, but which are named value 
     lists in Publishing/Authoring:
       - @fn-type 
       - @person-group-type
       - @pub-id-type
       - @ref-type 
     In order to give users of Publishing and Authoing
     more flexibility to name new values without 
     destroying the benefit of named value lists:
       - A new value “custom” has been added to the named
           value list of each of these four attributes.
       - A new attribute @custom-type has been added to
           each element that takes one of these attributes.
     In this module:
      - The element <fn> was given the new attribute 
        @custom-type and "custom" was added to
        %fn-types (the footnote type named value list).
      - The element <person-group> was given the new 
        attribute @custom-type and "custom" was added to
        %person-group-types (the person group type named 
        value list). As an example: 
           <person-group @person-group-type=”custom” 
            custom-type=”statisticians”>
     The Best Practice rule (unenforceable in DTDs, but  
     enforceable in Schematron) is, that if you use the 
     value “custom” from one of these lists, you should 
     also record what type of 'custom' in the 
     @custom-type attribute.                        

 71. CUSTOM META - Added all 4 vocabulary attributes to
     <custom-meta>.

 70. STRING DATE - Added to models through
     %references.class; Added to:
        <related-article>
     
 69. ISSUE SUBTITLE - Added <issue-subtitle> to 
     <issue-title-group> optional and repeatable
     
 68. ISSUE TITLE GROUP - Added <issue-title-group> to 
     <volume-issue-group> optional and repeatable

 67. ASSIGNING AUTHORITY - @assigning-authority attribute
     added to: 
       <custom-meta>, <isbn>, <issn>, <issn-l>, <role>, <uri>
 
 66. JATS "v1.3d2 20201130" becomes 'v1.3 20210610' (second 
     committee draft following the final ANSI/NISO 1.2 vote)

   ==============================================================
     JATS Version 1.3d1 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.3.d1 (2019-08-31)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by the NISO and ANSI memberships 
     to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     of the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
     
 65. BLOCK ALTERNATIVES - Add a new alternative-type element
     that provides for alternative block objects such as
     figures, tables, and boxed-text
     
 64. ADDED SUBJECTS TO WHEREVER KEYWORDS ALLOWED - Added
     <subj-group> to the following elements through <sec-meta>
      - abstract and trans-abstract
      - ack, app, bio, boxed-text, notes, question, sec

 63. LANGUAGE OF THE TARGET ARTICLE - Added a new optional
     attribute @hreflang to name the language of the
     target to which the <related-article> points.

 62. JATS "1.2" becomes "v1.3d1 20190831" (first committee
     draft following the final ANSI/NISO vote)
     
    ==============================================================
     JATS Version 1.2 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.2     (2019-02-08)
    
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the both the NISO and ANSI membership 
     to be changed. This version of the NISO JATS was approved 
     by ANSI vote on Feb 08, 2019, so it supersedes all Committee 
     Drafts as well as the draft version JATS 1.2 2018 (the version
     submitted for vote), becoming NISO JATS v1.2 20190208 
     (ANSI/NISO Z39.96-2021).
  
 61. JATS "1.2" becomes "v1.2 20190208" (final ANSI vote date)
     "ANSI/NISO Z39.96-2015" becomes "ANSI/NISO Z39.96-2019"
     
   ==============================================================
     JATS Version 1.2 (ANSI/NISO Z39.96-2018)
                                   (DAL/BTU) v1.2     (2018-11-30)
    
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the both the NISO and ANSI membership 
     to be changed. This version of the NISO JATS was approved 
     by ANSI and NISO vote, so it supersedes all Committee Drafts
     and becoming NISO JATS 1.2 (ANSI/NISO Z39.96-2018).
  
 60. JATS "1.2d2" and "v1.2d2 20180401" became
     JATS "1.2" and "v1.2 20181130" 

    =============================================================
     JATS Version 1.2d2              (DAL/BTU) v1.2d2  (2018-04-01)

     JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
     BITS is under continuous maintenance, and is modified at the
     discretion of the working group.
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
  
 59. PUB-ID-TYPE - @pub-id-type (for all tag sets EXCEPT 
     Archiving) changed back to a fixed list from CDATA for some
     elements, reversing the decision of Version 1.2d1. Although 
     the value list is unchanged, best practice will be documented
     as using @pub-id-type for type and @assigning-authority
     for the responsible agency.
        The following were changed back. Therefore, Archiving must 
     override the following attribute lists to make @pub-id-type 
     CDATA:
         - <article-id> (now value list)
         - <pub-id>     (now value list)
     But no change to the following, which were CDATA in version 1.1
         - <issue-id>
         - <object-id>
         - <volume-id>

 58. ASSIGNING AUTHORITY - Added @assigning-authority to the
     following elements, so that the type-of-identifier 
     could be recorded with @pub-id-type and the party-
     responsible-for-the-identifier could be recorded
     using @assigning-authority. Previously, these value 
     types were conflated in @pub-id-type. However, for
     backward compatibility, @pub-id-type was left as
     the SAME restricted list, with both types intermingled. 
         - <contrib-id>
         - <institution-id>
         - <issue-id>
         - <journal-id>
         - <object-id>
         - <volume-id>
         - Note: <ext-link> already took @assigning-authority

 57. DATA USAGE - Added new CDATA attribute @use-type to both
     <mixed-citation> and <element-citation>. First use case is to
     explain cited data usage ("analyzed", "generated", etc.)

 56. DEGREE OF CONTRIBUTION - Added new CDATA attribute 
     @degree-contribution to <role> to hold the degree of 
     contribution. This is an open list, but values are 
     expected to be "Lead", "Equal", or "Supporting".
     Numeric values will be discouraged.

 55. BITS "2.0" and "v2.0 20151225" remain unchanged
   
     JATS "1.2d1" and "v1.2d1 20171231" became
     JATS "1.2d2" and "v1.2d2 20180401". 

     No module names were changed.
 
    =============================================================
     JATS Version 1.2d1 (ANSI/NISO Z39.96-2015)
                                   (DAL/BTU) v1.2d1  (2017-12-31)
 
     NISO JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
  
 54. JATS "1.2d1" and "v1.2d1 20170631" became
     JATS "1.2d1" and "v1.2d1 20171231". 

    =============================================================
     JATS Version 1.2d1              (DAL/BTU) v1.2d1  (2017-06-31)

     NISO JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
   
 53. VOCABULARY/TAXONOMY ATTRIBUTES - Added 4 new attributes for
     naming (and possibly linking to) a general or controlled
     taxonomy, vocabulary, index, database or other source of
     terms: @vocab, @vocab-identifier, @vocab-term, and
     @vocab-term-identifier
      - Added all 4 attributes to:
        - to <role>, for adding CRediT or other specifically named
          roles to contributors
        - Added @vocab and @vocab-identifier to <institution-id>
 
 52. JATS became version "1.2d1" and "v1.2d1 20170631"
 
    =============================================================
     JATS Version 1.1                (DAL/BTU) v1.1  (2015-12-15)

     JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
     JATS 1.1 was approved in late 2015, so the formal public 
     identifiers were changed in the modules and the catalogs.
     No model or attribute changes were made at this time. 
 
 51. JATS became version "1.1" and "v1.1 20151215"
     BITS remained version "2.0" but becomes "v2.0 20151225"

     =============================================================
     JATS Version 1.1d3             (DAL/BTU) v1.1d3  (2015-03-01)
   
     The changes in this release are in response to NISO 
     License and Indicators Recommended Practice.

     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the NISO membership to be changed. This
     Committee Draft 1.1d3 will be sent to the NISO voting
     membership, to become (if approved) NISO JATS 1.1.
     
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee.

 50. ALI (NISO Access and License Indicators)
     - Added <ali:license_ref> to license-p.class, as 
       alternatives inside <license>
     - Use the new class: license.class to hold the new ALI
       element <ali:free_to_read>, as an alternative to
       <license> inside <permissions>.
     - Created new parameter entity 'anyURI' to act as 
       documentation concerning the content of any element whose
       content is intended to be a URI.  

 49. JATS became version "1.1d3" and "v1.1d3 20150301"

     =============================================================
     JATS Version 1.1d2              (DAL/BTU) v1.1d2 (2014-09-30)
   
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the NISO membership to be changed. This
     Committee Draft 1.1d2 will be sent to the NISO voting
     membership, to become (if approved) NISO JATS 1.1.
     
     This catalog represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing

 48. VOLUME ISSUE GROUPING
   - Added new optional element <volume-issue-group> inside 
     <article-meta), following all volume and issue elements,
     to hold volume-issue pairs (or n-tuples) when a second
     and subsequent <volume> has its own related issue
     information.

 47. NEW ATTRIBUTE VALUES FOR @PUB-ID-TYPE
     Added three new values to @pub-id-types:
      - accession
      - arc
      - handle
 
 46. NEW ATTRIBUTE FOR EXT-LINK
     Added the CDATA attribute @assigning-authority to <ext-link>
     to the authority (such as CrossRef) responsible for the
     link

 45. NEW ATTRIBUTE FOR CONTRIB-ID
     Added the boolean attribute @authenticated to <contrib-id> to 
     record whether the agency that assigned the <contrib-id>
     says the identifier is authenticated. The default value
     is "false"

 44. MORE LINKING POSSIBILITIES
     Added the might-link-atts to:
      - volume-id
      - issue-id
     so that these potentially external identifiers can link,
     for example to a DOI.

 43. JATS became version "1.1d2" and "v1.1d2 20140930//EN"

 42. NEW ADDRESS ELEMENTS
      - Added 3 new elements: <city>, <state>, <postal-code>
      - Therefore introduced new PEs: city-element, state-elements,
        and postal-code-elements, all set initially to null strings.
      - Therefore introduced new PEs: city-atts, state-atts,
        and postal-code-atts.
      - Added the elements (in the default classes module) to:
          - address.class
          - addrress-line.class
        which adds then to: address, addr-line, aff, collab, 
        conf-loc, corresp, publisher-loc

 41. CHANGES TO <collab>
     Added @symbol attribute.
   
     =============================================================
     NISO JATS Version 1.1d1           (DAL/BTU) v1.1 (2013-11-15)
   
     ANSI/NISO Z39.96-2012 (pre-release for V1.0 BITS; Version 1.1d1) 

     NISO JATS Standing Committee met and answered the requests and
     suggestions from the NISO request forms.

     Details concerning ANSI/NISO Z39.96-2012 JATS-based DTDs,  
     XSDs, RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.3d2/

 40. CHANGES TO <on-behalf-of>
     Added <institution>, <institution-wrap>, and <xref>

 39. NEW <contrib> ATTRIBUTE
     Added @xml:lang as requested with <institution-id>

 38. NEW <era> ELEMENT
     Added <era> through date-parts.class to:
      - <string-date>

 37. NEW ELEMENT <era>
     Added to the model of <date>, optional following year,
     through date-model.

 36. XML Base
     Added the attribute @xml:base to the Global (common)
     attributes used on all elements.

 35. INSTITUTION WRAP ADDED
     Added the elements <institution-wrap> to the following elements
     through the institution-wrap.class:
      - <copyright-holder>
      - <on-behalf-of>
      - <publisher-name>
      - <std-organization>

 34. GLOBAL ATTRIBUTES - Added the new parameter entity 
         %jats-common-atts;
     to every element in this module. This PE adds (for now) the
     @id attribute and the @xml:base attribute to every element,
     whether metadata or narrative.
     Since the @id in this parameter entity is optional, a second
     parameter entity jats-common-atts-id-required was also added.
     The two are kept in sync with the jats-base-atts parameter 
     entity.
     This added a new attribute list to:
      - aff-alternatives
      - alternatives
      - citation-alternatives
      - collab-alternatives
      - custom-meta-group
      - institution-wrap
      - meta-name
      - meta-value
      - name-alternatives
      - permissions                          

 33. INSTITUTIONAL IDENTIFIERS
     - Added <institution-id> to hold institutional identifiers
       whether publisher specific ("AIP") or from an established 
       authorities ("Ringgold" and "ISNI").

     - Added <institution-wrap> to hold both the name of an
       institution <institution> and all of its institutional 
       identifiers <institution-id>. This element will be allowed
       everywhere institution is allowed.
                          

 32. ABSTRACTS AND KEYWORDS ON NEW STRUCTURES
     - Added <abstract> (through %abstract.class;) and <kwd-group> 
       (through %kwd-group.class;) to the following elements:
        - sec-meta (through %sec-meta-model;)

 31. ATTRIBUTE ALIGNMENT
     - Added @publication-format and @content-type to <issn>
       through %issn-atts The idea is to make the attributes
       for <issn> and <isbn> as similar as possible.

     - Added @publication-format to <isbn> through %isbn-atts 
     
     The idea is to make the attributes for <issn> and <isbn> as 
     similar as possible. <issn> retains @pub-type for historical
     reasons. <issn-l> attributes do not change.
   
     =============================================================
     NISO JATS Version 1.0             (DAL/BTU) v1.0 (2012-xx-xx)
   
     ANSI/NISO Z39.96-2012 (Version 1.0) 

     Details concerning ANSI/NISO Z39.96-2012 JATS-based DTDs,  
     XSDs, RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.3d2/

 30. CONTRIB-GROUP AND CONTRIB MODULE SHIFT - Moved <contrib>
     and <contrib-group> to common.ent from the module
     articlemeta.ent, since they are now used
     in both article metadata and journal metadata.

 29. CONTRIB ID - Added new element <contrib-id> using new PE 
     contrib-id.class, contrib-id-atts, contrib-id-model.
     Element holds one identifier, such as an ORCID, for a
     person such as a contributor or principal investigator.

 28. COLLABORATION ALTERNATIVES - Added new element 
     <collab-alternatives> using new PE %collab-alternatives-model;.
     Element holds different versions (for example, different
     language renderings) of a single collaboration.   
   
 27. DATE ATTRIBUTES - Added date attributes
      - to <conf-date> through %conf-date-atts
      - to <date> through %date-atts
      - to <string-date> through %string-date-atts
      - to <year> through %year-atts
      
       a) @iso-8601-date - ISO 8601 standard date format
          of the publication date. Format  YYYY-MM-DDThh:mm:ss 

       b) @calendar - Name of the calendar used (@calendar) in
          naming this publication date (such as "Gregorian", 
          "Japanese" (Emperor years), or "Islamic").

       c) Added @publication-format, so that both publication
          dates and history dates can separate the for amt of the
          publication from the version/lifecycle event. Thus
          both an "article" and a "manuscript" might be received.

 27. PUBLICATION IDENTIFIER @pub-id-type
      - Added "std-designation" as value in predefined  list 
        for '@pub-id-type' (% pub-id-types).
      - Added new value "arxiv" as value in predefined  list 
        for '@pub-id-type' (% pub-id-types).
     "pub-id-types" are used within citations for 
     <object-id> and <pub-id>; 
     used within the article metadata for <article-id>, 
     <issue-id>, and <volume-id>; 
     used within many body elements for <object-id>.
       
 26. LONG DESCRIPTION - Added the might-link attributes to
     <long-desc> because of an argument over what <long-desc>
     should be. The semantic web folks want it to be a URI, the
     librarians and accessibility (for pronouncing readers) want
     to find a real description there, not a link. So JATS needs
     to handle both.
   
 25. ISSN Linking - Added new element <issn-l> with the usual
     %issn-l-elements; and %issn-l-atts;.
   
 24. MODULE MOVEMENT (from increased reference content)

     a) Element <degrees> - Moved <degrees> and its parameter
        entities into this module from JATS-articlemeta.ent.
   
     b) Element <supplement> - Moved <supplement> and its parameter
        entities into this module from JATS-articlemeta.ent.
   
     c) Element <conf-sponsor> - Moved <conf-sponsor> and its parameter
        entities into this module from JATS-articlemeta.ent.
   
     d) Element <on-behalf-of> - Moved <on-behalf-of> and its parameter
        entities into this module from JATS-articlemeta.ent.
   
 23. Element <license-p> - Added @content-type, which can hold
     values such as "open-access", "CCC-statement",
     "non-commercial-use".
   
 22. Updated the DTD-version attribute to "1.0" and the formal
     public identifier to the date: "v1.0 20120330//EN".

     =============================================================
     Version 0.4                       (DAL/BTU) v0.4 (2011-01-31)
   
     This Tag Set is in the process of becoming a NISO standard.
     The version numbers are starting over from 0.4", as a Trial
     Use Draft, to be made into "Version 1.0" when the Tag Suite 
     becomes a NISO standard. Thus, the version number that would
     have been "NLM Version 3.1 Draft" has become NISO JATS
     "Version 0.4".

     Details on NISO Trial Use Draft Version 0.4 are available at
           http://jats.nlm.nih.gov/JATS-0.4.
   
 21. Updated the DTD-version attribute to "0.4" 
   
 18. Updated the public identifier to "v0.4 20110131", 
     modified the formal public identifier to include "JATS (Z39.96)",
     and the filename as delivered to include "JATS" and the
     new version number "0".
           http://jats.nlm.nih.gov/0.4.

 17. MIXED CITATION ATTS PE - Was defined twice, identically,
     deleted one of them.

 16. ACCESSIBILITY - Added @alt to <label> through %label-atts;

 15. ALTERNATIVES ATTRIBUTES- Created a new parameter entity for
     attributes for <alternatives>; currently null

 14. @SPECIFIC-USE and @XML:LANG - Added the @specific-use and
     @xml:lang to the following elements:
       - anonymous through anonymous-atts (both) NEW PE
       - conf-acronym through conf-acronym-atts (both)
       - conf-date through conf-date-atts (both)
       - conf-loc through conf-loc-atts (both)
       - conf-name through conf-name-atts (both)
       - conf-num through conf-num-atts (both)
       - conf-sponsor through conf-sponsor-atts (both)
       - conf-theme through conf-theme-atts (both)
       - day through day-atts (@specific-use)
       - degrees through degrees-atts (both)
       - element-citation through citation-atts (both)
       - etal through etal-atts (both) NEW PE
       - ext-link through ext-link-atts (@xml:lang; @specific-use
           already)
       - institution through institution-atts (both)
       - issue-part through issue-part (both + content-type) NEW PE
       - isbn through isbn-atts (both) NEW PE
       - label through label-atts (both) NEW PE
       - mixed-citation through citation-atts (both)
       - name through name-atts (@xml:lang; @specific-use already)
       - notes through notes-atts (both)
       - prefix through prefix-atts (both)
       - publisher-loc through publisher-loc-atts (both)
       - publisher-name through publisher-name-atts (both)
       - string-name through string-name-atts (both and
            content-type and name-style) NEW PE
       - suffix through suffix-atts (both)
       - textual-form through textual-form-atts (both)
       - trans-subtitle through trans-subtitle-atts
          (@xml:lang only) NEW PE
       - x through x-atts (both)

 13. AFFILIATION ALTERNATIVES - Created new the element
     <aff-alternatives> to hold multiple <aff>s that are
     representations of a single affiliation, for example, the
     name of an institution in two languages or two scripts.
     New PE; New attribute PE (currently null)

 12. RELATED-ARTICLE-ATTS - Removed the dependency that tied
     %related-article-atts to journal-id-atts and added all
     the journal-id attributes to related-article-atts explicitly.

 11. TITLE ELEMENTS - Removed the dependency which had both
     <subtitle> and <alt-title> modeled with the same parameter
     entity %title-elements;. Created new PEs for each element
     but set them (as the default) to %title-elements so that no
     customization would break. Models changed:
       - alt-title      %alt-title-elements;
       - subtitle       %subtitle-elements; (defined in
                                article-meta3.ent;)
       - trans-title    %trans-title-elements;
       - trans-subtitle %trans-subtitle-elements;

 10. TEXTUAL FORM MODEL - Changed the textual-form-elements
     parameter entity to begin with an OR bar, since it names
     elements to be mixed with #PCDATA.
     *****Customization Alert: New parameter entity could break some
     customizations. Check your %textual-form-elements parameter
     entity. *****

  9. PUBLISHER - Allowed <publisher-name> and <publisher-loc> to
     repeat (as an unnamed-parenthetical) inside <publisher>.
     This will allow co-publishers and publisher names and locations
     in more than one language. Added to facilitate multiple
     languages. Also added @specific-use and @xml:lang to
     both publisher-name and publisher-loc.

  8. ATTLIST PARAMETER ENTITIES - Changed the attribute lists
     from using the same parameter entity (%day-atts;) to using:
       - day    %day-atts;   (no change)
       - month  %month-atts;
       - season %season-atts;

  7. ISBN - Added "isbn" as value in predefined list for
    '@pub-id-type' (% pub-id-types)

  6. TEXTUAL FORM - Added the following attributes: (new)
       - @specific-use
       - @xml:lang

  5. NAME ALTERNATIVES - Created a new wrapper element for more
     than one version of a personal name, for example, the name in
     Japanese kana characters and a transliterated form of the name
     in Latin alphabet, or a regular name and a search version
     that transliterates umlauts to unaccented characters.
      - new element <name-alternatives>
      - new PE name-alternatives-model
      - new PE name-alternatives-atts (currently null)

  4. STRING NAME - Added the following attributes: (new)
       - @content-type
       - @specific-use
       - @name-style
       - @xml:lang

  3. PERSON'S NAME STYLE
       - Added the value "given-only" to the @name-style on <name>

  2. PERSON'S NAME - Allowed <given-names> as an alternative
     to <surname, given-names?) to accommodate the Indian
     names that are only a given name.

     =============================================================
     Version 3.0                       (DAL/BTU) v3.0 (2007-10-31)

     Version 3.0 is the first non-backward-compatible release.
     In addition to the usual incremental changes, some
     elements and attributes have been renamed and/or remodeled
     to better meet user needs and to increase tag set consistency.
     All module change histories are available through the Tag Suite
     web site at http://dtd.nlm.nih.gov.

     Details on version 3.0 are available at
           http://dtd.nlm.nih.gov/3.0.

  1. Updated public identifier to "v3.0 20071031//EN"              -->

<!-- ============================================================= -->
<!-- Details concerning 
     ANSI/NISO Z39.96-2015 JATS-based DTDs, XSDs, 
     RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.1/                            -->

<!-- ============================================================= -->
<!--                    PARAMETER ENTITIES FOR ATTRIBUTE VALUES    -->
<!-- ============================================================= -->


<!--                    ARTICLE/PUBLICATION IDENTIFIER TYPES       -->
<!--                    Values for the @pub-id attribute. Used:
                        1) Within citations with the <pub-id> and 
                        <object-id> elements to name the type of
                        identifier (such as DOI) or the organization 
                        or system that defined the identifier for the 
                        identifier of the journal article or a cited 
                        publication, such as a book or standard.
                        2) Within article metadata with the
                        <article-id>, <issue-id>, and <volume-id>
                        elements to name the type of identifier or 
                        identifying agency.
                        4) Within many body elements to perform a 
                        similar function for the <object-id> element.
                                                                   -->
<!ENTITY % pub-id-types  "accession | archive | ark | art-access-id | 
                          arxiv | coden | doaj | doi | 
                          handle | index | isbn | 
                          manuscript | medline | mr | other | 
                          pii | pmcid |  pmid | publisher-id | sici |
                          std-designation | zbl |
                          custom"                                    >
                                                           

<!-- ============================================================= -->
<!--                    PARAMETER ENTITIES FOR CONTENT MODELS      -->
<!-- ============================================================= -->


<!--                    ANY URI SCHEMA TYPE (as documentation)     -->
<!--                    Indicates that in an XSD or RNG Schema, this
                        element would be typed as the simple type
                        'anyURI". This has no enforcement power in a 
                        DTD, it is intended for human guidance.
                        but is only for human guidance in a DTD   -->
<!ENTITY % anyURI       "(#PCDATA)"                                 >


<!-- ============================================================= -->
<!--                    PARAMETER ENTITIES FOR ATTRIBUTE VALUES    -->
<!-- ============================================================= -->


<!--                    ARTICLE/PUBLICATION IDENTIFIER TYPES       -->
<!--                    Values for the @pub-id attribute. Used:
                        1) Within citations with the <pub-id> and 
                        <object-id> elements to name the type of
                        identifier (such as DOI) or the organization 
                        or system that defined the identifier for the 
                        identifier of the journal article or a cited 
                        publication, such as a book or standard.
                        2) Within article metadata with the
                        <article-id>, <issue-id>, and <volume-id>
                        elements to name the type of identifier or 
                        identifying agency.
                        4) Within many body elements to perform a 
                        similar function for the <object-id> element.
                                                                   -->

<!-- ============================================================= -->
<!--                    PARAMETER ENTITIES FOR FULL CONTENT MODELS -->
<!-- ============================================================= -->


<!--                    DATE ELEMENTS MODEL                        -->
<!--                    The content models for elements that describe
                        dates, such as Publication Date <pub-date>
                        and History Dates <date>.                  -->
<!ENTITY % date-model   "( ( (day?, month?) | season)?,
                          year?, era?, string-date?)"                >


<!--                    CONTENT MODEL FOR A STRUCTURAL SECTION     -->
<!--                    The model for a section that requires that a
                        section title be present, used for elements
                        such as Section and Appendix.              -->
<!ENTITY % sec-model    "(sec-meta?, label?, title, (%para-level;)*,
                          (%sec-level;)*,
                          (%sec-back-matter-mix;)* )"                >


<!--                    CONTENT MODEL FOR A SECTION METADATA       -->
<!--                    In some works, each section has a different
                        author or some sections are authored by
                        different contributors from the enclosing
                        article. This wrapper element for
                        section-level metadata is used to capture
                        information such as those contributors.
                                                                   -->
<!ENTITY % sec-meta-model
                        "((%id.class;)*, (%contrib-group.class;)*, 
                          (%abstract.class;)*, (%kwd-group.class;)*,
                          (%subj-group.class;)*,
                          permissions?)"       >


<!--                    CONTENT MODEL FOR AN UNTITLED SECTION      -->
<!--                    The model for a section-like structure that
                        may or may not have an initial title       -->
<!ENTITY % sec-opt-title-model
                        "(sec-meta?, label?, title?, (%para-level;)*,
                          (%sec-level;)*,
                          (%sec-back-matter-mix;)* )"                >


<!-- ============================================================= -->
<!--                    PARAMETER ENTITIES FOR MIXED CONTENT       -->
<!-- ============================================================= -->


<!--                    LINK ELEMENTS                              -->
<!--                    Elements for use in the linking elements
                        such as <xref>, <target>, and <ext-link>   -->
<!ENTITY % link-elements
                        "| %emphasis.class; | %phrase-content.class; |
                         %subsup.class;"                             >


<!--                    PARAGRAPH ELEMENTS                         -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        a paragraph <p>.
                        Design Note: There is a major overlap between
                        this parameter entity and the mix for elements
                        that are at the same level as a paragraph.
                        Inline elements appear only inside a
                        paragraph, but block elements such as quotes
                        and lists may appear either within a
                        paragraph or at the same level as a
                        paragraph. This serves a requirement in a
                        repository DTD, since some incoming material
                        will have restricted such elements to only
                        inside a paragraph, some incoming material
                        will have restricted them to only outside a
                        paragraph and some may allow them in both
                        places. Thus the DTD must allow for them to
                        be in either or both.
                        Design Note: Inline mixes begin with an
                        OR bar                                     -->
<!ENTITY % p-elements   "| %address-link.class; |
                         %article-link.class; |
                         %block-display.class; | %block-math.class; |
                         %citation.class; |  %emphasis.class; |
                         %funding.class; |  %inline-math.class; |
                         %inline-display-noalt.class; |
                         %list.class; | %math.class; |
                         %phrase.class; | %rest-of-para.class; |
                         %simple-link.class; |
                         %subsup.class;"                             >


<!-- ============================================================= -->
<!--                    PARAMETER ENTITIES FOR TITLES              -->
<!-- ============================================================= -->


<!--                    TITLE ELEMENTS                             -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        a paragraph <title>.
                        Design Note: All inline mixes begin with an
                        OR bar, but since %simple-phrase; is an
                        inline mix, the OR bar is already there.   -->
<!ENTITY % title-elements
                        "%simple-phrase; | %break.class;"            >


<!--                    ALTERNATE TITLE ELEMENTS                   -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        a paragraph <alt-title>.
                        An earlier version of this tag set used the
                        parameter entity %title-elements; for many
                        title elements including <alt-title>.      -->
<!ENTITY % alt-title-elements
                        "%title-elements;"                           >


<!--                    TRANSLATED TITLE ELEMENTS                  -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        a paragraph <trans-title>.
                        An earlier version of this tag set used the
                        parameter entity %title-elements; for many
                        title elements including <trans-title>.    -->
<!ENTITY % trans-title-elements
                        "%title-elements;"                           >


<!--                    TRANSLATED SUBTITLE ELEMENTS               -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        a paragraph <subtitle>.
                        An earlier version of this tag set used the
                        parameter entity %title-elements; for many
                        title elements including <trans-subtitle>. -->
<!ENTITY % trans-subtitle-elements
                        "%title-elements;"                           >


<!-- ============================================================= -->
<!--                    PARAMETER ENTITIES FOR LINKING ATTRIBUTES  -->
<!--                    THE FOLLOWING FEW ARE SHARED ATTRIBUTES    -->
<!-- ============================================================= -->


<!--                    COMMON ATTRIBUTES                          -->
<!--                    These lists of attributes will be added to 
                        nearly every element, in both the document 
                        metadata and the document body, of the entire 
                        Tag Set:
                          - including table elements for both the 
                              XHTML-inspired and OASIS-inspired
                              table model elements
                          - excluding only <mml:math> and 
                              <xi:include> (whose namespaces JATS
                              does not control).
                           Great care should be taken to use these
                        attributes judiciously, not as a shortcut to
                        thinking through where an attribute should be
                        used. Potential use cases include adding
                        @xml:lang or some RDFa attribute to every 
                        elements.                                  -->


<!--                    BASE ATTRIBUTES                            -->
<!--                    Holds all the common attributes except @id.
                        Used to keep the two common attribute lists 
                        in sync. To add a global attribute, modify
                        this list.                                 -->
<!ENTITY % jats-base-atts
            "xml:base   CDATA                             #IMPLIED"  >


<!--                    JATS COMMON ATTRIBUTES                     -->
<!--                    Holds all the common attributes, as defined in
                        the base attribute parameter entity, plus an
                        optional @id.                              -->
<!ENTITY % jats-common-atts
            "id         ID                                #IMPLIED 
             %jats-base-atts;"                                       >


<!--                    JATS COMMON ATTRIBUTES (ID REQUIRED)       -->
<!--                    Holds all the common attributes, as defined in
                        the base attribute parameter entity, plus a 
                        required @id.                              -->
<!ENTITY % jats-common-atts-id-required
            "id         ID                                #REQUIRED
             %jats-base-atts;"                                       >



<!--                    XLINK LINK ATTRIBUTES                      -->
<!--                    Used for elements that are a link by
                        definition, such as the <xref> element.    -->
<!ENTITY % link-atts
            "xmlns:xlink CDATA                            #IMPLIED
             xlink:type  (simple)                         #IMPLIED
             xlink:href  CDATA                            #REQUIRED 
             xlink:role  CDATA                            #IMPLIED
             xlink:title CDATA                            #IMPLIED
             xlink:show  (embed | new | none | other | replace)
                                                          #IMPLIED
             xlink:actuate
                         (none | onLoad | onRequest | other)
                                                          #IMPLIED
             hreflang    NMTOKEN                          #IMPLIED"  >



<!--                    MIGHT LINK XLINK ATTRIBUTES                -->
<!--                    Used for elements which may need to link to
                        external sources or other objects within
                        the document, but may not necessarily act
                        as a link at all.  The attribute
                        "xlink:href" identifies the object to which
                        the link points.                           -->
<!ENTITY % might-link-atts
            "xmlns:xlink CDATA                            #IMPLIED
             xlink:type  (simple)                         #IMPLIED
             xlink:href  CDATA                            #IMPLIED
             xlink:role  CDATA                            #IMPLIED
             xlink:title CDATA                            #IMPLIED
             xlink:show  (embed | new | none | other | replace)
                                                          #IMPLIED
             xlink:actuate
                         (none | onLoad | onRequest | other)
                                                          #IMPLIED
             hreflang    NMTOKEN                          #IMPLIED"  >



<!--                    CITATION ATTRIBUTES                        -->
<!--                    Attributes for all two kinds of citations:
                        <element-citation> and <mixed-citation>    -->
<!ENTITY % citation-atts
            "%jats-common-atts;                                        
             publication-type
                        CDATA                             #IMPLIED
             publisher-type
                        CDATA                             #IMPLIED
             publication-format
                        CDATA                             #IMPLIED
             use-type   CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED
             %might-link-atts;"                                      >


<!-- ============================================================= -->
<!--                    PARAMETER ENTITIES FOR ATTRIBUTES LISTS    -->
<!--                    (ALPHABETICAL ORDER)                       -->
<!-- ============================================================= -->


<!--                    AFFILIATION ATTRIBUTES                     -->
<!--                    Attributes for the <ack> element           -->
<!ENTITY % ack-atts
            "%jats-common-atts;                                        
             specific-use
                        CDATA                             #IMPLIED
             content-type
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    ADDRESS ATTRIBUTES                         -->
<!--                    Attributes for the <address> element       -->
<!ENTITY % address-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    ADDRESS LINE ATTRIBUTES                    -->
<!--                    Attributes for the <addr-line> element     -->
<!ENTITY % addr-line-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    AFFILIATION ATTRIBUTES                     -->
<!--                    Attributes for the Affiliation <aff>
                        element                                    -->
<!ENTITY % aff-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             rid        IDREFS                            #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    AFFILIATION ALTERNATIVES ATTRIBUTES        -->
<!--                    Attributes for the <aff-alternatives>
                        element                                    -->
<!ENTITY % aff-alternatives-atts
            "%jats-common-atts;"                                     >                                        


<!--                    ALTERNATE TEXT ATTRIBUTES                  -->
<!--                    Attributes for the <alt-text> element      -->
<!ENTITY % alt-text-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    ANONYMOUS ATTRIBUTES                       -->
<!--                    Attributes for the <anonymous> element     -->
<!ENTITY % anonymous-atts
            "%jats-common-atts;                                        
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    ALTERNATIVES ATTRIBUTES                    -->
<!--                    Attributes for the <alternatives> element  -->
<!ENTITY % alternatives-atts
            "%jats-common-atts;"                                     >                                        


<!--                    ARTICLE TITLE ATTRIBUTES                   -->
<!--                    Attributes for the <article-title> element -->
<!ENTITY % article-title-atts
            "%jats-common-atts;                                        
             xml:lang   NMTOKEN                            #IMPLIED" >


<!--                    ATTRIBUTION ATTRIBUTES                     -->
<!--                    Attributes for the <attrib> element        -->
<!ENTITY % attrib-atts
            "%jats-common-atts;                                        
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    BIOGRAPHY ATTRIBUTES                       -->
<!--                    Attributes for <bio> element               -->
<!ENTITY % bio-atts
            "%jats-common-atts;                                        
             rid        IDREFS                            #IMPLIED
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED
             %might-link-atts;"                                      >


<!--                    CITATION ALTERNATIVES ATTRIBUTES           -->
<!--                    Attributes for the <citation-alternatives>
                        element                                    -->
<!ENTITY % citation-alternatives-atts
            "%jats-common-atts;"                                     >                                        


<!--                    CITY ATTRIBUTES                            -->
<!--                    Attributes for the <city> element          -->
<!ENTITY % city-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    COLLABORATION ATTRIBUTES                   -->
<!--                    Attributes for <collab>                    -->
<!ENTITY % collab-atts
            "%jats-common-atts;                                        
             collab-type
                        CDATA                             #IMPLIED
             symbol     CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED
             %might-link-atts;"                                      >


<!--                    COLLABORATION ALTERNATIVES ATTRIBUTES      -->
<!--                    Attributes for the <collab-alternatives>
                        element                                    -->
<!ENTITY % collab-alternatives-atts
            "%jats-common-atts;"                                      >                                        


<!--                    CONFERENCE DATE ATTRIBUTES                 -->
<!--                    Attributes for the <conf-date> element     -->
<!ENTITY % conf-date-atts
            "%jats-common-atts;                                        
             iso-8601-date
                        CDATA                             #IMPLIED
             calendar   CDATA                             #IMPLIED
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    CONFERENCE LOCATION ATTRIBUTES             -->
<!--                    Attributes for the <conf-loc> element      -->
<!ENTITY % conf-loc-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    CONFERENCE NAME ATTRIBUTES                 -->
<!--                    Attributes for the <conf-name> element.    -->
<!ENTITY % conf-name-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    CONFERENCE SPONSOR ATTRIBUTES              -->
<!--                    Attributes for the <conf-sponsor> element. -->
<!ENTITY % conf-sponsor-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    CONTRIBUTOR ATTRIBUTES                     -->
<!--                    Attributes for the <contrib> element       -->
<!ENTITY % contrib-atts
            "%jats-common-atts;                                        
             contrib-type
                        CDATA                             #IMPLIED
             corresp    (no | yes)                        #IMPLIED
             equal-contrib
                        (no | yes)                        #IMPLIED
             deceased   (no | yes)                        #IMPLIED
             rid        IDREFS                            #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             %might-link-atts;"                                      >


<!--                    CONTRIBUTOR GROUP ATTRIBUTES               -->
<!--                    Attributes for the <contrib-group> element -->
<!ENTITY % contrib-group-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED"  >


<!--                    CONTRIBUTOR IDENTIFIER ATTRIBUTES          -->
<!--                    Attributes for the <contrib-id> 
                        element.                                   -->
<!ENTITY % contrib-id-atts
            "%jats-common-atts;                                        
             contrib-id-type
                        CDATA                             #IMPLIED
             assigning-authority                
                        CDATA                             #IMPLIED
             authenticated
                        (true | false)                    'false'
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    COPYRIGHT HOLDER ATTRIBUTES                -->
<!--                    Attributes for the <copyright-holder>
                        element.                                   -->
<!ENTITY % copyright-holder-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    COPYRIGHT STATEMENT ATTRIBUTES             -->
<!--                    Attributes for the <copyright-statement>
                        element.                                   -->
<!ENTITY % copyright-statement-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    COPYRIGHT YEAR ATTRIBUTES                  -->
<!--                    Attributes for the <copyright-year>
                        element                                    -->
<!ENTITY % copyright-year-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED"  >


<!--                    COUNTRY ATTRIBUTES                         -->
<!--                    Attributes for the <country> element       -->
<!ENTITY % country-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             country    CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    CUSTOM METADATA ATTRIBUTES                 -->
<!--                    Attributes for the <custom-meta> element   -->
<!ENTITY % custom-meta-atts
            "%jats-common-atts;                                        
             specific-use
                        CDATA                             #IMPLIED
             assigning-authority                
                        CDATA                             #IMPLIED
             vocab      CDATA                             #IMPLIED
             vocab-identifier
                        CDATA                             #IMPLIED
             vocab-term CDATA                             #IMPLIED
             vocab-term-identifier
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED
             %might-link-atts;"                                      >


<!--                    CUSTOM METADATA GROUP ATTRIBUTES           -->
<!--                    Attributes for the <custom-meta-group>
                        element                                    -->
<!ENTITY % custom-meta-group-atts
            "%jats-common-atts;                                                                                    content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >
        


<!--                    DATE (HISTORICAL) ATTRIBUTES               -->
<!--                    Attributes for the <date> element          -->
<!ENTITY % date-atts
            "%jats-common-atts;                                        
             date-type  CDATA                             #IMPLIED
             publication-format
                        CDATA                             #IMPLIED
             iso-8601-date
                        CDATA                             #IMPLIED
             calendar   CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED"  >


<!--                    DAY ATTRIBUTES                             -->
<!--                    Attributes for the <day> element           -->
<!ENTITY % day-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    DEFINITION LIST: DEFINITION ATTRIBUTES     -->
<!--                    Attribute list for the <def> element       -->
<!ENTITY % def-atts
            "%jats-common-atts;                                        
             rid        IDREFS                            #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    DEGREES ATTRIBUTES                         -->
<!--                    Attributes for the <degrees> element       -->
<!ENTITY % degrees-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    ELEMENT CITATION ATTRIBUTES                -->
<!--                    Attributes for <element-citation>          -->
<!ENTITY % element-citation-atts
            "%citation-atts;"                                        >


<!--                    ELECTRONIC LOCATION IDENTIFIER ATTRIBUTES
                        Attribute list for the <elocation-id>
                        element                                    -->
<!ENTITY % elocation-id-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             seq        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED"  >


<!--                    EMAIL ATTRIBUTES                           -->
<!--                    Attribute list for the <email> element     -->
<!ENTITY % email-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED
             %might-link-atts;"                                      >


<!--                    ERA ATTRIBUTES                             -->
<!--                    Attributes for the <era> element           -->
<!ENTITY % era-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    ET AL. ATTRIBUTES                          -->
<!--                    Attribute list for the <etal> element      -->
<!ENTITY % etal-atts
            "%jats-common-atts;                                        
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    EXTERNAL LINK ATTRIBUTES                   -->
<!--                    Attribute list for external links, such as
                        <ext-link>                                 -->
<!ENTITY % ext-link-atts
            "%jats-common-atts;                                        
             ext-link-type
                        CDATA                             #IMPLIED
             assigning-authority                
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED
             %might-link-atts;"                                      >


<!--                    FAX ATTRIBUTES                             -->
<!--                    Attribute list for the <fax> element       -->
<!ENTITY % fax-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED"  >


<!--                    FIRST PAGE ATTRIBUTES                      -->
<!--                    Attribute list for the <fpage> element     -->
<!ENTITY % fpage-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             seq        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    FREE TO READ ATTRIBUTES                    -->
<!--                    Attributes for the ALI namespaced
                        <ali:free_to_read> element                 -->
<!ENTITY % free-to-read-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             end_date   CDATA                             #IMPLIED
             start_date CDATA                             #IMPLIED
             %ali.xmlns.attrib;"                                     >


<!--                    GIVEN NAMES ATTRIBUTES                     -->
<!--                    Attribute list for the <given-names> element
                                                                   -->
<!ENTITY % given-names-atts
            "%jats-common-atts;                                        
             initials  CDATA                             #IMPLIED"  >


<!--                    INSTITUTION ATTRIBUTES                     -->
<!--                    Attribute list for <institution>           -->
<!ENTITY % institution-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED
             %might-link-atts;"                                      >


<!--                    INSTITUTION IDENTIFIER ATTRIBUTES          -->
<!--                    Attribute list for <institution-id>        -->
<!ENTITY % institution-id-atts
            "%jats-common-atts;                                        
             institution-id-type
                        CDATA                             #IMPLIED
             assigning-authority                
                        CDATA                             #IMPLIED
             vocab      CDATA                             #IMPLIED
             vocab-identifier
                        CDATA                             #IMPLIED
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    INSTITUTION WRAPPER ATTRIBUTES             -->
<!--                    Attributes for the <institution-wrap>
                        element                                    -->
<!ENTITY % institution-wrap-atts
            "%jats-common-atts;"                                          >                                        


<!--                    ISBN ATTRIBUTES                            -->
<!--                    Attributes for the <isbn> element          -->
<!ENTITY % isbn-atts
            "%jats-common-atts;                                        
             publication-format
                        CDATA                             #IMPLIED
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             assigning-authority                
                        CDATA                             #IMPLIED"  >


<!--                    ISSN ATTRIBUTES                            -->
<!--                    Attribute list for the <issn> element      -->
<!ENTITY % issn-atts
            "%jats-common-atts;                                        
             pub-type   CDATA                             #IMPLIED
             publication-format
                        CDATA                             #IMPLIED
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             assigning-authority                
                        CDATA                             #IMPLIED"  >


<!--                    ISSN-L (INKING ISSN) ATTRIBUTES            -->
<!--                    Attribute list for the <issn-l> element    -->
<!ENTITY % issn-l-atts
            "%jats-common-atts;                                        
             specific-use
                        CDATA                             #IMPLIED
             assigning-authority                
                        CDATA                             #IMPLIED"  >


<!--                    ISSUE ATTRIBUTES                           -->
<!--                    Attribute list for the <issue> element     -->
<!ENTITY % issue-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             seq        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    ISSUE IDENTIFIER ATTRIBUTES                -->
<!--                    Attributes for the <issue-id> element      -->
<!ENTITY % issue-id-atts
            "%jats-common-atts;                                        
             pub-id-type
                        CDATA                             #IMPLIED
             assigning-authority                
                        CDATA                             #IMPLIED
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED
             %might-link-atts;"                                      >


<!--                    ISSUE PART ATTRIBUTES                      -->
<!--                    Attribute list for <issue-part> element    -->
<!ENTITY % issue-part-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    ISSUE SPONSOR ATTRIBUTES                   -->
<!--                    Attribute list for <issue-sponsor> element -->
<!ENTITY % issue-sponsor-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    ISSUE SUBTITLE ATTRIBUTES                  -->
<!--                    Attribute list for the <issue-subtitle> 
                        element                                    -->
<!ENTITY % issue-subtitle-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    ISSUE TITLE ATTRIBUTES                     -->
<!--                    Attribute list for the <issue-title> 
                        element                                    -->
<!ENTITY % issue-title-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    JOURNAL IDENTIFIER ATTRIBUTES              -->
<!--                    Attribute list for journal identifier
                        <journal-id> element                       -->
<!ENTITY % journal-id-atts
            "%jats-common-atts;                                        
             journal-id-type
                        CDATA                             #IMPLIED
             assigning-authority                
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    LABEL ATTRIBUTES                           -->
<!--                    Attributes for the <label> element         -->
<!ENTITY % label-atts
            "%jats-common-atts;                                        
             alt        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    LICENSE ATTRIBUTES                         -->
<!--                    Attributes for the <license> element       -->
<!ENTITY % license-atts
            "%jats-common-atts;                                        
             license-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED
             %might-link-atts;"                                      >


<!--                    LICENSE PARAGRAPH ATTRIBUTES               -->
<!--                    Attributes for the <license> element       -->
<!ENTITY % license-p-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED"  >


<!--                    LICENSE REFERENCE ATTRIBUTES               -->
<!--                    Attributes for the ALI namespaced
                        <ali:license_ref> element                  -->
<!ENTITY % license-ref-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             start_date CDATA                             #IMPLIED
             %ali.xmlns.attrib;"                                     >


<!--                    LONG DESCRIPTION ATTRIBUTES                -->
<!--                    Attributes for the <long-desc> element     -->
<!ENTITY % long-desc-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED
             %might-link-atts;"                                      >


<!--                    LPAGE ATTRIBUTES                           -->
<!--                    Attributes for the <lpage> element         -->
<!ENTITY % lpage-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    METADATA NAME (CUSTOM) ATTRIBUTES          -->
<!--                    Attributes for <meta-name> element         -->
<!ENTITY % meta-name-atts
            "%jats-common-atts;"                                     >                                       


<!--                    METADATA VALUE (CUSTOM) ATTRIBUTES          -->
<!--                    Attributes for <meta-value> element         -->
<!ENTITY % meta-value-atts
            "%jats-common-atts;"                                       >                                       


<!--                    MIXED CITATION ATTRIBUTES                  -->
<!--                    Attributes for <mixed-citation> element    -->
<!ENTITY % mixed-citation-atts
            "%citation-atts;"                                        >


<!--                    MONTH ATTRIBUTES                           -->
<!--                    Attributes for the <month> element         -->
<!ENTITY % month-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    NAME ATTRIBUTES                            -->
<!--                    Attribute list for the <name> element      -->
<!ENTITY % name-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             name-style (western | eastern | islensk |
                         given-only)                      'western'
             specific-use
                        CDATA                             #IMPLIED
             xml:lang  NMTOKEN                            #IMPLIED"  >


<!--                    NAME ALTERNATIVES ATTRIBUTES               -->
<!--                    Attributes for the <name-alternatives>
                        element                                    -->
<!ENTITY % name-alternatives-atts
            "%jats-common-atts;"                                      >                                       


<!--                    NOTES ATTRIBUTES                           -->
<!--                    Attribute list for the <note> element      -->
<!ENTITY % notes-atts
            "%jats-common-atts;                                        
             notes-type CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    OBJECT IDENTIFIER ATTRIBUTES               -->
<!--                    Attributes for the <object-id> element     -->
<!ENTITY % object-id-atts
            "%jats-common-atts;                                        
             pub-id-type
                        CDATA                             #IMPLIED
             assigning-authority                
                        CDATA                             #IMPLIED
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED"  >


<!--                    ON BEHALF OF ATTRIBUTES                    -->
<!--                    Attributes for the <on-behalf-of> element  -->
<!ENTITY % on-behalf-of-atts
            "%jats-common-atts;                                        
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    PAGE RANGE ATTRIBUTES                      -->
<!--                    Attributes for the <page-range> element    -->
<!ENTITY % page-range-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    PERMISSIONS ATTRIBUTES                     -->
<!--                    Attributes for the <permission> element    -->
<!ENTITY % permissions-atts
            "%jats-common-atts;"                                     >                                        


<!--                    PHONE ATTRIBUTES                           -->
<!--                    Attributes for the <phone> element         -->
<!ENTITY % phone-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED"  >


<!--                    POSTAL CODE ATTRIBUTES                     -->
<!--                    Attributes for the <postal-code> element   -->
<!ENTITY % postal-code-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    PREFIX ATTRIBUTES                          -->
<!--                    Attributes for the <prefix> element        -->
<!ENTITY % prefix-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    PRICE ATTRIBUTES                           -->
<!--                    Attributes for the <price> element         -->
<!ENTITY % price-atts
            "%jats-common-atts;                                        
             currency   CDATA                             #IMPLIED
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    PUBLISHER ATTRIBUTES                       -->
<!--                    Attributes for the <publisher> element     -->
<!ENTITY % publisher-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED"  >


<!--                    PUBLISHER LOCATION ATTRIBUTES              -->
<!--                    Attributes for the <publisher-loc> element -->
<!ENTITY % publisher-loc-atts
            "%jats-common-atts;                                        
             specific-use
                        CDATA                             #IMPLIED
             xml:lang  NMTOKEN                            #IMPLIED"  >


<!--                    PUBLISHER NAME ATTRIBUTES                  -->
<!--                    Attributes for the <publisher-name> element-->
<!ENTITY % publisher-name-atts
            "%jats-common-atts;                                        
             specific-use
                        CDATA                             #IMPLIED
             xml:lang  NMTOKEN                            #IMPLIED"  >


<!--                    RELATED ARTICLE ATTRIBUTES                 -->
<!--                    Attributes for <related-article>           -->
<!ENTITY % related-article-atts
            "%jats-common-atts;                                        
             related-article-type
                        CDATA                             #REQUIRED
             ext-link-type
                       CDATA                              #IMPLIED
             vol       CDATA                              #IMPLIED
             page      CDATA                              #IMPLIED
             issue     CDATA                              #IMPLIED
             elocation-id
                       CDATA                              #IMPLIED
             journal-id
                       CDATA                              #IMPLIED
             journal-id-type
                        CDATA                             #IMPLIED
             specific-use
                       CDATA                              #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED
             %might-link-atts;"                                      >


<!--                    ROLE ATTRIBUTES                            -->
<!--                    Attributes for the <role> element          -->
<!ENTITY % role-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             degree-contribution
                        CDATA                             #IMPLIED
             vocab      CDATA                             #IMPLIED
             vocab-identifier
                        CDATA                             #IMPLIED
             vocab-term CDATA                             #IMPLIED
             vocab-term-identifier
                        CDATA                             #IMPLIED
             assigning-authority                
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    SEASON ATTRIBUTES                          -->
<!--                    Attributes for the <season> element        -->
<!ENTITY % season-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    SIGNATURE ATTRIBUTES                       -->
<!--                    Attributes for the <sig> element           -->
<!ENTITY % sig-atts
            "%jats-common-atts;                                        
             rid        IDREFS                            #IMPLIED
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    SIGNATURE BLOCK ATTRIBUTES                 -->
<!--                    Attributes for the <sig-block> element     -->
<!ENTITY % sig-block-atts
            "%jats-common-atts;                                        
             rid       IDREFS                            #IMPLIED
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED"  >


<!--                    SIZE ATTRIBUTES                            -->
<!--                    Attribute list for the <size> element      -->
<!ENTITY % size-atts
            "%jats-common-atts;                                        
             units      CDATA                             #REQUIRED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    STATE ATTRIBUTES                           -->
<!--                    Attributes for the <state> element         -->
<!ENTITY % state-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    STRING DATE ATTRIBUTES                     -->
<!--                    Attributes for the <string-date> element   -->
<!ENTITY % string-date-atts
            "%jats-common-atts;                                        
             iso-8601-date
                        CDATA                             #IMPLIED
             calendar   CDATA                             #IMPLIED
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    STRING NAME ATTRIBUTES                     -->
<!--                    Attribute list for the <string-name> element
                                                                   -->
<!ENTITY % string-name-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             name-style
                        (western | eastern | islensk | given-only)
                                                          'western'
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    SUFFIX ATTRIBUTES                          -->
<!--                    Attributes for the <suffix> element        -->
<!ENTITY % suffix-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    SUPPLEMENT ATTRIBUTES                      -->
<!--                    Attributes for the <supplement> element    -->
<!ENTITY % supplement-atts
            "%jats-common-atts;                                        
             supplement-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    SURNAME ATTRIBUTES                         -->
<!--                    Attribute list for the <surname> element   -->
<!ENTITY % surname-atts
            "%jats-common-atts;                                        
             initials  CDATA                              #IMPLIED"  >


<!--                    TEXTUAL FORM ATTRIBUTES                    -->
<!--                    Attributes for the <textual-form> element  -->
<!ENTITY % textual-form-atts
            "%jats-common-atts;                                        
             specific-use
                       CDATA                              #IMPLIED
             xml:lang  NMTOKEN                            #IMPLIED"  >


<!--                    TITLE ATTRIBUTES                           -->
<!--                    Attributes for the <title> element         -->
<!ENTITY % title-atts
            "%jats-common-atts;                                        
             content-type
                       CDATA                              #IMPLIED
             specific-use
                       CDATA                              #IMPLIED"  >


<!--                    TRANSLATED SUBTITLE ATTRIBUTES             -->
<!--                    Attributes for the <trans-subtitle>
                        element                                    -->
<!ENTITY % trans-subtitle-atts
            "%jats-common-atts;                                        
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    TRANSLATED TITLE ATTRIBUTES                -->
<!--                    Attribute list for the <trans-title>       -->
<!ENTITY % trans-title-atts
            "%jats-common-atts;                                        
             content-type
                       CDATA                              #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    TRANSLATED TITLE GROUP ATTRIBUTES          -->
<!--                    Attribute list for the <trans-title-group> -->
<!ENTITY % trans-title-group-atts
            "%jats-common-atts;                                        
             content-type
                       CDATA                              #IMPLIED
             specific-use
                       CDATA                              #IMPLIED
             xml:lang  NMTOKEN                            #IMPLIED"  >


<!--                    URI ATTRIBUTES                             -->
<!--                    Attributes for the <uri> element           -->
<!ENTITY % uri-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             assigning-authority                
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED
             %might-link-atts;"                                      >


<!--                    VOLUME NUMBER ATTRIBUTES                   -->
<!--                    Attribute list for the <volume> element    -->
<!ENTITY % volume-atts
            "%jats-common-atts;                                        
             seq        CDATA                             #IMPLIED
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    VOLUME IDENTIFIER ATTRIBUTES               -->
<!--                    Attributes for the <volume-id> element     -->
<!ENTITY % volume-id-atts
            "%jats-common-atts;                                        
             pub-id-type
                        CDATA                             #IMPLIED
             assigning-authority                
                        CDATA                             #IMPLIED
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED
             %might-link-atts;"                                      >


<!--                    VOLUME ISSUE GROUPING ATTRIBUTES           -->
<!--                    Attribute list for the <volume-issue-group>
                        element                                    -->
<!ENTITY % volume-issue-group-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    VOLUME SERIES ATTRIBUTES                   -->
<!--                    Attribute list for the <volume-series>
                        element                                    -->
<!ENTITY % volume-series-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    YEAR ATTRIBUTES                            -->
<!--                    Attributes for the <year> element          -->
<!ENTITY % year-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             iso-8601-date
                        CDATA                             #IMPLIED
             calendar   CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!-- ============================================================= -->
<!--                    METADATA USED BY MORE THAN ONE CLASS       -->
<!-- ============================================================= -->

<!-- ============================================================= -->
<!--                    NISO ACCESS AND INDICATOR ELEMENTS         -->
<!-- ============================================================= -->


<!--                    FREE TO READ (NISO ALI) MODEL              -->
<!--                    Content model for the ALI namespaced  
                        <ali:free_to_read> element.                -->
<!ENTITY % free-to-read-model
                        "EMPTY"                                      >


<!--                    FREE TO READ (NISO ALI)                    -->
<!--                    An EMPTY element whose presence indicates 
                        that a document or portion of a document 
                        is free to be read. 
                        Remarks: Defined in "NISO Access License and 
                        Indicators (ALI) NISO RP-22-2015".    
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=ali:free_to_read
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=ali:free_to_read
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=ali:free_to_read
                                                                   -->
<!ELEMENT  ali:free_to_read 
                        %free-to-read-model;                         >
<!ATTLIST  ali:free_to_read
             %free-to-read-atts;                                     >


<!--                    LICENSE REFERENCE (NISO ALI) MODEL         -->
<!--                    Content model for the ALI namespaced  
                        <ali:license_ref> element.                 -->
<!ENTITY % license-ref-model
                        "%anyURI;"                                   >


<!--                    LICENSE REFERENCE (NISO ALI)               -->
<!--                    A URI that is a pointer to a public license
                        or waiver. If the element has content, it 
                        must be a URI.
                        Remarks: Defined in "NISO Access License and 
                        Indicators (ALI) NISO RP-22-2015".    
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=ali:license_ref
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=ali:license_ref
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=ali:license_ref
                                                                   -->
<!ELEMENT  ali:license_ref  
                        %license-ref-model;                          >
<!ATTLIST  ali:license_ref
             %license-ref-atts;                                      >


<!-- ============================================================= -->
<!--                    CONTRIBUTOR GROUP (AUTHOR/EDITOR) ELEMENTS -->
<!-- ============================================================= -->


<!--                    CONTRIBUTOR GROUP MODEL                    -->
<!--                    Content model for the <contrib-group>
                        element                                    -->
<!ENTITY % contrib-group-model
                        "( (%contrib.class;)+,
                           (%contrib-info.class;)* )"               >


<!--                    CONTRIBUTOR GROUP                          -->
<!--                    Wrapper element for information concerning
                        a grouping of contributors, such as the
                        primary authors
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=contrib-group
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=contrib-group
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=contrib-group
                                                                   -->
<!ELEMENT  contrib-group
             %contrib-group-model;                                   >
<!ATTLIST  contrib-group
             %contrib-group-atts;                                    >


<!--                    CONTRIBUTOR MODEL                          -->
<!--                    Content model for the <contrib> element    -->
<!ENTITY % contrib-model
                        "((%contrib-id.class;)*, (%name.class;)*,
                          (%degree.class; | %contrib-info.class;)* )">


<!--                    CONTRIBUTOR                                -->
<!--                    Wrapper element to contain the information
                        about a single contributor, for example an
                        author or editor.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=contrib
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=contrib
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=contrib
                                                                   -->
<!ELEMENT  contrib      %contrib-model;                              >
<!ATTLIST  contrib
             %contrib-atts;                                          >


<!-- ============================================================= -->
<!--                    COMMON COPYRIGHT/PERMISSION ELEMENTS       -->
<!-- ============================================================= -->


<!--                    COPYRIGHT HOLDER ELEMENTS                  -->
<!--                    Elements to be mixed with data characters
                        inside the content model for the
                        <copyright-holder> element.                -->
<!ENTITY % copyright-holder-elements
                        "| %institution-wrap.class; | %subsup.class;">


<!--                    COPYRIGHT HOLDER                           -->
<!--                    Name of the organizational or personal
                        entity that holds the copyright.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=copyright-holder
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=copyright-holder
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=copyright-holder
                                                                   -->
<!ELEMENT  copyright-holder
                        (#PCDATA %copyright-holder-elements;)*       >
<!ATTLIST  copyright-holder
             %copyright-holder-atts;                                 >


<!--                    COPYRIGHT STATEMENT ELEMENTS               -->
<!--                    Content model for <copyright-statement>    -->
<!ENTITY % copyright-statement-elements
                        "| %address-link.class; | %emphasis.class; |
                         %phrase-content.class; | %subsup.class;"    >


<!--                    COPYRIGHT STATEMENT                        -->
<!--                    Copyright notice or statement, suitable for
                        printing or display. Within the statement the
                        copyright year should be identified, if
                        expected to be displayed.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=copyright-statement
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=copyright-statement
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=copyright-statement
                                                                   -->
<!ELEMENT  copyright-statement
                        (#PCDATA %copyright-statement-elements;)*    >
<!ATTLIST  copyright-statement
             %copyright-statement-atts;                              >


<!--                    COPYRIGHT YEAR                             -->
<!--                    Year of the copyright. Need not be used, if,
                        for example, having the year as part of the
                        copyright statement is sufficient.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=copyright-year
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=copyright-year
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=copyright-year
                                                                   -->
<!ELEMENT  copyright-year
                        (#PCDATA)                                    >
<!ATTLIST  copyright-year
             %copyright-year-atts;                                   >


<!--                    LICENSE MODEL                              -->
<!--                    Content model for an <license> element     -->
<!ENTITY % license-model
                        "((%license-p.class;)+)"                     >


<!--                    LICENSE INFORMATION                        -->
<!--                    The set of conditions under which people are
                        allowed to use this article or other
                        license-related information or restrictions.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=license
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=license
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=license
                                                                   -->
<!ELEMENT  license      %license-model;                              >
<!ATTLIST  license
             %license-atts;                                          >


<!--                    LICENSE PARAGRAPH ELEMENTS                 -->
<!--                    Elements that can be included with the text
                        inside a <license-p> element.
                        Design Note: All inline mixes begin with an
                        OR bar, but since %p-elements; is an
                        inline mix, the OR bar is already there.   -->
<!ENTITY % license-p-elements
                        "%p-elements; | %price.class;"               >


<!--                    LICENSE PARAGRAPH                          -->
<!--                    Paragraphs of text within the description of
                        a <license>. Not defined as an ordinary
                        paragraph, so that it can have special
                        content.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=license-p
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=license-p
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=license-p
                                                                   -->
<!ELEMENT  license-p    (#PCDATA %license-p-elements;)*              >
<!ATTLIST  license-p
             %license-p-atts;                                        >


<!--                    PERMISSIONS MODEL                          -->
<!--                    Model for <permissions> wrapper element    -->
<!ENTITY % permissions-model
                        "(copyright-statement*, copyright-year*,
                          copyright-holder*, (%license.class;)*)"    >


<!--                    PERMISSIONS                                -->
<!--                    Wrapper element to hold the copyright
                        information, license material, and any
                        future similar metadata.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=permissions
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=permissions
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=permissions
                                                                   -->
<!ELEMENT  permissions  %permissions-model;                          >
<!ATTLIST  permissions
             %permissions-atts;                                      >


<!-- ============================================================= -->
<!--                    COMMON METADATA/BIBLIOGRAPHIC ELEMENTS     -->
<!-- ============================================================= -->


<!--                    ARTICLE TITLE ELEMENTS                     -->
<!--                    Elements that can be included with the text
                        inside an <article-title> element.
                        Design Note: All inline mixes begin with an
                        OR bar, but since %simple-phrase; is an
                        inline mix, the OR bar is already there.   -->
<!ENTITY % article-title-elements
                        "%simple-phrase; | %break.class;"            >


<!--                    ARTICLE TITLE                              -->
<!--                    The title of the article in the language
                        in which the article was originally
                        published
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=article-title
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=article-title
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=article-title
                                                                   -->
<!ELEMENT  article-title
                        (#PCDATA %article-title-elements;)*          >
<!ATTLIST  article-title
             %article-title-atts;                                    >


<!--                    AFFILIATION ELEMENTS                       -->
<!--                    Elements for use in the <aff> element      -->
<!ENTITY % aff-elements "| %address.class; | %address-link.class; |
                         %article-link.class; | %break.class; |
                         %emphasis.class; | %label.class; |
                         %simple-link.class; | %subsup.class;"       >


<!--                    AFFILIATION                                -->
<!--                    Name of a institution or organization such as
                        a university or corporation.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=aff
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=aff
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=aff
                                                                   -->
<!ELEMENT  aff          (#PCDATA %aff-elements;)*                    >
<!ATTLIST  aff
             %aff-atts;                                              >


<!--                    AFFILIATION ALTERNATIVES MODEL             -->
<!--                    Content model for the element
                        <aff-alternatives>                         -->
<!ENTITY % aff-alternatives-model
                        "(aff+)"                                     >


<!--                    AFFILIATION ALTERNATIVES                   -->
<!--                    Container element to hold one or more
                        representations of a single affiliation, for
                        example the name of an institution in two
                        languages or two scripts.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=aff-alternatives
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=aff-alternatives
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=aff-alternatives
                                                                   -->
<!ELEMENT  aff-alternatives
                        %aff-alternatives-model;                     >
<!ATTLIST  aff-alternatives
             %aff-alternatives-atts;                                 >


<!--                    CONFERENCE DATE ELEMENTS                   -->
<!--                    Elements for use in the <conf-date> element-->
<!ENTITY % conf-date-elements
                        ""                                           >


<!--                    CONFERENCE DATE                            -->
<!--                    The date(s) on which the conference was held.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=conf-date
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=conf-date
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=conf-date
                                                                   -->
<!ELEMENT  conf-date    (#PCDATA %conf-date-elements;)*              >
<!ATTLIST  conf-date
             %conf-date-atts;                                        >


<!--                    CONFERENCE LOCATION ELEMENTS               -->
<!--                    Elements for use in the <conf-loc> element
                        Design Note: All inline mixes begin with an
                        OR bar, but since %simple-text; is an inline
                        mix, the OR bar is already there.          -->
<!ENTITY % conf-loc-elements
                        "%simple-text; | %address.class;"            >


<!--                    CONFERENCE LOCATION                        -->
<!--                    The physical location(s) of the conference.
                        This may include a city, a country, or a
                        campus or organization location if that is
                        the only location available.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=conf-loc
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=conf-loc
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=conf-loc
                                                                   -->
<!ELEMENT  conf-loc     (#PCDATA %conf-loc-elements;)*               >
<!ATTLIST  conf-loc
             %conf-loc-atts;                                         >


<!--                    CONFERENCE NAME ELEMENTS                   -->
<!--                    Elements for use in the <conf-name> element.
                        Design Note: All inline mixes begin with an
                        OR bar, but since %simple-text; is an inline
                        mix, the OR bar is already there.          -->
<!ENTITY % conf-name-elements
                        "%simple-text;"                            >


<!--                    CONFERENCE NAME                            -->
<!--                    The full name of the conference, including any
                        qualifiers such as "43rd Annual".
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=conf-name
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=conf-name
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=conf-name
                                                                   -->
<!ELEMENT  conf-name    (#PCDATA %conf-name-elements;)*              >
<!ATTLIST  conf-name
             %conf-name-atts;                                        >


<!--                    CONFERENCE SPONSOR  ELEMENTS               -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the conference sponsor.
                        Design Note: All inline mixes begin with an
                        OR bar, but since %simple-text; is an inline
                        mix, the OR bar is already there.          -->
<!ENTITY % conf-sponsor-elements
                        "%simple-text; | %institution-wrap.class;"   >


<!--                    CONFERENCE SPONSOR                         -->
<!--                    One organization that sponsored the
                        conference. If more than one organization
                        sponsored the conference, multiple
                        <conf-sponsor> elements should be used.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=conf-sponsor
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=conf-sponsor
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=conf-sponsor
                                                                   -->
<!ELEMENT  conf-sponsor (#PCDATA %conf-sponsor-elements;)*           >
<!ATTLIST  conf-sponsor
             %conf-sponsor-atts;                                     >


<!--                    OBJECT IDENTIFIER                          -->
<!--                    Used to record an identifier such as a DOI
                        for an interior element such as an <abstract>
                        or <figure>.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=object-id
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=object-id
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=object-id
                                                                   -->
<!ELEMENT  object-id    (#PCDATA)                                    >
<!ATTLIST  object-id
             %object-id-atts;                                        >


<!--                    ISBN ELEMENTS                              -->
<!--                    Elements for use with data characters inside
                        the model for the <isbn> element           -->
<!ENTITY % isbn-elements
                        ""                                           >


<!--                    ISBN                                       -->
<!--                    International Standard Book Number
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=isbn
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=isbn
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=isbn
                                                                   -->
<!ELEMENT  isbn         (#PCDATA %isbn-elements;)*                   >
<!ATTLIST  isbn
             %isbn-atts;                                             >


<!--                    ISSN ELEMENTS                              -->
<!--                    Elements for use with data characters inside
                        the model for the <issue> element          -->
<!ENTITY % issn-elements
                        ""                                           >


<!--                    ISSN                                       -->
<!--                    International Standard Serial Number
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=issn
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=issn
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=issn
                                                                   -->
<!ELEMENT  issn         (#PCDATA %issn-elements;)*                   >
<!ATTLIST  issn
             %issn-atts;                                             >


<!--                    ISSN-L ELEMENTS                            -->
<!--                    Elements for use with data characters inside
                        the model for the <issn-l> element         -->
<!ENTITY % issn-l-elements
                        ""                                           >


<!--                    ISSN LINKING                               -->
<!--                    International Standard Linking Serial Number
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=issn-l
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=issn-l
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=issn-l
                                                                   -->
<!ELEMENT  issn-l       (#PCDATA %issn-l-elements;)*                 >
<!ATTLIST  issn-l
             %issn-l-atts;                                           >


<!--                    ISSUE ELEMENTS                             -->
<!--                    Elements for use with data characters inside
                        the model for the <issue> element          -->
<!ENTITY % issue-elements
                        "%just-rendition;"                           >


<!--                    ISSUE NUMBER                               -->
<!--                    The issue number, issue name, or other
                        identifier of an issue of a journal that
                        is displayed or printed with the issue.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=issue
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=issue
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=issue
                                                                   -->
<!ELEMENT  issue        (#PCDATA %issue-elements;)*                  >
<!ATTLIST  issue
             %issue-atts;                                            >


<!--                    ISSUE IDENTIFIER                           -->
<!--                    Used to record an identifier such as a DOI
                        that describes an entire issue of a
                        journal
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=issue-id
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=issue-id
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=issue-id
                                                                   -->
<!ELEMENT  issue-id     (#PCDATA)                                    >
<!ATTLIST  issue-id
             %issue-id-atts;                                         >


<!--                    ISSUE PART ELEMENTS                        -->
<!--                    Elements that can be added to the text
                        within the element <issue-part>            -->
<!ENTITY % issue-part-elements
                        "%just-rendition;"                           >


<!--                    ISSUE PART                         -->
<!--                    A publisher may split an issue into two or
                        more separately bound or separately issued
                        parts. This element holds the identifiers
                        (titles, part numbers, etc.) for those
                        publishing components.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=issue-part
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=issue-part
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=issue-part
                                                                   -->
<!ELEMENT  issue-part   (#PCDATA %issue-part-elements;)*             >
<!ATTLIST  issue-part
             %issue-part-atts;                                       >


<!--                    ISSUE SPONSOR ELEMENTS                     -->
<!--                    Elements for use in the <issue-sponsor>
                        element                                    -->
<!ENTITY % issue-sponsor-elements
                        "%just-rendition;"                           >


<!--                    ISSUE TITLE                                -->
<!--                    Used to record the sponsor for an issue of
                        the journal
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=issue-sponsor
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=issue-sponsor
                                                                   -->
<!ELEMENT  issue-sponsor
                        (#PCDATA %issue-sponsor-elements;)*          >
<!ATTLIST  issue-sponsor
             %issue-sponsor-atts;                                    >


<!--                    ISSUE SUBTITLE ELEMENTS                    -->
<!--                    Elements for use in the <issue-subtitle>
                        element                                    -->
<!ENTITY % issue-subtitle-elements
                        "%just-rendition;"                           >

<!--                    ISSUE SUBTITLE                             -->
<!--                    Used to record one or more subtitles for
                        an issue
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=issue-subtitle
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=issue-subtitle
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=issue-subtitle
                                                                   -->
<!ELEMENT  issue-subtitle  
                        (#PCDATA %issue-subtitle-elements;)*         >
<!ATTLIST  issue-subtitle
             %issue-subtitle-atts;                                   >


<!--                    ISSUE TITLE ELEMENTS                       -->
<!--                    Elements for use in the <issue-title> element
                                                                   -->
<!ENTITY % issue-title-elements
                        "%just-rendition;"                           >


<!--                    ISSUE TITLE                                -->
<!--                    Used to record the theme or special issue
                        title for an issue of the journal
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=issue-title
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=issue-title
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=issue-title
                                                                   -->
<!ELEMENT  issue-title  (#PCDATA %issue-title-elements;)*            >
<!ATTLIST  issue-title
             %issue-title-atts;                                      >


<!--                    JOURNAL IDENTIFIER                         -->
<!--                    Short code that represents the journal; used
                        as an alternative to or short form of the
                        journal title; used for identification of
                        the journal domain.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=journal-id
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=journal-id
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=journal-id
-->
<!ELEMENT  journal-id   (#PCDATA)                                    >
<!ATTLIST  journal-id
             %journal-id-atts;                                       >


<!--                    ROLE ELEMENTS                              -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        a <role>
                        Design Note: All inline mixes begin with an
                        OR bar; since %rendition-plus; is an
                        inline mix, the OR bar is already there.   -->
<!ENTITY % role-elements
                        "%rendition-plus;"                           >


<!--                    ROLE OR FUNCTION TITLE OF CONTRIBUTOR      -->
<!--                    A title or the role of a contributor
                        (such as an author) in this work. For example,
                        Editor-in-Chief, Contributor, Chief
                        Scientist, Photographer, Research Associate,
                        etc.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=role
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=role
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=role
                                                                   -->
<!ELEMENT  role         (#PCDATA %role-elements;)*                   >
<!ATTLIST  role
             %role-atts;                                             >


<!--                    TRANSLATED TITLE GROUP MODEL               -->
<!--                    Content model for the element
                        <trans-title-group>                        -->
<!ENTITY % trans-title-group-model
                        "(trans-title, trans-subtitle*)"             >


<!--                    TRANSLATED TITLE GROUP                     -->
<!--                    Container element for all translated, and
                        transliterated journal titles.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=trans-title-group
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=trans-title-group
                                                                   -->
<!ELEMENT  trans-title-group
                        %trans-title-group-model;                    >
<!ATTLIST  trans-title-group
             %trans-title-group-atts;                                >


<!--                    TRANSLATED SUBTITLE                        -->
<!--                    An alternate version of an article subtitle
                        that has been translated out of the original
                        language of the article subtitle <subtitle>
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=trans-subtitle
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=trans-subtitle
                                                                   -->
<!ELEMENT  trans-subtitle
                        (#PCDATA %trans-subtitle-elements;)*         >
<!ATTLIST  trans-subtitle
             %trans-subtitle-atts;                                   >


<!--                    TRANSLATED TITLE                           -->
<!--                    An alternate version of the title that has
                        been translated into a language other than
                        that of the original article title
                        <article-title>
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=trans-title
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=trans-title
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=trans-title
                                                                   -->
<!ELEMENT  trans-title  (#PCDATA %trans-title-elements;)*            >
<!ATTLIST  trans-title
            %trans-title-atts;                                       >


<!--                    VOLUME NUMBER ELEMENTS                     -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        a <volume>                                 -->
<!ENTITY % volume-elements
                        "%just-rendition; "                          >


<!--                    VOLUME NUMBER                              -->
<!--                    NEW DEFINITION FOR RELEASE 2.0:
                        The volume number, volume name, or other
                        identifier of an volume of a journal that
                        is displayed or printed with the volume.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=volume
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=volume
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=volume
                                                                   -->
<!ELEMENT  volume       (#PCDATA %volume-elements;)*                 >
<!ATTLIST  volume
            %volume-atts;                                            >


<!--                    VOLUME IDENTIFIER ELEMENTS                 -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        a <volume-id>                              -->
<!ENTITY % volume-id-elements
                        "%just-rendition; "                          >


<!--                    VOLUME IDENTIFIER                          -->
<!--                    Used to record an identifier such as a DOI
                        that describes an entire volume of a
                        journal.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=volume-id
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=volume-id
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=volume-id
                                                                   -->
<!ELEMENT  volume-id    (#PCDATA %volume-id-elements;)*              >
<!ATTLIST  volume-id
             %volume-id-atts;                                        >


<!--                    VOLUME SERIES ELEMENTS                     -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        a <volume>                                 -->
<!ENTITY % volume-series-elements
                        "%just-rendition; "                          >


<!--                    VOLUME SERIES                              -->
<!--                    This is a rare metadata element, intended to
                        hold the series number, in those odd cases
                        where, for example, a Publisher has reissued
                        a journal, restarting the volume numbers
                        with "1", so duplicate volume numbers
                        would exist and need to be differentiated.
                        Such a publisher typically adds a series
                        number to their volume numbers, and
                        this element has been created to hold such
                        a series number.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=volume-series
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=volume-series
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=volume-series
                                                                   -->
<!ELEMENT  volume-series
                        (#PCDATA %volume-series-elements;)*          >
<!ATTLIST  volume-series
            %volume-series-atts;                                     >


<!--                    VOLUME ISSUE GROUP                         -->
<!--                    Content model for the element
                        <volume-issue-group>.                      -->
<!ENTITY % volume-issue-group-model
                        "(volume*, volume-id*, volume-series?,
                         issue*, issue-id*, 
                         issue-title*, issue-title-group*, 
                         issue-sponsor*, issue-part?)"               >


<!--                    TRANSLATED TITLE GROUP                     -->
<!--                    Container element to hold together related
                        volume and issue elements.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=volume-issue-group
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=volume-issue-group
                                                                   -->
<!ELEMENT  volume-issue-group
                        %volume-issue-group-model;                   >
<!ATTLIST  volume-issue-group
             %volume-issue-group-atts;                               >


<!-- ============================================================= -->
<!--                    COMMON ARTICLE METADATA/BIBLIOGRAPHIC      -->
<!--                    CONTRIBUTOR IDENTIFICATION ELEMENTS        -->
<!-- ============================================================= -->


<!--                    ANONYMOUS ELEMENTS                         -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        an <anonymous> element                     -->
<!ENTITY % anonymous-elements
                        "%just-rendition;"                           >


<!--                    ANONYMOUS CONTENT MODEL                    -->
<!--                    The content model for the <anonymous> 
                        element                                    -->
<!ENTITY % anonymous-model
                        "(#PCDATA %anonymous-elements;)*"            >


<!--                    ANONYMOUS                                  -->
<!--                    Place holder for the name of a contributor
                        whose name is unknown or not disclosed.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=anonymous
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=anonymous
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=anonymous
                                                                   -->
<!ELEMENT  anonymous    %anonymous-model;                            >
<!ATTLIST  anonymous
             %anonymous-atts;                                        >


<!--                    ET AL ELEMENTS                             -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        an <etal> element                          -->
<!ENTITY % etal-elements
                        "%just-rendition;"                           >


<!--                    ET AL CONTENT MODEL                        -->
<!--                    The content model for the <etal> element   -->
<!ENTITY % etal-model   "(#PCDATA %etal-elements;)*"                 >


<!--                    ET AL                                      -->
<!--                    Most journals model this as an EMPTY element,
                        typically used to generate the text "et al."
                        from a stylesheet. However, a few journal
                        DTDs (Blackwell's, for example) expect
                        content for this element, with such text as
                        "Associates, coworkers, and colleagues".
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=etal
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=etal
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=etal
                                                                   -->
<!ELEMENT  etal         %etal-model;                                 >
<!ATTLIST  etal
             %etal-atts;                                             >


<!-- ============================================================= -->
<!--                    COMMON ARTICLE METADATA/BIBLIOGRAPHIC      -->
<!--                    PUBLISHER IDENTIFICATION ELEMENTS          -->
<!-- ============================================================= -->


<!--                    ON BEHALF OF CONTENT ELEMENTS              -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        <on-behalf-of>
                        Design Note: -%rendition-plus; begins with
                        an OR bar, so this inline mix beguines with
                        an OR bar.                                 -->
<!ENTITY % on-behalf-of-elements
                        "%rendition-plus; | %institution-wrap.class;"
                                                                     >

<!--                    ON BEHALF OF                               -->
<!--                    When a contributor has written or edited
                        a work  "on-behalf-of" an organization or
                        group the contributor is acting as a
                        representative of the organization, which
                        may or may not be his/her usual affiliation.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=on-behalf-of
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=on-behalf-of
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=on-behalf-of
                                                                   -->
<!ELEMENT  on-behalf-of (#PCDATA %on-behalf-of-elements;)*           >
<!ATTLIST  on-behalf-of
             %on-behalf-of-atts;                                     >


<!--                    PUBLISHER CONTENT MODEL                    -->
<!--                    The content model for the <publisher>
                        element                                    -->
<!ENTITY % publisher-model
                        "((publisher-name, publisher-loc?)+)"        >


<!--                    PUBLISHER                                  -->
<!--                    Who published the work
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=publisher
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=publisher
                                                                   -->
<!ELEMENT  publisher    %publisher-model;                            >
<!ATTLIST  publisher
             %publisher-atts;                                        >


<!--                    PUBLISHER'S NAME ELEMENTS                  -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        a <publisher-name>
                        Design Note: All inline mixes begin with an
                        OR bar; since %just-rendition; is an
                        inline mix, the OR bar is already there    -->
<!ENTITY % publisher-name-elements
                        "%just-rendition;| %institution-wrap.class;" >


<!--                    PUBLISHER'S NAME                           -->
<!--                    Name of the publisher of the work
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=publisher-name
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=publisher-name
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=publisher-name
                                                                   -->
<!ELEMENT  publisher-name
                        (#PCDATA %publisher-name-elements;)*         >
<!ATTLIST  publisher-name
             %publisher-name-atts;                                   >


<!--                    PUBLISHER'S LOCATION ELEMENTS              -->
<!--                    Elements for use in the Publisher Location
                        <publisher-loc> element                    -->
<!ENTITY % publisher-loc-elements
                        "| %address.class; | %address-link.class; |
                         %emphasis.class; | %subsup.class;"          >


<!--                    PUBLISHER'S LOCATION                       -->
<!--                    Place of publication, usually a city such
                        as New York or London
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=publisher-loc
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=publisher-loc
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=publisher-loc
                                                                   -->
<!ELEMENT  publisher-loc
                        (#PCDATA %publisher-loc-elements;)*          >
<!ATTLIST  publisher-loc
             %publisher-loc-atts;                                    >


<!-- ============================================================= -->
<!--                    COMMON METADATA ELEMENTS CONTINUED         -->
<!--                    PAGE NUMBERING (SIZE) ELEMENTS             -->
<!-- ============================================================= -->


<!--                    FIRST PAGE                                 -->
<!--                    The page number on which the article starts,
                        for print journals that have page numbers
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=fpage
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=fpage
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=fpage
                                                                   -->
<!ELEMENT  fpage        (#PCDATA)                                    >
<!ATTLIST  fpage
             %fpage-atts;                                            >


<!--                    LAST PAGE                                  -->
<!--                    The page number on which the article ends,
                        for print journals that have page numbers
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=lpage
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=lpage
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=lpage
                                                                   -->
<!ELEMENT  lpage        (#PCDATA)                                    >
<!ATTLIST  lpage
             %lpage-atts;                                            >


<!--                    PAGE RANGES                                -->
<!--                    A container element for additional page
                        information (TO BE USED TO SUPPLEMENT AND
                        NOT TO REPLACE <fpage> and <lpage>) to record
                        discontinuous pages ranges such as
                            "8-11, 14-19, 40"
                        meaning that the article begins on page
                        8, runs 8 through 11, skips to pages 14
                        through 19, and concludes on page 40.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=page-range
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=page-range
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=page-range
                                                                   -->
<!ELEMENT  page-range   (#PCDATA)                                    >
<!ATTLIST  page-range
             %page-range-atts;                                       >


<!--                    SIZE ELEMENTS                              -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the size element.                          -->
<!ENTITY % size-elements
                        ""                                           >


<!--                    SIZE                                       -->
<!--                    The size (such as running time, page count,
                        or physical measurements) of the object being
                        described, usually by a <product> element.
                        The "units" attribute must be used to name
                        the unit of measure (pages, minutes, hours,
                        linear feet, etc.).
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=size
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=size
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=size
                                                                   -->
<!ELEMENT  size         (#PCDATA %size-elements;)*                   >
<!ATTLIST  size
             %size-atts;                                             >


<!--                    ELECTRONIC LOCATION IDENTIFIER             -->
<!--                    Used to identify an article that
                        does not have traditional page numbers.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=elocation-id
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=elocation-id
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=elocation-id
                                                                   -->
<!ELEMENT  elocation-id (#PCDATA)                                    >
<!ATTLIST  elocation-id
             %elocation-id-atts;                                     >


<!-- ============================================================= -->
<!--                    CITATIONS (BIBLIOGRAPHIC REFERENCE)        -->
<!-- ============================================================= -->


<!--                    CITATION ALTERNATIVES MODEL                -->
<!--                    Model for the <citation-alternatives>
                        element.                                   -->
<!ENTITY % citation-alternatives-model
                        "( (%id.class;)*,
                           (%citation-minus-alt.class;)+ )"          >


<!--                    CITATION ALTERNATIVES                      -->
<!--                    This element will hold alternative versions of 
                        one citation, for example, a single citation 
                        in multiple languages or a single citation 
                        tagged as both a <mixed-citation> complete 
                        with punctuation and spacing and as an
                        <element-citation> with punctuation and 
                        spacing removed.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=citation-alternativesv
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=citation-alternatives
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=citation-alternatives
                                                                   -->
<!ELEMENT  citation-alternatives
                        %citation-alternatives-model;                >
<!ATTLIST  citation-alternatives
             %citation-alternatives-atts;                            >


<!--                    CITATION ELEMENTS                          -->
<!--                    Content for both types of citation. These
                        elements may be mixed with #PCDATA in the
                        <mixed-citation> element (in which all
                        punctuation and spacing are left intact), and
                        they also constitute the choices that can be
                        used to form the all-element-content of the
                        <element-citation> element (in which
                        punctuation and spacing are removed).
                        Design Note: All inline mixes begin with an
                        OR bar.                                    -->
<!ENTITY % citation-elements
                        "%emphasis.class; | %inline-display.class; |
                         %inline-math.class; | %label.class; |
                         %phrase.class; | %references.class; |
                         %subsup.class;"                             >


<!--                    MIXED CITATION                             -->
<!--                    A citation is a description of a work, such
                        as a journal article, book, or personal
                        communication, that is cited in the text of
                        the article. This citation element is
                        completely loose, with text, punctuation,
                        spacing, and any of the citation elements
                        in any order.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=mixed-citation
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=mixed-citation
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=mixed-citation
                                                                   -->
<!ELEMENT  mixed-citation
                        (#PCDATA | %citation-elements;)*             >
<!ATTLIST  mixed-citation
             %mixed-citation-atts;                                   >


<!--                    ELEMENT CITATION                           -->
<!--                    A citation is a description of a work, such
                        as a journal article, book, or personal
                        communication, that is cited in the text of
                        the article. This citation model contains
                        element-only content, with elements in
                        any order as many times as needed. This
                        citation is intended for use in capturing
                        a publisher's specific element order.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=element-citation
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=element-citation
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=element-citation
                                                                   -->
<!ELEMENT  element-citation
                        (%citation-elements;)+                       >
<!ATTLIST  element-citation
             %element-citation-atts;                                 >


<!-- ============================================================= -->
<!--                    ADDRESS ELEMENTS                           -->
<!-- ============================================================= -->


<!--                    ADDRESS MODEL                              -->
<!--                    Content model for the <address> element    -->
<!ENTITY % address-model
                        "(%address.class; | %address-link.class;)*"  >


<!--                    ADDRESS/CONTACT INFORMATION                -->
<!--                    Wrapper element for contact information such
                        as address, phone, fax, email, url, country,
                        etc.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=address
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=address
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=address
                                                                   -->
<!ELEMENT  address      %address-model;                              >
<!ATTLIST  address
             %address-atts;                                          >


<!--                    ADDRESS LINE ELEMENTS                      -->
<!--                    Elements for use in the <addr-line> element-->
<!ENTITY % addr-line-elements
                        "%simple-text; | %address-line.class;"       >


<!--                    ADDRESS LINE                               -->
<!--                    One line in an address                     -->
<!--                    Conversion Note: If the address is
                        undifferentiated data characters, the entire
                        address may be inside one of these elements.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=addr-line
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=addr-line
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=addr-line
                                                                   -->
<!ELEMENT  addr-line    (#PCDATA %addr-line-elements;)*              >
<!ATTLIST  addr-line
             %addr-line-atts;                                        >


<!--                    CITY ELEMENTS                              -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the city element.                          -->
<!ENTITY % city-elements
                        ""                                           >


<!--                    CITY: IN AN ADDRESS                        -->
<!--                    The name of a city.                   
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=city
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=city
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=city
                                                                   -->
<!ELEMENT  city         (#PCDATA %city-elements;)*                   >
<!ATTLIST  city
             %city-atts;                                             >


<!--                    COUNTRY ELEMENTS                           -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the country element.                       -->
<!ENTITY % country-elements
                        ""                                           >


<!--                    COUNTRY: IN AN ADDRESS                     -->
<!--                    The name of a country.                    
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=country
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=country
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=country
                                                                   -->
<!ELEMENT  country      (#PCDATA %country-elements;)*                >
<!ATTLIST  country
             %country-atts;                                          >


<!--                    EMAIL ADDRESS ELEMENTS                     -->
<!--                    Elements to be mixed with #PCDATA inside the
                        <email> element                            -->
<!ENTITY % email-elements
                        ""                                           >


<!--                    EMAIL ADDRESS                              -->
<!--                    The email address of a person or institution.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=email
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=email
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=email
                                                                   -->
<!ELEMENT  email        (#PCDATA %email-elements;)*                  >
<!ATTLIST  email
             %email-atts;                                            >


<!--                    FAX NUMBER ELEMENTS                        -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        a <fax>                                    -->
<!ENTITY % fax-elements
                        "%just-rendition; "                          >


<!--                    FAX NUMBER: IN AN ADDRESS                  -->
<!--                    The number used to send faxes.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=fax
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=fax
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=fax
                                                                   -->
<!ELEMENT  fax          (#PCDATA %fax-elements;)*                    >
<!ATTLIST  fax
             %fax-atts;                                              >


<!--                    INSTITUTION NAME ELEMENTS                  -->
<!--                    Elements for use in the <institution>
                        element                                    -->
<!ENTITY % institution-elements
                        "| %break.class; | %emphasis.class; |
                         %subsup.class;"                             >


<!--                    INSTITUTION NAME: IN AN ADDRESS            -->
<!--                    Name of a institution or organization such as
                        a university or corporation used in an
                        address or within a citation (such as a
                        <mixed-citation> or an <element-citation>)
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=institution
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=institution
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=institution
                                                                   -->
<!ELEMENT  institution  (#PCDATA %institution-elements;)*            >
<!ATTLIST  institution
             %institution-atts;                                      >


<!--                    INSTITUTION IDENTIFIER MODEL               -->
<!--                    Content model for the <institution-id>
                        element                                    -->
<!ENTITY % institution-id-model
                        "(#PCDATA)"                                  >


<!--                    INSTITUTION IDENTIFIER                     -->
<!--                    Contains an institutional identifier,
                        whether publisher-specific (for example,  
                        "AIP") or from an established identifying
                        authority (for example,"Ringgold" or "ISNI").
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=institution-id
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=institution-id
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=institution-id
                                                                   -->
<!ELEMENT  institution-id  
                        %institution-id-model;                       >
<!ATTLIST  institution-id
             %institution-id-atts;                                   >


<!--                    INSTITUTION WRAPPER MODEL                  -->
<!--                    Content model for the <institution-wrap>
                        element                                    -->
<!ENTITY % institution-wrap-model
                        "(%institution.class;)*"                     >


<!--                    INSTITUTION WRAPPER                        -->
<!--                    A container element to hold the name of an
                        institution <institution> and any formal
                        identifiers for that institution 
                        <institution-id>), for example, a INSI or
                        Ringgold ID.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=institution-wrap
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=institution-wrap
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=institution-wrap
                                                                   -->
<!ELEMENT  institution-wrap  
                        %institution-wrap-model;                     >
<!ATTLIST  institution-wrap
             %institution-wrap-atts;                                 >


<!--                    PHONE NUMBER ELEMENTS                      -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        a <phone number>
                        Design Note: All inline mixes begin with an
                        OR bar, but since %just-rendition; is an
                        inline mix, the OR bar is already there.   -->
<!ENTITY % phone-elements
                        "%just-rendition;"                           >


<!--                    PHONE NUMBER: IN AN ADDRESS                -->
<!--                    A callable phone number in some telephone or
                        wireless system somewhere in the world.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=phone
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=phone
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=phone
                                                                   -->
<!ELEMENT  phone        (#PCDATA %phone-elements;)*                  >
<!ATTLIST  phone
             %phone-atts;                                            >


<!--                    POSTAL CODE ELEMENTS                       -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the postal-code element.                   -->
<!ENTITY % postal-code-elements
                        ""                                           >


<!--                    POSTAL CODE: IN AN ADDRESS                 -->
<!--                    A postal number such as a zip-code or postal
                        code used to address physical mail.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=postal-code
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=postal-code
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=postal-code
                                                                   -->
<!ELEMENT  postal-code  (#PCDATA %postal-code-elements;)*            >
<!ATTLIST  postal-code
             %postal-code-atts;                                      >


<!--                    STATE ELEMENTS                             -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the state element.                         -->
<!ENTITY % state-elements
                        ""                                           >


<!--                    STATE or PROVINCE: IN AN ADDRESS           -->
<!--                    The name of a state, province, territory or
                        other political unit used in an address. This
                        is a lower level subdivision than a country,
                        but higher than a city, county, or parish. 
                        The names for such a unit vary geopolitically.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=state
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=state
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=state
                                                                   -->
<!ELEMENT  state        (#PCDATA %state-elements;)*                  >
<!ATTLIST  state
             %state-atts;                                            >


<!--                    URI ELEMENTS                               -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        a <uri>
                        Design Note: This PE begins with an OR
                        bar because %just-rendition; begins with an
                        OR bar.                                    -->
<!ENTITY % uri-elements
                        "%just-rendition;"                           >


<!--                    URI                                        -->
<!--                    URI such as a URL that may be used as a
                        live link, typically naming a web site, such
                        as:
                           <url>http://www.mulberrytech.com</url>
                        Alternatively the element content may name
                        the URL, e.g., "Mulberry's Website" and the
                        "xlink:href" attribute may hold the real
                        URL.
                           <url xlink:href="http://www.mulberrytech.
                           com">Mulberry's Website</url>
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=uri
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=uri
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=uri
                                                                   -->
<!ELEMENT  uri          (#PCDATA %uri-elements;)*                    >
<!ATTLIST  uri
             %uri-atts;                                              >


<!-- ============================================================= -->
<!--                    SUPPLEMENT ELEMENTS                        -->
<!-- ============================================================= -->


<!--                    SUPPLEMENT ELEMENTS                        -->
<!--                    Elements for use in the <supplement> element
                        Design Note: All inline mixes begin with an
                        OR bar, but since %simple-text; is an inline
                        mix, the OR bar is already there.          -->
<!ENTITY % supplement-elements
                        "%simple-text; | %contrib-group.class; |
                         %title.class;"                              >


<!--                    SUPPLEMENT                                 -->
<!--                    For a journal published as a supplement, this
                        is a container element for all the provided
                        supplement information, such as additional
                        identification numbers, titles, authors, and
                        supplement series information.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=supplement
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=supplement
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=supplement
                                                                   -->
<!ELEMENT  supplement   (#PCDATA %supplement-elements;)*             >
<!ATTLIST  supplement
            %supplement-atts;                                        >


<!-- ============================================================= -->
<!--                    DATE ELEMENTS (PUBLICATION HISTORY)        -->
<!-- ============================================================= -->


<!--                    DATE                                       -->
<!--                    The elements <day>, <month>, and <year> should
                        ALWAYS be numeric values. The date may be
                        represented as a string in <string-date>, but
                        the numeric values should be present whenever
                        possible.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=date
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=date
                                                                   -->
<!ELEMENT  date         %date-model;                                 >
<!ATTLIST  date
              %date-atts;                                            >


<!--                    DAY                                        -->
<!--                    The numeric value of a day of the month, used
                        in both article metadata and inside a citation,
                        in two digits as it would be stated in the "DD"
                        in an international date format YYYY-MM-DD, for
                        example "03", "25".
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=day
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=day
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=day
                                                                   -->
<!ELEMENT  day          (#PCDATA)                                    >
<!ATTLIST  day
             %day-atts;                                              >


<!--                    MONTH                                      -->
<!--                    Names one of the months of the year. Used in
                        both article metadata and inside a citation,
                        this element may contain a full month
                        "December", an abbreviation "Dec", or,
                        preferably, a numeric month such as "12".
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=month
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=month
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=month
                                                                   -->
<!ELEMENT  month        (#PCDATA)                                    >
<!ATTLIST  month
             %month-atts;                                            >


<!--                    SEASON                                     -->
<!--                    Season of publication, such as "Spring".
                        Used in both article metadata and inside a
                        citation (such as a <mixed-citation> or an
                        <element-citation>)
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=season
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=season
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=season
                                                                   -->
<!ELEMENT  season       (#PCDATA)                                    >
<!ATTLIST  season
             %season-atts;                                           >


<!--                    ERA                                        -->
<!--                    Era of publication. Used in certain Japanese
                        and Korean dates.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=era
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=era
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=era
                                                                   -->
<!ELEMENT  era         (#PCDATA)                                     >
<!ATTLIST  era
             %era-atts;                                              >


<!--                    YEAR                                       -->
<!--                    Year of publication, which should be expressed
                        as a 4-digit number: "1776" or "1924"
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=year
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=year
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=year
                                                                   -->
<!ELEMENT  year         (#PCDATA)                                    >
<!ATTLIST  year
             %year-atts;                                             >


<!--                    STRING DATE ELEMENTS                       -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the <string-date> element                  -->
<!ENTITY % string-date-elements
                        " | %date-parts.class;"                      >


<!--                    DATE AS A STRING                           -->
<!--                    This is a representation of the date as a
                        string; usually used for dates for which
                        months and years are not given, but may be
                        used for any date as a string (i.e., "January,
                        2001" "Fall 2001" "March 11, 2001").
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=string-date
                                                                   -->
<!ELEMENT  string-date  (#PCDATA %string-date-elements;)*            >
<!ATTLIST  string-date
             %string-date-atts;                                      >


<!-- ============================================================= -->
<!--                    GROUP OR CORPORATE AUTHOR ELEMENTS         -->
<!-- ============================================================= -->


<!--                    COLLABORATION ALTERNATIVES ELEMENTS        -->
<!--                    Content model for the <collab-alternatives>
                        element.                                   -->
<!ENTITY % collab-alternatives-model
                        "(%collab.class;)+"                          >


<!--                    COLLABORATION ALTERNATIVES                 -->
<!--                    Wrapper element for more than one version of
                        a collaboration, for example, the name of a
                        laboratory in more than one language such as
                        the lab name in Japanese kana characters and 
                        a transliterated form of the lab name in Latin 
                        alphabet.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=collab-alternatives
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=collab-alternatives
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=collab-alternatives
                                                                   -->
<!ELEMENT  collab-alternatives
                        %collab-alternatives-model;                  >
<!ATTLIST  collab-alternatives
             %collab-alternatives-atts;                              >


<!--                    COLLABORATIVE (GROUP) AUTHOR ELEMENTS      -->
<!--                    Elements for use in the <collab> element
                        Design Note: All inline mixes begin with an
                        OR bar, but since %simple-text; is an inline
                        mix, the OR bar is already there.          -->
<!ENTITY % collab-elements
                        "%simple-text; | %address.class; |
                         %contrib-group.class; |
                         %contrib-info.class; | %fn-link.class;"     >


<!--                    COLLABORATIVE (GROUP) AUTHOR               -->
<!--                    Used for groups of authors credited under
                        one name, either as a collaboration in the
                        strictest sense, or when an organization,
                        institution, or corporation is the group.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=collab
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=collab
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=collab
                                                                   -->
<!ELEMENT  collab       (#PCDATA %collab-elements;)*                 >
<!ATTLIST  collab
             %collab-atts;                                           >


<!-- ============================================================= -->
<!--                    PERSON'S NAME ELEMENTS (BIBLIOGRAPHIC)     -->
<!-- ============================================================= -->


<!--                    CONTRIBUTOR IDENTIFIER MODEL               -->
<!--                    Content model for the <contrib-id> element -->
<!ENTITY % contrib-id-model
                        "(#PCDATA)"                                  >


<!--                    CONTRIBUTOR IDENTIFIER                     -->
<!--                    One identifier for a person such as a
                        contributor or principal investigator. This
                        element will hold an ORCID, a trusted
                        publisher's identifier, or a JST or NII 
                        identifier.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=contrib-id
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=contrib-id
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=contrib-id
                                                                   -->
<!ELEMENT  contrib-id   %contrib-id-model;                           >
<!ATTLIST  contrib-id 
             %contrib-id-atts;                                       >


<!--                    NAME ALTERNATIVES MODEL                    -->
<!--                    Content model for the <name-alternatives>
                        element                                    -->
<!ENTITY % name-alternatives-model
                        "((%name-alternatives.class;)+)"             >


<!--                    NAME ALTERNATIVES                          -->
<!--                    Wrapper element for more than one version of
                        a personal name, for example, the name in
                        Japanese kana characters and a transliterated
                        form of the name in Latin alphabet.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=name-alternatives
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=name-alternatives
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=name-alternatives
                                                                   -->
<!ELEMENT  name-alternatives
                        %name-alternatives-model;                    >
<!ATTLIST  name-alternatives
             %name-alternatives-atts;                                >



<!--                    NAME OF PERSON (STRUCTURED)                -->
<!--                    Wrapper element for personal names.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=name
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=name
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=name
                                                                   -->
<!ELEMENT  name         ( ( (surname, given-names?) | given-names),
                          prefix?, suffix?)                          >
<!ATTLIST  name
             %name-atts;                                             >


<!--                    STRING NAME ELEMENTS                       -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the <string-name> element                  -->
<!ENTITY % string-name-elements
                        " | %person-name.class;"                     >


<!--                    NAME OF PERSON (UNSTRUCTURED)              -->
<!--                    Wrapper element for personal names where the
                        stricter format of the <name> element cannot
                        be followed. This is a very loose element,
                        allowing data characters, generated text,
                        and any or all of the naming elements.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=string-name
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=string-name
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=string-name
                                                                   -->
<!ELEMENT  string-name  (#PCDATA %string-name-elements;)*            >
<!ATTLIST  string-name
             %string-name-atts;                                      >


<!-- ============================================================= -->
<!--                    PARTS OF A PERSON'S NAME ELEMENTS          -->
<!-- ============================================================= -->


<!--                    DEGREE(S) ELEMENTS                         -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        <degrees>
                        Design Note: -%just-rendition; begins with
                        an OR bar, so this inline mix begins with
                        an OR bar.                                 -->
<!ENTITY % degrees-elements
                        "%just-rendition;"                           >


<!--                    DEGREE(S)                                  -->
<!--                    Academic degrees or professional
                        certifications
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=degrees
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=degrees
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=degrees
                                                                   -->
<!ELEMENT  degrees      (#PCDATA %degrees-elements;)*                >
<!ATTLIST  degrees
             %degrees-atts;                                          >


<!--                    GIVEN (FIRST) NAMES ELEMENTS               -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        a <given-names>                            -->
<!ENTITY % given-names-elements
                        "%just-rendition;"                           >


<!--                    GIVEN (FIRST) NAMES                        -->
<!--                    Includes all given names for a person, such
                        as the first name, middle names, maiden
                        name if used as part of the married name,
                        etc.)
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=given-names
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=given-names
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=given-names
                                                                   -->
<!ELEMENT  given-names  (#PCDATA %given-names-elements;)*            >
<!ATTLIST  given-names
             %given-names-atts;                                      >


<!--                    SURNAME ELEMENTS                           -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        a <surname>
                        Design Note: This PE begins with an OR
                        bar because %just-rendition; begins with an
                        OR bar.                                    -->
<!ENTITY % surname-elements
                        "%just-rendition; "                          >


<!--                    SURNAME                                    -->
<!--                    The surname (family name) of an individual.
                        or the single name if there is only one
                        name, for example, "Cher" or "Pele".
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=surname
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=surname
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=surname
                                                                   -->
<!ELEMENT  surname      (#PCDATA %surname-elements;)*                >
<!ATTLIST  surname
             %surname-atts;                                          >


<!--                    PREFIX ELEMENTS                            -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        a <prefix>
                        Design Note: This PE begins with an OR
                        bar because %just-rendition; begins with an
                        OR bar.                                    -->
<!ENTITY % prefix-elements
                        "%just-rendition;"                           >


<!--                    PREFIX                                     -->
<!--                    Honorifics or other qualifiers that usually
                        precede the surname, for example,  Professor,
                        Rev., President, Senator, Dr., Sir, The
                        Honorable, et al.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=prefix
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=prefix
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=prefix
                                                                   -->
<!ELEMENT  prefix       (#PCDATA %prefix-elements;)*                 >
<!ATTLIST  prefix
             %prefix-atts;                                           >



<!--                    SUFFIX ELEMENTS                            -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        a <suffix>
                        Design Note: This PE begins with an OR bar,
                        it is inside %just-rendition;              -->
<!ENTITY % suffix-elements
                        "%just-rendition;"                           >


<!--                    SUFFIX                                     -->
<!--                    Text used as a suffix to a person's name, for
                        example: Sr., Jr., III, 3rd
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=suffix
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=suffix
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=suffix
                                                                   -->
<!ELEMENT  suffix       (#PCDATA %suffix-elements;)*                 >
<!ATTLIST  suffix
             %suffix-atts;                                           >


<!-- ============================================================= -->
<!--                    EXTERNAL LINK ELEMENTS                     -->
<!-- ============================================================= -->


<!--                    EXTERNAL LINK ELEMENTS                     -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        an <ext-link>
                        Design Note: All inline mixes begin with an
                        OR bar, but since %link-elements; is an inline
                        mix, the OR bar is already there.          -->
<!ENTITY % ext-link-elements
                        "%link-elements;"                            >


<!--                    EXTERNAL LINK                              -->
<!--                    Link to an external file, such as Medline,
                        Genbank, etc.  Linking may be accomplished
                        using the XLink linking attributes.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=ext-link
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=ext-link
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=ext-link
                                                                   -->
<!ELEMENT  ext-link     (#PCDATA %ext-link-elements;)*               >
<!ATTLIST  ext-link
             %ext-link-atts;                                         >


<!-- ============================================================= -->
<!--                    SHARED STRUCTURAL ELEMENTS                 -->
<!-- ============================================================= -->


<!--                    ATTRIBUTION ELEMENTS                       -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        an attribution
                        Design Note: All inline mixes begin with an
                        OR bar, but since %simple-phrase; is an
                        inline mix, the OR bar is already there.   -->
<!ENTITY % attrib-elements
                        "%emphasized-text;"                          >


<!--                    ATTRIBUTION                                -->
<!--                    Source, author, formal thanks, or other
                        information (other than copyright material)
                        concerning the origins of an extract, a poem
                        <verse-group> or similar element.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=attrib
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=attrib
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=attrib
                                                                   -->
<!ELEMENT  attrib       (#PCDATA %attrib-elements;)*                 >
<!ATTLIST  attrib
             %attrib-atts;                                           >


<!--                    DEFINITION LIST: DEFINITION MODEL          -->
<!--                    Content model for the <def> element, which
                        is used in contexts outside of <def-list>s -->
<!ENTITY % def-model    "((%just-para.class;)+ )"                    >


<!--                    DEFINITION LIST: DEFINITION                -->
<!--                    Used in two senses:
                        1) The definition, description, or other
                        explanation of the word, phrase, or picture
                        of a 2-part or definition list
                        2) The definition or expansion of an
                        abbreviation or acronym <abbrev>
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=def
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=def
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=def
                                                                   -->
<!ELEMENT  def          %def-model;                                  >
<!ATTLIST  def
             %def-atts;                                              >


<!--                    LABEL ELEMENTS                             -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the <label> element                        -->
<!ENTITY % label-elements
                        "| %emphasis.class; | %inline-display.class; |
                         %inline-math.class; | %subsup.class;"       >


<!--                    LABEL OF A FIGURE, REFERENCE, ETC.         -->
<!--                    The number and any prefix word that comes
                        before, for example, the caption of a Figure,
                        such as "Figure 3." or "Exhibit 2.".
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=label
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=label
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=label
                                                                   -->
<!ELEMENT  label        (#PCDATA %label-elements;)*                  >
<!ATTLIST  label
             %label-atts;                                            >


<!--                    PRICE ELEMENTS                             -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the <price> element                        -->
<!ENTITY % price-elements
                        "| %emphasis.class;"                         >


<!--                    PRICE                                      -->
<!--                    Sale price of a work, typically a book or
                        software package that is being reviewed
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=price
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=price
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=price
                                                                   -->
<!ELEMENT  price        (#PCDATA %price-elements;)*                  >
<!ATTLIST  price
             %price-atts;                                            >


<!--                    STRUCTURAL TITLE ELEMENTS                  -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the <title> element
                        Design Note: All inline mixes begin with an
                        OR bar, but since %simple-phrase; is an
                        inline mix, the OR bar is already there.   -->
<!ENTITY % struct-title-elements
                        "%simple-phrase; | %break.class; |
                         %citation.class;"                           >


<!--                    TITLE                                      -->
<!--                    Heading or title for a structural element
                        such as a Section, Figure, Boxed Text, etc.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=title
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=title
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=title
                                                                   -->
<!ELEMENT  title        (#PCDATA %struct-title-elements;)*           >
<!ATTLIST  title
             %title-atts;                                            >


<!-- ============================================================= -->
<!--                    RELATED ARTICLE ELEMENTS                   -->
<!--                    (METADATA AND STRUCTURAL)                  -->
<!-- ============================================================= -->


<!--                    RELATED ARTICLE ELEMENTS                   -->
<!--                    Elements allowed inside <related-article>  -->
<!ENTITY % related-article-elements
                        "| %emphasis.class; | %journal-id.class; |
                         %phrase-content.class; |
                         %references.class; |  %subsup.class;"       >


<!--                    RELATED ARTICLE INFORMATION                -->
<!--                    Wrapper element, used as a container for
                        text links to a related article, possibly
                        accompanied by a very brief description
                        such as "errata (correction)".
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=related-article
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=related-article
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=related-article
                                                                   -->
<!ELEMENT  related-article
                        (#PCDATA %related-article-elements;)*        >
<!ATTLIST  related-article
             %related-article-atts;                                  >


<!-- ============================================================= -->
<!--                    SIGNATURE BLOCK ELEMENTS                   -->
<!-- ============================================================= -->


<!--                    SIGNATURE BLOCK ELEMENTS                   -->
<!--                    Elements to be mixed with data characters
                        inside the content model for the
                        <sig-block> element.                       -->
<!ENTITY % sig-block-elements
                         "| %break.class; | %emphasis.class; |
                          %just-base-display.class; |
                          %inline-display-noalt.class; |
                          %phrase-content.class; |
                          %sig.class; | %subsup.class;"              >


<!--                    SIGNATURE BLOCK                            -->
<!--                    An area of text and graphic material placed
                        at the end of an article or section to hold
                        the graphical signature or other description
                        of the person responsible for or attesting
                        to the content.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=sig-block
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=sig-block
                                                                   -->
<!ELEMENT  sig-block    (#PCDATA %sig-block-elements;)*              >
<!ATTLIST  sig-block
             %sig-block-atts;                                        >


<!--                    SIGNATURE ELEMENTS                         -->
<!--                    Elements to be mixed with data characters
                        inside the content model for the
                        <sig> element.                             -->
<!ENTITY % sig-elements "%rendition-plus; | %break.class; |
                         %inline-display-noalt.class; |
                         %just-base-display-noalt.class;"            >


<!--                    SIGNATURE                                  -->
<!--                    One contributor signature and associated
                        material (such as a text restatement of the
                        affiliation) inside a signature block.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=sig
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=sig
                                                                   -->
<!ELEMENT  sig          (#PCDATA %sig-elements;)*                    >
<!ATTLIST  sig
             %sig-atts;                                              >


<!-- ============================================================= -->
<!--                    FRONT MATTER/BACK MATTER ELEMENTS          -->
<!-- ============================================================= -->


<!--                    ACKNOWLEDGMENTS MODEL                      -->
<!--                    Content model for the <ack> element        -->
<!ENTITY % ack-model    "%sec-opt-title-model;"                      >


<!--                    ACKNOWLEDGMENTS                            -->
<!--                    Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=ack
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=ack
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=ack
                                                                   -->

<!ELEMENT  ack          %ack-model;                                  >
<!ATTLIST  ack
             %ack-atts;                                              >


<!--                    BIOGRAPHY MODEL                            -->
<!--                    Content model for the <bio> element        -->
<!ENTITY % bio-model    "%sec-opt-title-model;"                      >


<!--                    BIOGRAPHY                                  -->
<!--                    Short biography of a person, usually the
                        author
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=bio
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=bio
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=bio
                                                                   -->
<!ELEMENT  bio          %bio-model;                                  >
<!ATTLIST  bio
             %bio-atts;                                              >


<!--                    NOTES MODEL                                -->
<!--                    Content model for the <notes> element      -->
<!ENTITY % notes-model  "%sec-opt-title-model;"                      >


<!--                    NOTES                                      -->
<!--                    A container element for the notes that may
                        appear at the end of an Article or at the
                        end of a Table, for example, a typical
                        end-of-article note is a "Note in Proof".
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=notes
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=notes
                                                                   -->
<!ELEMENT  notes        %notes-model;                                >
<!ATTLIST  notes
             %notes-atts;                                            >


<!-- ============================================================= -->
<!--                    ACCESSIBILITY ELEMENTS                     -->
<!-- ============================================================= -->


<!--                    ALTERNATE TITLE TEXT FOR A FIGURE, ETC.    -->
<!--                    Short phrase used to display or pronounce
                        as an alternative to providing the full
                        graphic for accessibility display or
                        graphic-limited web sites or devices. For
                        example, <alt-text> may be used to display
                        "behind" a figure or graphic.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=alt-text
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=alt-text
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=alt-text
                                                                   -->
<!ELEMENT  alt-text     (#PCDATA)                                    >
<!ATTLIST  alt-text
             %alt-text-atts;                                         >


<!--                    LONG DESCRIPTION ELEMENTS                  -->
<!--                    Elements to be mixed with data characters
                        inside the <long-desc> element             -->
<!ENTITY % long-desc-elements
                        ""                                           >


<!--                    LONG DESCRIPTION                           -->
<!--                    Description or summary of the content of a
                        graphical object, table, or textual object
                        such as a text box, used by some systems to
                        make the object accessible, even to people
                        or systems that cannot read/see/display the
                        object.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=long-desc
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=long-desc
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=long-desc
                                                                   -->
<!ELEMENT  long-desc    (#PCDATA %long-desc-elements;)*              >
<!ATTLIST  long-desc
             %long-desc-atts;                                        >


<!-- ============================================================= -->
<!--                    CUSTOM METADATA ELEMENTS                   -->
<!-- ============================================================= -->


<!--                    CUSTOM METADATA GROUP MODEL                -->
<!--                    Content model for the <custom-meta-group>
                        element                                    -->
<!ENTITY % custom-meta-group-model
                        "(custom-meta+)"                             >


<!--                    CUSTOM METADATA GROUP                      -->
<!--                    Some DTDs and schemas allow for metadata
                        above and beyond that which can be specified
                        by this DTD. This element is a grouping
                        element used to contain all these additional
                        metadata elements.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=custom-meta-group
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=custom-meta-group
                                                                   -->
<!ELEMENT  custom-meta-group
                        %custom-meta-group-model;                    >
<!ATTLIST  custom-meta-group
             %custom-meta-group-atts;                                >


<!--                    CUSTOM METADATA MODEL                      -->
<!--                    Content model for the <custom-meta> element-->
<!ENTITY % custom-meta-model
                        "(meta-name, meta-value)"                    >


<!--                    CUSTOM METADATA                            -->
<!--                    Some DTDs and schemas allow for metadata
                        above and beyond that which can be specified
                        by this DTD. This element is used to capture
                        metadata elements that have not been defined
                        explicitly in the models for this DTD, so
                        that the intellectual content will not be lost.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=custom-meta
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=custom-meta
                                                                   -->
<!ELEMENT  custom-meta  %custom-meta-model;                          >
<!ATTLIST  custom-meta
             %custom-meta-atts;                                      >


<!--                    METADATA DATA NAME ELEMENTS                -->
<!--                    Elements that may be used, along with data
                        characters, inside the <meta-name> element
                        Design Note: All inline mixes begin with an
                        OR bar, but since %simple-phrase; is an
                        inline mix, the OR bar is already there.   -->
<!ENTITY % meta-name-elements
                        ""                                           >


<!--                    METADATA DATA NAME FOR CUSTOM METADATA     -->
<!--                    The <custom-meta> element
                        allows for an infinite number of name/value
                        pairs, with few constraints on the length or
                        content of the value. This element contains
                        the name of the metadata field.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=meta-name
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=meta-name
                                                                   -->
<!ELEMENT  meta-name    (#PCDATA %meta-name-elements;)*              >
<!ATTLIST  meta-name
             %meta-name-atts;                                        >


<!--                    METADATA DATA VALUE ELEMENTS               -->
<!--                    Elements that may be used, along with data
                        characters, inside the <meta-value> element
                        Design Note: All inline mixes begin with an
                        OR bar, but since %simple-phrase; is an
                        inline mix, the OR bar is already there.   -->
<!ENTITY % meta-value-elements
                        "%simple-phrase;"                            >


<!--                    METADATA DATA VALUE FOR CUSTOM METADATA    -->
<!--                    The <custom-meta> element
                        allows for an infinite number of name/value
                        pairs, with few constraints on the length or
                        content of the value. This element contains
                        the value of the metadata field that is named
                        by the <meta-name> element.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=meta-value
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=meta-value
                                                                   -->
<!ELEMENT  meta-value   (#PCDATA %meta-value-elements;)*             >
<!ATTLIST  meta-value
             %meta-value-atts;                                       >


<!-- ============================================================= -->
<!--                    PROCESSING ALTERNATIVES ELEMENTS           -->
<!-- ============================================================= -->


<!--                    ALTERNATIVES MODEL                         -->
<!--                    Model for the <alternatives> processing
                        alternatives element                       -->
<!ENTITY % alternatives-model
                        "((%id.class;)*, 
                          (%alternatives-display.class; | 
                          %math.class;)+ )"                          >


<!--                    ALTERNATIVES FOR PROCESSING                -->
<!--                    Container element used to hold a group of
                        processing alternatives, for example, a
                        single logical <graphic> that ships in
                        different formats (tif, gif, jpeg) or
                        different resolutions. This element is a
                        physical grouping to contain multiple
                        logically equivalent (substitutable) versions
                        of the same information object.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=alternatives
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=alternatives
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=alternatives
                                                                   -->
<!ELEMENT  alternatives %alternatives-model;                         >
<!ATTLIST  alternatives
             %alternatives-atts;                                     >


<!--                    TEXTUAL FORM ELEMENTS                      -->
<!--                    Model for the <textual-form> element       -->
<!ENTITY % textual-form-elements
                        "| %emphasis.class; |
                         %inline-display-noalt.class; |
                         %math.class; |
                         %phrase-content.class; | %subsup.class;"    >


<!--                    TEXTUAL FORM                               -->
<!--                    Container element (for use only inside
                        <alternatives>) that will hold text and
                        mixed content objects that act as alternatives
                        to, for example, graphics.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=textual-form
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=textual-form
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=textual-form
                                                                   -->
<!ELEMENT  textual-form (#PCDATA %textual-form-elements;)*           >
<!ATTLIST  textual-form
             %textual-form-atts;                                     >


<!-- ============================================================= -->
<!--                    GENERATED TEXT OR PUNCTUATION              -->
<!-- ============================================================= -->


<!--                    X TEXT ATTRIBUTES                          -->
<!--                    Attributes for the element <x>             -->
<!ENTITY % x-atts
            "%jats-common-atts;                                        
             content-type
                        CDATA                             #IMPLIED
             xml:space ( default | preserve)       #FIXED 'preserve'
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    X ELEMENTS                                 -->
<!--                    Elements for use inside the <x> element    -->
<!ENTITY % x-elements   ""                                           >


<!--                    X - GENERATED TEXT AND PUNCTUATION         -->
<!--                    A container element to hold punctuation or
                        other generated text, typically when 1) an
                        archive decides not to have any text
                        generated and thus to pre-generate such
                        things as commas or semicolons between
                        keywords or 2) when an archive receives text
                        with <x> tags embedded and wishes to retain
                        them.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=x
                                                                   -->
<!ELEMENT  x            (#PCDATA %x-elements;)*                      >
<!ATTLIST  x
             %x-atts;                                                >


<!-- ================== End NISO JATS Common (Shared) Elements === -->


"""
PDF导出模块
使用WeasyPrint将HTML报告转换为PDF
"""
import os
import logging
from pathlib import Path
from typing import Optional, Dict, Any, Union
from urllib.parse import urljoin, urlparse

from weasyprint import HTML, CSS
from weasyprint.text.fonts import FontConfiguration

logger = logging.getLogger(__name__)

class PDFExporter:
    """
    PDF导出类，用于将HTML报告转换为PDF
    """
    
    def __init__(self, base_url: Optional[str] = None):
        """
        初始化PDF导出器
        
        Args:
            base_url: 基础URL，用于解析HTML中的相对路径
        """
        self.base_url = base_url or 'file://' + str(Path.cwd()) + os.sep
    
    def export_from_html(
        self,
        html_content: str,
        output_path: str,
        stylesheets: Optional[list] = None,
        pdf_kwargs: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        从HTML内容导出PDF
        
        Args:
            html_content: HTML内容
            output_path: 输出PDF文件路径
            stylesheets: 额外的CSS样式表路径列表
            pdf_kwargs: 传递给PDF生成的参数
            
        Returns:
            str: 输出的PDF文件路径
        """
        try:
            # 确保输出目录存在
            os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)
            
            # 创建字体配置
            font_config = FontConfiguration()
            
            # 准备样式表
            css = [
                CSS(string=self._get_default_styles()),
                # 添加对中文字体的支持
                CSS(string='''
                    @font-face {
                        font-family: 'SimHei';
                        src: local('SimHei'), local('Microsoft YaHei');
                    }
                    body {
                        font-family: 'SimHei', 'Microsoft YaHei', Arial, sans-serif;
                    }
                ''')
            ]
            
            # 添加额外的样式表
            if stylesheets:
                for sheet in stylesheets:
                    if os.path.exists(sheet):
                        css.append(CSS(filename=sheet))
            
            # 生成PDF
            html = HTML(
                string=html_content,
                base_url=self.base_url,
                encoding='utf-8'
            )
            
            # 默认PDF参数
            default_pdf_kwargs = {
                'stylesheets': css,
                'font_config': font_config,
                'optimize_size': ['fonts', 'images'],
                'presentational_hints': True
            }
            
            # 更新用户提供的参数
            if pdf_kwargs:
                default_pdf_kwargs.update(pdf_kwargs)
            
            # 写入PDF文件
            html.write_pdf(output_path, **default_pdf_kwargs)
            
            logger.info(f"PDF已成功导出到: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"导出PDF时出错: {str(e)}", exc_info=True)
            raise
    
    def export_from_url(
        self,
        url: str,
        output_path: str,
        stylesheets: Optional[list] = None,
        pdf_kwargs: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        从URL导出PDF
        
        Args:
            url: 网页URL
            output_path: 输出PDF文件路径
            stylesheets: 额外的CSS样式表路径列表
            pdf_kwargs: 传递给PDF生成的参数
            
        Returns:
            str: 输出的PDF文件路径
        """
        try:
            # 确保输出目录存在
            os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)
            
            # 创建字体配置
            font_config = FontConfiguration()
            
            # 准备样式表
            css = [CSS(string=self._get_default_styles())]
            
            # 添加额外的样式表
            if stylesheets:
                for sheet in stylesheets:
                    if os.path.exists(sheet):
                        css.append(CSS(filename=sheet))
            
            # 生成PDF
            html = HTML(
                url=url,
                encoding='utf-8'
            )
            
            # 默认PDF参数
            default_pdf_kwargs = {
                'stylesheets': css,
                'font_config': font_config,
                'optimize_size': ['fonts', 'images']
            }
            
            # 更新用户提供的参数
            if pdf_kwargs:
                default_pdf_kwargs.update(pdf_kwargs)
            
            # 写入PDF文件
            html.write_pdf(output_path, **default_pdf_kwargs)
            
            logger.info(f"PDF已成功从URL导出到: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"从URL导出PDF时出错: {str(e)}", exc_info=True)
            raise
    
    def _get_default_styles(self) -> str:
        """
        获取默认的CSS样式
        
        Returns:
            str: CSS样式字符串
        """
        return """
            @page {
                size: A4;
                margin: 1.5cm;
                @top-center {
                    content: element(header);
                }
                @bottom-center {
                    content: element(footer);
                }
            }
            
            @page :first {
                @top-center {
                    content: none;
                }
            }
            
            body {
                font-family: 'SimHei', 'Microsoft YaHei', Arial, sans-serif;
                font-size: 10pt;
                line-height: 1.5;
                color: #000;
                margin: 0;
                padding: 0;
            }
            
            h1, h2, h3, h4, h5, h6 {
                color: #2c3e50;
                page-break-after: avoid;
            }
            
            h1 {
                font-size: 18pt;
                margin-top: 1.5em;
                margin-bottom: 0.5em;
                border-bottom: 2px solid #2c3e50;
                padding-bottom: 0.2em;
            }
            
            h2 {
                font-size: 16pt;
                margin-top: 1.2em;
                margin-bottom: 0.5em;
                border-bottom: 1px solid #ddd;
                padding-bottom: 0.2em;
            }
            
            h3 {
                font-size: 14pt;
                margin-top: 1em;
                margin-bottom: 0.5em;
            }
            
            p {
                margin: 0.5em 0;
                text-align: justify;
                hyphens: auto;
            }
            
            a {
                color: #2980b9;
                text-decoration: none;
            }
            
            table {
                width: 100%;
                border-collapse: collapse;
                margin: 1em 0;
                font-size: 9pt;
                page-break-inside: avoid;
            }
            
            th, td {
                border: 1px solid #ddd;
                padding: 6px 8px;
                text-align: left;
            }
            
            th {
                background-color: #f2f2f2;
                font-weight: bold;
            }
            
            .plotly-graph-div {
                margin: 1em 0;
                page-break-inside: avoid;
            }
            
            .no-print {
                display: none !important;
            }
            
            .page-break {
                page-break-before: always;
            }
            
            .keep-together {
                page-break-inside: avoid;
            }
            
            .footer {
                position: running(footer);
                font-size: 8pt;
                color: #666;
                text-align: center;
                width: 100%;
            }
            
            .page-number:before {
                content: counter(page);
            }
            
            .page-count:before {
                content: counter(pages);
            }
        """

    @staticmethod
    def merge_pdfs(input_paths: list, output_path: str) -> str:
        """
        合并多个PDF文件
        
        Args:
            input_paths: 输入PDF文件路径列表
            output_path: 输出PDF文件路径
            
        Returns:
            str: 合并后的PDF文件路径
        """
        try:
            from PyPDF2 import PdfMerger
            
            merger = PdfMerger()
            
            for pdf in input_paths:
                if os.path.exists(pdf):
                    merger.append(pdf)
            
            os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)
            merger.write(output_path)
            merger.close()
            
            logger.info(f"PDF文件已成功合并到: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"合并PDF文件时出错: {str(e)}", exc_info=True)
            raise

    @staticmethod
    def add_watermark(
        input_path: str, 
        output_path: str, 
        text: str = "CONFIDENTIAL",
        **kwargs
    ) -> str:
        """
        添加水印到PDF
        
        Args:
            input_path: 输入PDF文件路径
            output_path: 输出PDF文件路径
            text: 水印文本
            **kwargs: 其他参数，如角度、透明度、字体大小等
            
        Returns:
            str: 添加水印后的PDF文件路径
        """
        try:
            from reportlab.pdfgen import canvas
            from reportlab.lib.colors import gray
            from PyPDF2 import PdfReader, PdfWriter
            from io import BytesIO
            
            # 默认参数
            angle = kwargs.get('angle', 45)
            alpha = kwargs.get('alpha', 0.1)
            font_size = kwargs.get('font_size', 60)
            
            # 读取原始PDF
            reader = PdfReader(input_path)
            writer = PdfWriter()
            
            # 获取PDF页面大小
            page = reader.pages[0]
            width = float(page.mediabox.width)
            height = float(page.mediabox.height)
            
            # 创建水印PDF
            packet = BytesIO()
            can = canvas.Canvas(packet, pagesize=(width, height))
            
            # 设置透明度
            can.setFillColor(gray, alpha=alpha)
            
            # 设置字体和大小
            can.setFont('Helvetica-Bold', font_size)
            
            # 计算文本位置（居中）
            text_width = can.stringWidth(text, 'Helvetica-Bold', font_size)
            x = (width - text_width) / 2
            y = height / 2
            
            # 旋转画布并绘制水印
            can.saveState()
            can.translate(x, y)
            can.rotate(angle)
            can.drawString(0, 0, text)
            can.restoreState()
            
            # 保存水印PDF
            can.save()
            packet.seek(0)
            
            # 合并水印和原始PDF
            watermark = PdfReader(packet)
            watermark_page = watermark.pages[0]
            
            for page in reader.pages:
                page.merge_page(watermark_page)
                writer.add_page(page)
            
            # 写入输出文件
            os.makedirs(os.path.dirname(os.path.abspath(output_path)), exist_ok=True)
            with open(output_path, 'wb') as output_file:
                writer.write(output_file)
            
            logger.info(f"水印已成功添加到: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"添加水印时出错: {str(e)}", exc_info=True)
            raise

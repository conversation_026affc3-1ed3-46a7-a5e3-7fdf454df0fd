"""
EBM报告生成示例

本示例展示如何使用扩展模块生成包含森林图、漏斗图和偏倚风险图的EBM报告
"""
import os
import sys
import json
import logging
from pathlib import Path
import numpy as np
import pandas as pd

# 添加项目根目录到Python路径
project_root = str(Path(__file__).parent.parent)
if project_root not in sys.path:
    sys.path.append(project_root)

# 导入扩展模块
from extensions.visualization import ForestPlot, FunnelPlot, RobPlot
from extensions.reporting import ReportBuilder
from extensions.utils import ensure_dir, save_json, setup_logger

# 配置日志
log_dir = Path('logs')
log_file = log_dir / 'ebm_report_example.log'
ensure_dir(log_dir)
setup_logger('ebm_report', log_file=log_file, level=logging.INFO)
logger = logging.getLogger('ebm_report')

def generate_sample_data():
    """生成示例数据"""
    # 森林图数据
    studies = [
        {'study': 'Study A', 'year': 2020, 'effect': 0.5, 'ci_lower': 0.3, 'ci_upper': 0.7, 'weight': 15.5},
        {'study': 'Study B', 'year': 2019, 'effect': 0.7, 'ci_lower': 0.5, 'ci_upper': 0.9, 'weight': 18.2},
        {'study': 'Study C', 'year': 2021, 'effect': 0.3, 'ci_lower': 0.1, 'ci_upper': 0.5, 'weight': 12.8},
        {'study': 'Study D', 'year': 2018, 'effect': 0.8, 'ci_lower': 0.6, 'ci_upper': 1.0, 'weight': 20.1},
        {'study': 'Study E', 'year': 2022, 'effect': 0.4, 'ci_lower': 0.2, 'ci_upper': 0.6, 'weight': 14.3},
        {'study': 'Study F', 'year': 2020, 'effect': 0.6, 'ci_lower': 0.4, 'ci_upper': 0.8, 'weight': 19.0},
    ]
    
    # 漏斗图数据
    funnel_data = [
        {'effect': 0.5, 'se': 0.1, 'study': 'Study A'},
        {'effect': 0.7, 'se': 0.12, 'study': 'Study B'},
        {'effect': 0.3, 'se': 0.08, 'study': 'Study C'},
        {'effect': 0.8, 'se': 0.15, 'study': 'Study D'},
        {'effect': 0.4, 'se': 0.09, 'study': 'Study E'},
        {'effect': 0.6, 'se': 0.11, 'study': 'Study F'},
    ]
    
    # 偏倚风险数据
    rob_data = {
        'domains': [
            '随机序列生成',
            '分配隐藏',
            '参与者和人员盲法',
            '结果评估盲法',
            '不完整的结果数据',
            '选择性报告',
            '其他偏倚'
        ],
        'studies': [
            {'study': 'Study A', 'scores': [1, 1, 1, 0, 0, 1, 0]},  # 1=低风险, 0=高风险, 0.5=不确定
            {'study': 'Study B', 'scores': [1, 0.5, 0, 0.5, 1, 1, 0]},
            {'study': 'Study C', 'scores': [1, 1, 1, 1, 1, 1, 0]},
            {'study': 'Study D', 'scores': [0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5]},
            {'study': 'Study E', 'scores': [1, 1, 0, 1, 1, 0, 0]},
            {'study': 'Study F', 'scores': [1, 0, 0, 0, 1, 1, 0]},
        ]
    }
    
    return {
        'forest': studies,
        'funnel': funnel_data,
        'rob': rob_data
    }

def create_sample_report(output_dir: str = 'reports'):
    """创建示例报告"""
    try:
        logger.info("开始生成EBM报告...")
        
        # 生成示例数据
        data = generate_sample_data()
        
        # 创建报告构建器
        report_builder = ReportBuilder(output_dir=output_dir)
        
        # 准备报告内容
        title = "系统评价与Meta分析报告示例"
        
        # Markdown格式的报告内容
        content_md = """
        # 系统评价与Meta分析报告
        
        ## 背景
        本报告展示了如何使用Python生成包含森林图、漏斗图和偏倚风险图的EBM报告。
        
        ## 方法
        - **研究选择**：纳入了6项随机对照试验
        - **效应量**：计算了标准化均数差(SMD)及其95%置信区间
        - **异质性**：使用I²统计量评估研究间异质性
        - **发表偏倚**：通过漏斗图评估发表偏倚
        - **偏倚风险**：使用Cochrane偏倚风险评估工具评估纳入研究的质量
        
        ## 结果
        
        ### 森林图
        下图展示了各研究的效应量及其95%置信区间，以及汇总效应量。
        
        ### 漏斗图
        下图展示了各研究的效应量与标准误的关系，用于评估发表偏倚。
        
        ### 偏倚风险图
        下图展示了各研究在不同偏倚领域的风险评估结果。
        
        ## 讨论
        本示例展示了如何使用Python生成专业的EBM报告。实际应用中，需要根据具体研究问题和数据调整分析方法和报告内容。
        """
        
        # 构建报告
        result = report_builder.create_ebm_report(
            title=title,
            content_md=content_md,
            forest_plot_data={
                'studies': data['forest'],
                'effect_measure': 'SMD',
                'show_overall': True,
                'title': '森林图: 干预组 vs 对照组',
                'x_label': '标准化均数差 (SMD)',
                'summary_label': '汇总效应',
                'reference_line': 0,
                'show_heterogeneity': True,
                'show_favors': True,
                'favors_left': 'Favors Control',
                'favors_right': 'Favors Treatment'
            },
            funnel_plot_data={
                'data': data['funnel'],
                'title': '漏斗图: 发表偏倚评估',
                'x_label': '效应量 (SMD)',
                'y_label': '标准误',
                'show_ci': True,
                'show_contour': True,
                'contour_color': 'rgba(0, 100, 0, 0.1)',
                'contour_line_color': 'rgba(0, 100, 0, 0.5)',
                'show_legend': True
            },
            rob_plot_data={
                'domains': data['rob']['domains'],
                'studies': data['rob']['studies'],
                'title': '偏倚风险评估',
                'width': 800,
                'height': 400,
                'colorscale': [
                    [0, 'rgb(255, 105, 97)'],    # 红色 - 高风险
                    [0.5, 'rgb(255, 255, 191)'],  # 黄色 - 不确定风险
                    [1, 'rgb(165, 221, 134)']     # 绿色 - 低风险
                ],
                'show_legend': True
            },
            output_filename='ebm_report_example',
            author='EBM Report Generator',
            date='2023-11-15',
            version='1.0.0',
            contact='<EMAIL>',
            footer='Generated by EBM Report Generator - 仅供研究使用'
        )
        
        logger.info(f"报告生成完成: {json.dumps(result, indent=2, ensure_ascii=False)}")
        return result
        
    except Exception as e:
        logger.error(f"生成报告时出错: {str(e)}", exc_info=True)
        raise

if __name__ == "__main__":
    # 创建示例报告
    report = create_sample_report()
    
    # 打印报告路径
    if report and 'html' in report:
        print(f"\n报告已生成:")
        print(f"- HTML 报告: {os.path.abspath(report['html'])}")
        if 'pdf' in report:
            print(f"- PDF 报告: {os.path.abspath(report['pdf'])}")
        else:
            print("PDF 生成失败，请检查日志文件查看详细信息")
    else:
        print("报告生成失败，请检查日志文件查看错误信息")

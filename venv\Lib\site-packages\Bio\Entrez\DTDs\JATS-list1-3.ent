<!-- ============================================================= -->
<!--  MODULE:    List Element Classes                              -->
<!--  VERSION:   ANSI/NISO JATS Version 1.3 (Z39.96-2021)          -->
<!--  DATE:      June 2021                                         -->
<!--                                                               -->
<!-- ============================================================= -->

<!-- ============================================================= -->
<!--                    PUBLIC DOCUMENT TYPE DEFINITION            -->
<!--                        TYPICAL INVOCATION                     -->
<!--
"-//NLM//DTD JATS (Z39.96) JATS DTD Suite List Class Elements v1.3 20210610//EN"
     Delivered as file "JATS-list1-3.ent"                          -->
<!-- ============================================================= -->

<!-- ============================================================= -->
<!-- SYSTEM:     JATS DTD Suite                                    -->
<!--                                                               -->
<!-- PURPOSE:    Names all elements in the list class. These are   -->
<!--             all lists except the lists of bibliographic       -->
<!--             references (citations). Lists are considered      -->
<!--             to be composed of items.                          -->
<!--                                                               -->
<!-- TAG SET SPONSOR                                               -->
<!--             National Center for Biotechnology                 -->
<!--                Information (NCBI)                             -->
<!--             National Library of Medicine (NLM)                -->
<!--                                                               -->
<!-- CREATED FOR:                                                  -->
<!--             This module was created for the JATS DTD Suite.   -->
<!--             Digital archives and publishers may use the       -->
<!--             DTD as is for markup of journal literature or     -->
<!--             related material for archiving and transferring   -->
<!--             such material between archives or create a        -->
<!--             custom XML DTD from the Suite for                 -->
<!--             these purposes.                                   -->
<!--                                                               -->
<!--             This DTD is in the public domain. An organization -->
<!--             that wishes to create its own DTD from the suite  -->
<!--             may do so without permission from NLM.            -->
<!--                                                               -->
<!--             The suite has been set up to be extended using a  -->
<!--             new DTD file and a new DTD-specific customization -->
<!--             module to redefine the many Parameter Entities.   -->
<!--             Do not modify the suite directly or redistribute  -->
<!--             modified versions of the suite.                   -->
<!--                                                               -->
<!--             In the interest of maintaining consistency and    -->
<!--             clarity for potential users, NLM requests:        -->
<!--                                                               -->
<!--             1. If you create a DTD from the JATS DTD Suite    -->
<!--                and intend to stay compatible with the suite,  -->
<!--                then please include the following statement    -->
<!--                as a comment in all of your DTD modules:       -->
<!--                   "Created from, and fully compatible with,   -->
<!--                    the ANSI/NISO Z39.96 Journal Article Tag   -->
<!--                    Suite (JATS)."                             -->
<!--                                                               -->
<!--             2. If you alter one or more modules of the suite, -->
<!--                then please rename your version and all its    -->
<!--                modules to avoid any confusion with the        -->
<!--                original suite. Also, please include the       -->
<!--                following statement as a comment in all your   -->
<!--                DTD modules:                                   -->
<!--                   "Based in part on, but not fully compatible -->
<!--                    with, the ANSI/NISO Z39.96 Journal Article -->
<!--                    Tag Suite (JATS)."                         -->
<!--                                                               -->
<!-- ORIGINAL CREATION DATE:                                       -->
<!--             December 2002                                     -->
<!--                                                               -->
<!-- CREATED BY: Mulberry Technologies, Inc. for the NISO Z39.96   -->
<!--             Working Group. Mulberry Technologies was          -->
<!--             supported by National Center for Biotechnology    -->
<!--             Information (NCBI), a center of the US National   -->
<!--             Library of Medicine (NLM).                        -->
<!--                                                               -->
<!--             This module is part of the JATS DTD Suite. The    -->
<!--             Suite is a continuation of work done by NCBI,     -->
<!--             Mulberry Technologies, and Inera, Inc. on the NLM -->
<!--             Journal Archiving and Interchange DTD Suite, which-->
<!--             was originally released in December, 2002.        -->
<!--                                                               -->
<!--             NLM thanks the Harvard University Libraries, both -->
<!--             for proposing that a draft archiving NLM DTD for  -->
<!--             life sciences journals be extended to accommodate -->
<!--             journals in all disciplines and for sponsoring    -->
<!--             Inera Inc.'s collaboration with other DTD         -->
<!--             authors in completing NLM Version 1.0. The        -->
<!--             Andrew W. Mellon Foundation provided support for  -->
<!--             these important contributions.                    -->
<!--                                                               -->
<!--             Suggestions for refinements and enhancements to   -->
<!--             this DTD should be sent in email to:              -->
<!--                 <EMAIL>                         -->
<!-- ============================================================= -->


<!-- ============================================================= -->
<!--                    DTD VERSION/CHANGE HISTORY                 -->
<!-- ============================================================= -->
<!--
Version  Reason/Occasion                   (who) vx.x (yyyy-mm-dd)
   ==============================================================
     JATS Version 1.3 (ANSI/NISO Z39.96-2021)
                                  (DAL/BTU) v1.3 (2021-06-10)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by both the NISO and ANSI memberships 
     to be changed. This version of the NISO JATS was approved 
     by ANSI vote on June 10, 2021, so it supersedes all Committee 
     Drafts as well as the draft version JATS 1.3 2021 (the version
     submitted for vote), becoming NISO JATS v1.3 20210610 
     (ANSI/NISO Z39.96-2019).

     The file names and formal public identifiers have been 
     changed in the modules and the catalogs.

 23. JATS 'v1.3d2 20201130' becomes 'v1.3 20210610' 
     (final ANSI vote date)
     'ANSI/NISO Z39.96-2019' becomes 'ANSI/NISO Z39.96-2021'

  ==============================================================
     JATS Version 1.3d2 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.3d2 (2020-11-30)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by the NISO and ANSI memberships 
     to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     of the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting; it is for Standing Committee review and
     testing. The formal public identifiers have been changed 
     in the modules and the catalogs.

 22. OBJECT ID Added <object-id> to: 
        <list>

 21. JATS "v1.3d2 20201130" becomes 'v1.3 20210610' (second 
     committee draft following the final ANSI/NISO 1.2 vote)

   ==============================================================
     JATS Version 1.3d1 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.3.d1 (2019-08-31)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by the NISO and ANSI memberships 
     to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     of the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
  
 20. JATS "1.2" becomes "v1.3d1 20190831" (first committee
     draft following the final ANSI/NISO vote) 

   ==============================================================
     JATS Version 1.2 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.2     (2019-02-08)
    
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the both the NISO and ANSI membership 
     to be changed. This version of the NISO JATS was approved 
     by ANSI vote on Feb 08, 2019, so it supersedes all Committee 
     Drafts as well as the draft version JATS 1.2 2018 (the version
     submitted for vote), becoming NISO JATS v1.2 20190208 
     (ANSI/NISO Z39.96-2021).
  
 19. JATS "1.2" becomes "v1.2 20190208" (final ANSI vote date)
     "ANSI/NISO Z39.96-2015" becomes "ANSI/NISO Z39.96-2019"
     
   ==============================================================
     JATS Version 1.2 (ANSI/NISO Z39.96-2018)
     
                                   (DAL/BTU) v1.2     (2018-11-30)
    
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the both the NISO and ANSI membership 
     to be changed. This version of the NISO JATS was approved 
     by ANSI and NISO vote, so it supersedes all Committee Drafts
     and becoming NISO JATS 1.2 (ANSI/NISO Z39.96-2018).
  
 18. 
     JATS "1.2d2" and "v1.2d2 20180401" became
     JATS "1.2" and "v1.2 20181130" 

    =============================================================
     
     JATS Version 1.2d2              (DAL/BTU) v1.2d2  (2018-04-01)

     JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
     BITS is under continuous maintenance, and is modified at the
     discretion of the working group.
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.

 17. BITS "2.0" and "v2.0 20151225" remain unchanged
      
   
     JATS "1.2d1" and "v1.2d1 20171231" became
     JATS "1.2d2" and "v1.2d2 20180401". 

     No module names were changed.
 
    =============================================================
     JATS Version 1.2d1 (ANSI/NISO Z39.96-2015)
                                   (DAL/BTU) v1.2d1  (2017-12-31)
 
     NISO JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
  
 16. JATS "1.2d1" and "v1.2d1 20170631" became
     JATS "1.2d1" and "v1.2d1 20171231". 

    =============================================================
     JATS Version 1.2d1              (DAL/BTU) v1.2d1  (2017-06-31)

     NISO JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
   
 15. TERM ATTRIBUTES - Added to <term> the following attributes:
     - @term-type
     - @term-status
     - the four vocabulary attributes described below
   
 14. VOCABULARY/TAXONOMY ATTRIBUTES - Added 4 new attributes for
     naming (and possibly linking to) a general or controlled
     taxonomy, vocabulary, index, database or other source of
     terms: @vocab, @vocab-identifier, @vocab-term, and
     @vocab-term-identifier
      - All 4 added to <term>, to name a vocabulary 
        as the source of the term being identified
 
 13. JATS became version "1.2d1" and "v1.2d1 20170631" 

     =============================================================
     JATS Version 1.1                (DAL/BTU) v1.1  (2015-12-15)

     JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
     JATS 1.1 was approved in late 2015, so the formal public 
     identifiers were changed in the modules and the catalogs.
     No model or attribute changes were made at this time. 
 
 12. JATS became version "1.1" and "v1.1 20151215"
     BITS remained version "2.0" but becomes "v2.0 20151225"

     =============================================================
     JATS Version 1.1d3             (DAL/BTU) v1.1d3  (2015-03-01)
   
     The changes in this release are in response to NISO 
     License and Indicators Recommended Practice.

     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the NISO membership to be changed. This
     Committee Draft 1.1d3 will be sent to the NISO voting
     membership, to become (if approved) NISO JATS 1.1.
     
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee.

 11. JATS became version "1.1d3" and "v1.1 20150301//EN"

     =============================================================
     JATS Version 1.1d2              (DAL/BTU) v1.1d2 (2014-09-30)
   
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the NISO membership to be changed. This
     Committee Draft 1.1d2 will be sent to the NISO voting
     membership, to become (if approved) NISO JATS 1.1.
     
     This catalog represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee.
 
 10. TITLE FOR LIST ITEMS
     Added an optional <title> to the beginning of each <list-item>.

  9. JATS became version "1.1d2" and "v1.1d2 20140930//EN"

   
     =============================================================
     NISO JATS Version 1.1d1           (DAL/BTU) v1.1 (2013-11-15)
   
     ANSI/NISO Z39.96-2012 (pre-release for V1.0 BITS; Version 1.1d1) 

     NISO JATS Standing Committee met and answered the requests and
     suggestions from the NISO request forms.

     Details concerning ANSI/NISO Z39.96-2012 JATS-based DTDs,  
     XSDs, RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.3d2/

  8. GLOBAL ATTRIBUTES - Added the new parameter entity 
         %jats-common-atts;
     to every element in this module. This PE adds (for now) the
     @id attribute and the @xml:base attribute to every element,
     whether metadata or narrative.
     Since the @id in this parameter entity is optional, a second
     parameter entity jats-common-atts-id-required was also added.
     The two are kept in sync with the jats-base-atts parameter 
     entity.
     This added a new attribute list to:
      - def-head
      - term-head

     =============================================================
     NISO JATS Version 1.0             (DAL/BTU) v1.0 (2012-xx-xx)
   
     ANSI/NISO Z39.96-2012 (Version 1.0) 

     Details concerning ANSI/NISO Z39.96-2012 JATS-based DTDs,  
     XSDs, RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.3d2/
   
  7. Updated the DTD-version attribute to "1.0" and the formal
     public identifier to the date: "v1.0 20120330//EN".

     =============================================================
     Version 0.4                       (DAL/BTU) v0.4 (2011-01-31)
   
     This Tag Set is in the process of becoming a NISO standard.
     The version numbers are starting over from 0.4", as a Trial
     Use Draft, to be made into "Version 1.0" when the Tag Suite 
     becomes a NISO standard. Thus, the version number that would
     have been "NLM Version 3.1 Draft" has become NISO JATS
     "Version 0.4".

     Details on NISO Trial Use Draft Version 0.4 are available at
           http://jats.nlm.nih.gov/JATS-0.4.
   
  6. Updated the DTD-version attribute to "0.4" 
   
  5. Updated the public identifier to "v0.4 20110131", 
     modified the formal public identifier to include "JATS (Z39.96)",
     and the filename as delivered to include "JATS" and the
     new version number "0".

  4. @SPECIFIC-USE and @XML:LANG - Added the @specific-use and
     @xml:lang to the following elements:

      - def-item through def-item-atts (both)
      - def-list through def-list-atts (@xml:lang-only;
          @specific-use already)
      - list through list-atts (@xml:lang-only; @specific-use already)
      - list-item through list-item-atts (both)
      - term through term-atts (both)

  3. DEFINITION LIST HEADS - Removed the dependency in which
     <term-head> and <def-head> both used the parameter entity
     %def-list-head-elements; One uses %term-head-elements; and
     the other uses %def-head-elements; But the
     %def-list-head-elements; was retained for backward
     compatibility.

  2. DEF LIST ATTRIBUTES - Removed the dependency whereby <def-list>
     used both %def-list-atts; and %list-atts;. <def-list> now uses
     only %def-list-atts; No documents need change.
     *****Customization Alert: New parameter entity could break some
     customizations. Check your <def-list> attributes.        *****

     =============================================================
     Version 3.0                       (DAL/BTU) v3.0 (2007-10-31)

     Version 3.0 is the first non-backward-compatible release.
     In addition to the usual incremental changes, some
     elements and attributes have been renamed and/or remodeled
     to better meet user needs and to increase tag set consistency.
     All module change histories are available through the Tag Suite
     web site at http://dtd.nlm.nih.gov.

     Details on version 3.0 are available at
           http://dtd.nlm.nih.gov/3.0.

  1. Updated public identifier to "v3.0 20071031//EN"              -->

<!-- ============================================================= -->
<!-- Details concerning 
     ANSI/NISO Z39.96-2015 JATS-based DTDs, XSDs, 
     RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.1/                            -->

<!-- ============================================================= -->
<!--                    DEFAULT PE FOR ATTRIBUTE LISTS             -->
<!-- ============================================================= -->


<!--                    DEFINITION LIST: DEFINITION HEAD ATTRIBUTES-->
<!--                    Attributes for the <def-head> element      -->
<!ENTITY % def-head-atts
            "%jats-common-atts;"                                     >


<!--                    DEFINITION LIST: DEFINITION ITEM ATTRIBUTES-->
<!--                    Attributes for the <def-item> element      -->
<!ENTITY % def-item-atts
            "%jats-common-atts;                                       
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    DEFAULT DEFINITION LIST ATTRIBUTES         -->
<!--                    Default attribute lists to be used for
                        Definition (2-part) lists                  -->
<!ENTITY % def-list-atts
            "%jats-common-atts;                                       
             list-type  CDATA                             #IMPLIED
             prefix-word
                        CDATA                             #IMPLIED
             list-content
                        CDATA                             #IMPLIED
             continued-from
                        IDREF                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    DEFAULT LIST CLASS ATTRIBUTES              -->
<!--                    Default attribute lists to be used for most
                        of the types of lists.                     -->
<!ENTITY % list-atts
            "%jats-common-atts;                                       
             list-type  CDATA                             #IMPLIED
             prefix-word
                        CDATA                             #IMPLIED
             list-content
                        CDATA                             #IMPLIED
             continued-from
                        IDREF                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    DEFAULT LIST ITEM ATTRIBUTES               -->
<!--                    Default attribute for list items           -->
<!ENTITY % list-item-atts
            "%jats-common-atts;                                       
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    DEFINITION LIST: TERM ATTRIBUTES           -->
<!--                    Attributes for the <term> element          -->
<!ENTITY % term-atts
            "%jats-common-atts;                                       
             rid        IDREFS                            #IMPLIED
             term-status
                        CDATA                             #IMPLIED
             term-type  CDATA                             #IMPLIED
             vocab      CDATA                             #IMPLIED
             vocab-identifier
                        CDATA                             #IMPLIED
             vocab-term CDATA                             #IMPLIED
             vocab-term-identifier
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    DEFINITION LIST: TERM HEAD ATTRIBUTES      -->
<!--                    Attributes for the <term-head> element     -->
<!ENTITY % term-head-atts
            "%jats-common-atts;"                                     >


<!-- ============================================================= -->
<!--                    DEFINITION LIST                            -->
<!-- ============================================================= -->


<!--                    DEFINITION LIST MODEL                      -->
<!--                    Content model for the <def-list> element   -->
<!ENTITY % def-list-model
                        "(label?, title?, term-head?, def-head?,
                          (%def-item.class;)*, (%def-list.class;)* )">


<!--                    DEFINITION LIST                            -->
<!--                    Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=def-list
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=def-list
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=def-list
                                                                   -->
<!ELEMENT  def-list     %def-list-model;                             >
<!ATTLIST  def-list
             %def-list-atts;                                         >


<!--                    DEFINITION LIST HEAD ELEMENTS              -->
<!--                    Elements for use in the <def-list> heading
                        elements <term-head> and <def-head>.
                        Design Note: All inline mixes begin with an
                        OR bar, but since %simple-phrase; is an
                        inline mix, the OR bar is already there.   -->
<!ENTITY % def-list-head-elements
                        "%simple-phrase;"                            >


<!--                    DEFINITION LIST TERM HEAD ELEMENTS         -->
<!--                    Elements for use in the <term-head> element-->
<!ENTITY % term-head-elements
                        "%def-list-head-elements;"                   >


<!--                    DEFINITION LIST DEFINITION HEAD ELEMENTS   -->
<!--                    Elements for use in the <def-head> element -->
<!ENTITY % def-head-elements
                        "%def-list-head-elements;"                   >


<!--                    DEFINITION LIST: TERM HEAD                 -->
<!--                    Title over the first (term) column of a
                        two-part list
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=term-head
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=term-head
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=term-head
                                                                   -->
<!ELEMENT  term-head    (#PCDATA %term-head-elements;)*              >
<!ATTLIST  term-head
             %term-head-atts;                                        >


<!--                    DEFINITION LIST: DEFINITION HEAD           -->
<!--                    Title over the second (definition) column
                        of a two-part list
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=def-head
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=def-head
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=def-head
                                                                   -->
<!ELEMENT  def-head     (#PCDATA %def-head-elements;)*               >
<!ATTLIST  def-head
             %def-head-atts;                                         >


<!--                    DEFINITION LIST: DEFINITION ITEM MODEL     -->
<!--                    The content model of a <def-item>          -->
<!ENTITY % def-item-model
                        "(label?, term, def*)"                       >


<!--                    DEFINITION LIST: DEFINITION ITEM           -->
<!--                    A term and definition pair inside a
                        definition or two-part list
                        of a two-part list
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=def-item
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=def-item
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=def-item
                                                                   -->
<!ELEMENT  def-item     %def-item-model;                             >
<!ATTLIST  def-item
             %def-item-atts;                                         >


<!--ELEM   def          Defined in %common.ent;                    -->


<!--                    DEFINITION LIST: TERM ELEMENTS             -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        a <term>.
                        Design Note: All inline mixes begin with an
                        OR bar, but since %simple-phrase; is an
                        inline mix, the OR bar is already there.   -->
<!ENTITY % term-elements
                        "%simple-phrase; | %block-math.class; |
                         %simple-display-noalt.class;"               >


<!--                    DEFINITION LIST: TERM                      -->
<!--                    The word, phrase, picture, or other noun
                        being defined or description that occupies
                        the first column of a definition or 2-part
                        list and is the subject of the definition or
                        description.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=term
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=term
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=term
                                                                   -->
<!ELEMENT  term         (#PCDATA %term-elements;)*                   >
<!ATTLIST  term
             %term-atts;                                             >


<!-- ============================================================= -->
<!--                    LIST ELEMENTS (PARAGRAPH-LEVEL ELEMENTS)   -->
<!-- ============================================================= -->


<!--                    LIST MODEL                                 -->
<!--                    Content model for the <list> element       -->
<!ENTITY % list-model   "((%id.class;)*, label?, title?, 
                          (%list-item.class;)+)"                     >


<!--                    LIST                                       -->
<!--                    Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=list
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=list
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=list
                                                                   -->
<!ELEMENT  list         %list-model;                                 >
<!ATTLIST  list
             %list-atts;                                             >


<!--                    LIST ITEM ELEMENTS                         -->
<!--                    The content model of a <list-item>.        -->
<!ENTITY % list-item-model
                        "(label?, title?, 
                          (%just-para.class; | %list.class;)+ )"     >


<!--                    LIST ITEM                                  -->
<!--                    One item in a list
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=list-item
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=list-item
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=list-item
                                                                   -->
<!ELEMENT  list-item    %list-item-model;                            >
<!ATTLIST  list-item
             %list-item-atts;                                        >


<!-- ================== End List Class Module ==================== -->

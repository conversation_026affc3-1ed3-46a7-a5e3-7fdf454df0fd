<!--    
                This is the Current DTD for Entrez eLink
$Id: eLink_090910.dtd 170412 2009-09-11 21:29:34Z fialkov $
-->
<!-- ================================================================= -->

<!ELEMENT       ERROR           (#PCDATA)>	<!-- .+ -->
<!ELEMENT       Info                   (#PCDATA)>	<!-- .+ -->

<!ELEMENT	Id	           (#PCDATA)>	<!-- \d+ -->
<!ATTLIST	Id		
			HasLinkOut  (Y|N)	#IMPLIED	
			HasNeighbor (Y|N)	#IMPLIED
			>
<!ELEMENT	Score		(#PCDATA)>	<!-- \d+ -->
<!ELEMENT	DbFrom	(#PCDATA)>	<!-- \S+ -->
<!ELEMENT	DbTo		(#PCDATA)>	<!-- \S+ -->
<!ELEMENT	LinkName	(#PCDATA)>	<!-- \S+ -->
<!ELEMENT	WebEnv	(#PCDATA)>	<!-- \S+ -->
<!ELEMENT       MenuTag             (#PCDATA)>      <!-- \S+ -->
<!ELEMENT       HtmlTag              (#PCDATA)>      <!-- \S+ -->
<!ELEMENT       Priority                 (#PCDATA)>      <!-- \S+ -->


<!ELEMENT	IdList		(Id*)>

<!-- cmd=neighbor -->
<!ELEMENT	Link		(Id, Score?)>
<!ELEMENT	QueryKey	(#PCDATA)>

<!ELEMENT	LinkSetDb	               (DbTo, LinkName, (Link*|Info), ERROR?)>
<!ELEMENT	LinkSetDbHistory	(DbTo, LinkName, (QueryKey|Info), ERROR?)>

<!-- cmd=llinks -->

<!ELEMENT	Url		(#PCDATA)>	<!-- \S+ -->
<!ELEMENT	IconUrl		(#PCDATA)>	<!-- \S+ -->
<!ELEMENT       SubjectType	(#PCDATA)>	<!-- .+ -->
<!ELEMENT       Category	(#PCDATA)>	<!-- .+ -->
<!ELEMENT       Attribute               (#PCDATA)>	<!-- .+ -->
<!ELEMENT       Name		(#PCDATA)>	<!-- .+ -->
<!ELEMENT       NameAbbr	(#PCDATA)>	<!-- \S+ -->

<!ELEMENT	Provider (
				Name,
				NameAbbr,
				Id,
				Url,
				IconUrl?
			)>


<!ELEMENT	ObjUrl	(
				Url,
				IconUrl?,
				LinkName?,
                                SubjectType*,
                                Category*,
                                Attribute*,
                               Provider			
			)>

<!ELEMENT	IdUrlSet	(Id,(ObjUrl+|Info))>

<!ELEMENT	IdUrlList	(IdUrlSet*,ERROR?)>

<!ELEMENT       LinkInfo        (DbTo, LinkName, MenuTag?, HtmlTag?, Url?, Priority)>
<!ELEMENT       IdLinkSet       (Id, LinkInfo*)>

<!-- cmd=ncheck & lcheck & acheck -->
<!ELEMENT	IdCheckList	((Id|IdLinkSet)*,ERROR?)>


<!-- Common -->
<!ELEMENT	LinkSet		(DbFrom, 
				((IdList?, (LinkSetDb*  |  (LinkSetDbHistory*, WebEnv))) | IdUrlList | IdCheckList | ERROR)  
				)>

<!ELEMENT	eLinkResult	(LinkSet*, ERROR?)>

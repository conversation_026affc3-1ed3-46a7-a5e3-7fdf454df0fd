import os
import sys
import sys
import os
from datetime import datetime

# 将当前目录添加到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 先导入模拟的 LLMManager
from test_llm_manager import LLMManager

# 然后导入其他模块
from ebm_generator import EBMGenerator, PROMPTS_CN, PROMPTS_EN

def test_report_saving():
    print("=== 开始测试报告保存功能 ===")
    
    # 初始化模拟的 LLM 管理器
    llm_manager = LLMManager()
    
    # 初始化生成器
    generator = EBMGenerator(llm_manager)
    
    # 创建测试输出目录
    test_output_dir = os.path.join("test_output")
    os.makedirs(test_output_dir, exist_ok=True)
    print(f"测试输出目录: {os.path.abspath(test_output_dir)}")
    
    # 测试数据
    test_topic = "测试报告生成"
    test_articles = [
        {
            "title": "测试文章1",
            "abstract": "这是一个测试文章的摘要",
            "authors": ["作者1", "作者2"],
            "year": "2023",
            "journal": "测试期刊"
        },
        {
            "title": "测试文章2",
            "abstract": "这是另一个测试文章的摘要",
            "authors": ["作者3", "作者4"],
            "year": "2024",
            "journal": "测试期刊"
        }
    ]
    
    # 创建完整的 prompts 字典
    test_prompts_cn = {
        **PROMPTS_CN,
        'thematic_grouping': '请将以下研究按主题分组：',
        'title_generation': '请为以下研究生成一个专业的标题：',
        'quality_assessment': '请评估以下研究的质量：',
        'narrative_synthesis': '请为以下研究生成叙述性综合：',
        'conclusion': '请总结研究结论：'
    }
    
    # 测试中文报告
    print("\n=== 测试中文报告生成 ===")
    cn_report = generator._generate_single_report(
        test_topic, 
        test_articles, 
        "ebm", 
        test_prompts_cn,  # 使用更新后的 prompts
        "openai",
        "gpt-4"
    )
    
    if not cn_report or "ERROR" in cn_report:
        print("❌ 中文报告生成失败")
        print(f"错误信息: {cn_report}")
        return False
    
    # 创建英文 prompts 字典
    test_prompts_en = {
        **PROMPTS_EN,
        'thematic_grouping': 'Please group the following studies by theme:',
        'title_generation': 'Please generate a professional title for the following research:',
        'quality_assessment': 'Please assess the quality of the following study:',
        'narrative_synthesis': 'Please generate a narrative synthesis for the following studies:',
        'conclusion': 'Please summarize the research conclusions:'
    }
    
    # 测试英文报告
    print("\n=== 测试英文报告生成 ===")
    en_report = generator._generate_single_report(
        test_topic,
        test_articles,
        "ebm",
        test_prompts_en,  # 使用更新后的 prompts
        "openai",
        "gpt-4"
    )
    
    if not en_report or "ERROR" in en_report:
        print("❌ 英文报告生成失败")
        print(f"错误信息: {en_report}")
        return False
    
    # 测试报告保存
    print("\n=== 测试报告保存 ===")
    try:
        # 保存中文报告
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        cn_md_file = os.path.join(test_output_dir, f"{timestamp}_test_report_cn.md")
        with open(cn_md_file, 'w', encoding='utf-8') as f:
            f.write(cn_report)
        print(f"✅ 中文Markdown报告已保存到: {os.path.abspath(cn_md_file)}")
        
        # 保存英文报告
        en_md_file = os.path.join(test_output_dir, f"{timestamp}_test_report_en.md")
        with open(en_md_file, 'w', encoding='utf-8') as f:
            f.write(en_report)
        print(f"✅ 英文Markdown报告已保存到: {os.path.abspath(en_md_file)}")
        
        # 生成HTML报告
        try:
            # 中文HTML报告
            cn_html_file = cn_md_file.replace('.md', '.html')
            generator.convert_markdown_to_html(
                markdown_file=cn_md_file,
                output_file=cn_html_file,
                title=f"测试报告 - 中文 - {timestamp}",
                is_chinese=True
            )
            print(f"✅ 中文HTML报告已生成: {os.path.abspath(cn_html_file)}")
            
            # 英文HTML报告
            en_html_file = en_md_file.replace('.md', '.html')
            generator.convert_markdown_to_html(
                markdown_file=en_md_file,
                output_file=en_html_file,
                title=f"Test Report - English - {timestamp}",
                is_chinese=False
            )
            print(f"✅ 英文HTML报告已生成: {os.path.abspath(en_html_file)}")
            
        except Exception as e:
            print(f"⚠️ 生成HTML报告时出错: {str(e)}")
            print("请确保已安装 weasyprint: pip install weasyprint")
        
        # 检查文件是否生成
        files_to_check = [
            (cn_md_file, "中文Markdown报告"),
            (en_md_file, "英文Markdown报告"),
            (cn_md_file.replace('.md', '.html'), "中文HTML报告"),
            (en_md_file.replace('.md', '.html'), "英文HTML报告")
        ]
        
        all_files_ok = True
        for file_path, file_desc in files_to_check:
            if os.path.exists(file_path) and os.path.getsize(file_path) > 0:
                print(f"✅ {file_desc}检查通过: {file_path}")
            else:
                print(f"❌ {file_desc}检查失败: {file_path}")
                all_files_ok = False
        
        return all_files_ok
        
    except Exception as e:
        print(f"❌ 报告保存过程中出错: {str(e)}")
        return False

if __name__ == "__main__":
    # 设置工作目录为脚本所在目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    # 运行测试
    success = test_report_saving()
    
    if success:
        print("\n✅ 所有测试通过！")
        sys.exit(0)
    else:
        print("\n❌ 测试失败！")
        sys.exit(1)

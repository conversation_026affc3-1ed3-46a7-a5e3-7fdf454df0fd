<!-- ============================================================= -->
<!--  MODULE:    Paragraph-Like Elements                           -->
<!--  VERSION:   ANSI/NISO JATS Version 1.3 (Z39.96-2021)          -->
<!--  DATE:      June 2021                                         -->
<!--                                                               -->
<!-- ============================================================= -->

<!-- ============================================================= -->
<!--                    PUBLIC DOCUMENT TYPE DEFINITION            -->
<!--                        TYPICAL INVOCATION                     -->
<!--
"-//NLM//DTD JATS (Z39.96) JATS DTD Suite Paragraph-Like Elements v1.3 20210610//EN"
     Delivered as file "JATS-para1-3.ent"                          -->
<!-- ============================================================= -->

<!-- ============================================================= -->
<!-- SYSTEM:     JATS DTD Suite                                    -->
<!--                                                               -->
<!-- PURPOSE:    Names structural elements that will appear in     -->
<!--             the same places as a paragraph                    -->
<!--                                                               -->
<!-- TAG SET SPONSOR                                               -->
<!--             National Center for Biotechnology                 -->
<!--                Information (NCBI)                             -->
<!--             National Library of Medicine (NLM)                -->
<!--                                                               -->
<!-- CREATED FOR:                                                  -->
<!--             This module was created for the JATS DTD Suite.   -->
<!--             Digital archives and publishers may use the       -->
<!--             DTD as is for markup of journal literature or     -->
<!--             related material for archiving and transferring   -->
<!--             such material between archives or create a        -->
<!--             custom XML DTD from the Suite for                 -->
<!--             these purposes.                                   -->
<!--                                                               -->
<!--             This DTD is in the public domain. An organization -->
<!--             that wishes to create its own DTD from the suite  -->
<!--             may do so without permission from NLM.            -->
<!--                                                               -->
<!--             The suite has been set up to be extended using a  -->
<!--             new DTD file and a new DTD-specific customization -->
<!--             module to redefine the many Parameter Entities.   -->
<!--             Do not modify the suite directly or redistribute  -->
<!--             modified versions of the suite.                   -->
<!--                                                               -->
<!--             In the interest of maintaining consistency and    -->
<!--             clarity for potential users, NLM requests:        -->
<!--                                                               -->
<!--             1. If you create a DTD from the JATS DTD Suite    -->
<!--                and intend to stay compatible with the suite,  -->
<!--                then please include the following statement    -->
<!--                as a comment in all of your DTD modules:       -->
<!--                   "Created from, and fully compatible with,   -->
<!--                    the ANSI/NISO Z39.96 Journal Article Tag   -->
<!--                    Suite (JATS)."                             -->
<!--                                                               -->
<!--             2. If you alter one or more modules of the suite, -->
<!--                then please rename your version and all its    -->
<!--                modules to avoid any confusion with the        -->
<!--                original suite. Also, please include the       -->
<!--                following statement as a comment in all your   -->
<!--                DTD modules:                                   -->
<!--                   "Based in part on, but not fully compatible -->
<!--                    with, the ANSI/NISO Z39.96 Journal Article -->
<!--                    Tag Suite (JATS)."                         -->
<!--                                                               -->
<!-- ORIGINAL CREATION DATE:                                       -->
<!--             December 2002                                     -->
<!--                                                               -->
<!-- CREATED BY: Mulberry Technologies, Inc. for the NISO Z39.96   -->
<!--             Working Group. Mulberry Technologies was          -->
<!--             supported by National Center for Biotechnology    -->
<!--             Information (NCBI), a center of the US National   -->
<!--             Library of Medicine (NLM).                        -->
<!--                                                               -->
<!--             This module is part of the JATS DTD Suite. The    -->
<!--             Suite is a continuation of work done by NCBI,     -->
<!--             Mulberry Technologies, and Inera Inc. on the NLM  -->
<!--             Journal Archiving and Interchange DTD Suite, which-->
<!--             was originally released in December, 2002.        -->
<!--                                                               -->
<!--             NLM thanks the Harvard University Libraries, both -->
<!--             for proposing that a draft archiving NLM DTD for  -->
<!--             life sciences journals be extended to accommodate -->
<!--             journals in all disciplines and for sponsoring    -->
<!--             Inera Inc.'s collaboration with other DTD         -->
<!--             authors in completing NLM Version 1.0. The        -->
<!--             Andrew W. Mellon Foundation provided support for  -->
<!--             these important contributions.                    -->
<!--                                                               -->
<!--             Suggestions for refinements and enhancements to   -->
<!--             this DTD should be sent in email to:              -->
<!--                 <EMAIL>                         -->
<!-- ============================================================= -->


<!-- ============================================================= -->
<!--                    DTD VERSION/CHANGE HISTORY                 -->
<!-- ============================================================= -->
<!--
Version  Reason/Occasion                   (who) vx.x (yyyy-mm-dd)
   ==============================================================
     JATS Version 1.3 (ANSI/NISO Z39.96-2021)
                                  (DAL/BTU) v1.3 (2021-06-10)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by both the NISO and ANSI memberships 
     to be changed. This version of the NISO JATS was approved 
     by ANSI vote on June 10, 2021, so it supersedes all Committee 
     Drafts as well as the draft version JATS 1.3 2021 (the version
     submitted for vote), becoming NISO JATS v1.3 20210610 
     (ANSI/NISO Z39.96-2019).

     The file names and formal public identifiers have been 
     changed in the modules and the catalogs.

 25. JATS 'v1.3d2 20201130' becomes 'v1.3 20210610' 
     (final ANSI vote date)
     'ANSI/NISO Z39.96-2019' becomes 'ANSI/NISO Z39.96-2021'

   ==============================================================
     JATS Version 1.3d2 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.3d2 (2020-11-30)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by the NISO and ANSI memberships 
     to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     of the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting; it is for Standing Committee review and
     testing. The formal public identifiers have been changed 
     in the modules and the catalogs.

 24. OBJECT ID Added <object-id> to: 
        <speech>

 23. JATS "v1.3d2 20201130" becomes 'v1.3 20210610' (second 
     committee draft following the final ANSI/NISO 1.2 vote)

   ==============================================================
     JATS Version 1.3d1 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.3.d1 (2019-08-31)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by the NISO and ANSI memberships 
     to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     of the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
      
 22. ADDED SUBJECTS TO WHEREVER KEYWORDS ALLOWED - Added
     <subj-group> directly to the following elements:
      - statement
  
 21. JATS "1.2" becomes "v1.3d1 20190831" (first committee
     draft following the final ANSI/NISO vote) 

   ==============================================================
     JATS Version 1.2 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.2     (2019-02-08)
    
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the both the NISO and ANSI membership 
     to be changed. This version of the NISO JATS was approved 
     by ANSI vote on Feb 08, 2019, so it supersedes all Committee 
     Drafts as well as the draft version JATS 1.2 2018 (the version
     submitted for vote), becoming NISO JATS v1.2 20190208 
     (ANSI/NISO Z39.96-2021).
  
 20. JATS "1.2" becomes "v1.2 20190208" (final ANSI vote date)
     "ANSI/NISO Z39.96-2015" becomes "ANSI/NISO Z39.96-2019"
     
   ==============================================================
     JATS Version 1.2 (ANSI/NISO Z39.96-2018)
     
                                   (DAL/BTU) v1.2     (2018-11-30)
    
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the both the NISO and ANSI membership 
     to be changed. This version of the NISO JATS was approved 
     by ANSI and NISO vote, so it supersedes all Committee Drafts
     and becoming NISO JATS 1.2 (ANSI/NISO Z39.96-2018).
  
 19. 
     JATS "1.2d2" and "v1.2d2 20180401" became
     JATS "1.2" and "v1.2 20181130" 

    =============================================================
     
     JATS Version 1.2d2              (DAL/BTU) v1.2d2  (2018-04-01)

     JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
     BITS is under continuous maintenance, and is modified at the
     discretion of the working group.
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
     
 18. BITS "2.0" and "v2.0 20151225" remain unchanged
      
   
     JATS "1.2d1" and "v1.2d1 20171231" became
     JATS "1.2d2" and "v1.2d2 20180401". 

     No module names were changed.
 
    =============================================================
     JATS Version 1.2d1 (ANSI/NISO Z39.96-2015)
                                   (DAL/BTU) v1.2d1  (2017-12-31)
 
     NISO JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
  
 17. JATS "1.2d1" and "v1.2d1 20170631" became
     JATS "1.2d1" and "v1.2d1 20171231". 

    =============================================================
     JATS Version 1.2d1              (DAL/BTU) v1.2d1  (2017-06-31)

     NISO JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
    
 16. NEW VERSE ATTRIBUTES
     - Added @indent-level, @style, @style-type, and @style-detail 
       to <verse-line>
     - Added @style, @style-type, and @style-detail to <verse-group>      
    
 15. JATS became version "1.2d1" and "v1.2d1 20170631" 

     =============================================================
     JATS Version 1.1                (DAL/BTU) v1.1  (2015-12-15)

     JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
     JATS 1.1 was approved in late 2015, so the formal public 
     identifiers were changed in the modules and the catalogs.
     No model or attribute changes were made at this time. 
 
 14. JATS became version "1.1" and "v1.1 20151215"
     BITS remained version "2.0" but becomes "v2.0 20151225"

     =============================================================
     JATS Version 1.1d3             (DAL/BTU) v1.1d3  (2015-03-01)
   
     The changes in this release are in response to NISO 
     License and Indicators Recommended Practice.

     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the NISO membership to be changed. This
     Committee Draft 1.1d3 will be sent to the NISO voting
     membership, to become (if approved) NISO JATS 1.1.
     
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee.

 13. JATS became version "1.1d3" and "v1.1 20150301//EN"

     =============================================================
     JATS Version 1.1d2              (DAL/BTU) v1.1d2 (2014-09-30)
   
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the NISO membership to be changed. This
     Committee Draft 1.1d2 will be sent to the NISO voting
     membership, to become (if approved) NISO JATS 1.1.
     
     This catalog represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee.

 12. STATEMENT - Added new statement.class inside the model
     for <statement>, so that statements can be recursive.

 11. JATS became version "1.1d2" and "v1.1d2 20140930//EN"

   
     =============================================================
     NISO JATS Version 1.1d1           (DAL/BTU) v1.1 (2013-11-15)
   
     ANSI/NISO Z39.96-2012 (pre-release for V1.0 BITS; Version 1.1d1) 

     NISO JATS Standing Committee met and answered the requests and
     suggestions from the NISO request forms.

     Details concerning ANSI/NISO Z39.96-2012 JATS-based DTDs,  
     XSDs, RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.3d2/

 10. GLOBAL ATTRIBUTES - Added the new parameter entity 
         %jats-common-atts;
     to every element in this module. This PE adds (for now) the
     @id attribute and the @xml:base attribute to every element,
     whether metadata or narrative.
     Since the @id in this parameter entity is optional, a second
     parameter entity jats-common-atts-id-required was also added.
     The two are kept in sync with the jats-base-atts parameter 
     entity.


  9. ABSTRACTS AND KEYWORDS ON NEW STRUCTURES
     - Added <abstract> (through %abstract.class;) and <kwd-group> 
       (through %kwd-group.class;) to the following elements:
        - statement (through statement-model PE)
   
     =============================================================
   
     =============================================================
     NISO JATS Version 1.0             (DAL/BTU) v1.0 (2012-xx-xx)
   
     ANSI/NISO Z39.96-2012 (Version 1.0) 

     Details concerning ANSI/NISO Z39.96-2012 JATS-based DTDs,  
     XSDs, RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.3d2/
   
  5. Updated the DTD-version attribute to "1.0" and the formal
     public identifier to the date: "v1.0 20120330//EN".

     =============================================================
     Version 0.4                       (DAL/BTU) v0.4 (2011-01-31)
   
     This Tag Set is in the process of becoming a NISO standard.
     The version numbers are starting over from 0.4", as a Trial
     Use Draft, to be made into "Version 1.0" when the Tag Suite 
     becomes a NISO standard. Thus, the version number that would
     have been "NLM Version 3.1 Draft" has become NISO JATS
     "Version 0.4".

     Details on NISO Trial Use Draft Version 0.4 are available at
           http://jats.nlm.nih.gov/JATS-0.4.
   
  4. Updated the DTD-version attribute to "0.4" 
   
  3. Updated the public identifier to "v0.4 20110131", 
     modified the formal public identifier to include "JATS (Z39.96)",
     and the filename as delivered to include "JATS" and the
     new version number "0".

  2. @SPECIFIC-USE and @XML:LANG - Added the @specific-use and
     @xml:lang to the following elements:
      - speaker through speaker-atts (both)
      - statement through statement-atts (@xml:lang only; already
          @specific-use))
      - verse-line through verse-line-atts (both)

     =============================================================
     Version 3.0                       (DAL/BTU) v3.0 (2007-10-31)

     Version 3.0 is the first non-backward-compatible release.
     In addition to the usual incremental changes, some
     elements and attributes have been renamed and/or remodeled
     to better meet user needs and to increase tag set consistency.
     All module change histories are available through the Tag Suite
     web site at http://dtd.nlm.nih.gov.

     Details on version 3.0 are available at
           http://dtd.nlm.nih.gov/3.0.

  1. Updated public identifier to "v3.0 20071031//EN"              -->

<!-- ============================================================= -->
<!-- Details concerning 
     ANSI/NISO Z39.96-2015 JATS-based DTDs, XSDs, 
     RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.1/                            -->

<!-- ============================================================= -->
<!--                    PARAMETER ENTITIES FOR ATTRIBUTE LISTS     -->
<!-- ============================================================= -->


<!--                    DISPLAY QUOTE ATTRIBUTES                   -->
<!--                    Attribute list for the <disp-quote> element-->
<!ENTITY % disp-quote-atts
            "%jats-common-atts;                                       
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    PARAGRAPH ATTRIBUTES                       -->
<!--                    Attribute list for the <p> element         -->
<!ENTITY % p-atts
            "%jats-common-atts;                                       
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    SPEAKER ATTRIBUTES                         -->
<!--                    Attribute list for the <speaker> element   -->
<!ENTITY % speaker-atts
            "%jats-common-atts;                                       
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    SPEECH ATTRIBUTES                          -->
<!--                    Attribute list for the <speech> element    -->
<!ENTITY % speech-atts
            "%jats-common-atts;                                       
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    STATEMENT ATTRIBUTES                       -->
<!--                    Attribute list for the <statement> element -->
<!ENTITY % statement-atts
            "%jats-common-atts;                                       
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    VERSE GROUP ATTRIBUTES                     -->
<!--                    Attribute list for the <verse-group> element
                                                                   -->
<!ENTITY % verse-group-atts
            "%jats-common-atts;                                       
             style      CDATA                             #IMPLIED
             style-detail
                        CDATA                             #IMPLIED
             style-type CDATA                             #IMPLIED
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    VERSE LINE ATTRIBUTES                      -->
<!--                    Attribute list for the <verse-line> element-->
<!ENTITY % verse-line-atts
            "%jats-common-atts;                                       
             indent-level
                        CDATA                             #IMPLIED
             style      CDATA                             #IMPLIED
             style-detail
                        CDATA                             #IMPLIED
             style-type CDATA                             #IMPLIED
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!-- ============================================================= -->
<!--                    PARAGRAPH-LEVEL ELEMENTS                   -->
<!-- ============================================================= -->


<!--                    PARAGRAPH                                  -->
<!--                    The basic block-unit of textual information
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=p
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=p
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=p
                                                                   -->
<!ELEMENT  p            (#PCDATA %p-elements;)*                      >
<!ATTLIST  p
             %p-atts;                                                >


<!-- ============================================================= -->
<!--                    THE REST OF THE PARAGRAPH ELEMENTS         -->
<!-- ============================================================= -->


<!--                    QUOTE, DISPLAYED MODEL                     -->
<!--                    Content model for the Display Quote element-->
<!ENTITY % disp-quote-model
                        "(label?, title?, (%para-level;)*,
                          (%display-back-matter.class;)* )"          >


<!--                    QUOTE, DISPLAYED                           -->
<!--                    Extract or extended quoted passage from
                        another work, usually made typographically
                        distinct from the surrounding text.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=disp-quote
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=disp-quote
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=disp-quote
                                                                   -->
<!ELEMENT  disp-quote   %disp-quote-model;                           >
<!ATTLIST  disp-quote
             %disp-quote-atts;                                       >


<!--                    SPEECH MODEL                               -->
<!--                    Content model for the <speech> element     -->
<!ENTITY % speech-model "((%id.class;)*, speaker, 
                          (%just-para.class;)+ )"                    >


<!--                    SPEECH                                     -->
<!--                    One exchange in a real or imaginary
                        conversation between two or more entities,
                        for example, between a an interviewer and the
                        person being interviewed, between a nurse or
                        doctor and a patient, between a person and a
                        computer, etc.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=speech
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=speech
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=speech
                                                                   -->
<!ELEMENT  speech       %speech-model;                               >
<!ATTLIST  speech
             %speech-atts;                                           >


<!--                    SPEAKER ELEMENTS                           -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        a speaker.                                 -->
<!ENTITY % speaker-elements
                        "| %person-name.class; | %simple-link.class;">


<!--                    SPEAKER                                    -->
<!--                    One who utters a speech as part of a
                        speech, for example the computer "HAL" in
                        the exchange 'Hal: "Hi Dave"'.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=speaker
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=speaker
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=speaker
                                                                   -->
<!ELEMENT  speaker      (#PCDATA %speaker-elements;)*                >
<!ATTLIST  speaker
             %speaker-atts;                                          >


<!--                    STATEMENT, FORMAL MODEL                    -->
<!--                    Content model for the <statement> element  -->
<!ENTITY % statement-model
                        "(label?, title?, 
                          (%abstract.class;)*, 
                          (%kwd-group.class;)*, (%subj-group.class;)*,
                          (%just-para.class; | %statement.class;)+,
                          (%display-back-matter.class;)*)"           >


<!--                    STATEMENT, FORMAL                          -->
<!--                    A Theorem, Lemma, Proof, Postulate,
                        Hypothesis, Proposition, Corollary, or
                        other formal statement, identified as such
                        with a label, usually made typographically
                        distinct from the surrounding text
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=statement
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=statement
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=statement
                                                                   -->
<!ELEMENT  statement    %statement-model;                            >
<!ATTLIST  statement
             %statement-atts;                                        >


<!--                    VERSE GROUP MODEL                          -->
<!--                    Content model for the <verse-group> element-->
<!ENTITY % verse-group-model
                        "(label?, title?, subtitle?,
                         (verse-line | verse-group)+,
                         (%display-back-matter.class;)*) "           >


<!--                    VERSE FORM FOR POETRY                      -->
<!--                    A song, poem, or verse.
                        Implementor's Note: No attempt has been made
                        to retain the look or visual form of the
                        original.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=verse-group
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=verse-group
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=verse-group
                                                                   -->
<!ELEMENT  verse-group  %verse-group-model;                          >
<!ATTLIST  verse-group
             %verse-group-atts;                                      >


<!--                    VERSE-LINE ELEMENTS                        -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        a <verse-line>
                        Design Note: All inline mixes begin with an
                        OR bar, but since %simple-text; is an inline
                        mix, the OR bar is already there.          -->
<!ENTITY % verse-line-elements
                        "%simple-text; | %simple-link.class;"        >


<!--                    LINE OF A VERSE                            -->
<!--                    One line of a poem or verse
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=verse-line
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=verse-line
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=verse-line
                                                                   -->
<!ELEMENT  verse-line   (#PCDATA %verse-line-elements;)*             >
<!ATTLIST  verse-line
             %verse-line-atts;                                       >


<!-- ================== End Paragraph Class Module =============== -->

# integration_checker.py
"""
集成检查器
检查新的专业写作方法与现有系统的集成情况
确保app.py、llm_manager、search、数据可视化等完美匹配
"""

import os
import logging
import importlib
import inspect
from typing import Dict, List, Any, Optional, Tuple

logger = logging.getLogger(__name__)


class IntegrationChecker:
    """
    系统集成检查器
    验证新的专业写作方法与现有组件的兼容性
    """
    
    def __init__(self):
        self.issues = []
        self.recommendations = []
        
    def check_all_integrations(self) -> Dict[str, Any]:
        """检查所有集成点"""
        results = {
            'app_integration': self.check_app_integration(),
            'llm_integration': self.check_llm_integration(),
            'search_integration': self.check_search_integration(),
            'visualization_integration': self.check_visualization_integration(),
            'data_flow': self.check_data_flow(),
            'language_consistency': self.check_language_consistency(),
            'overall_status': 'PASS'
        }
        
        # 计算总体状态
        failed_checks = []
        warning_checks = []

        for k, v in results.items():
            if k == 'overall_status':
                continue
            if isinstance(v, dict):
                if v.get('status') == 'FAIL':
                    failed_checks.append(k)
                elif v.get('status') == 'WARNING':
                    warning_checks.append(k)

        if failed_checks:
            results['overall_status'] = 'FAIL'
            results['failed_components'] = failed_checks
        elif warning_checks:
            results['overall_status'] = 'WARNING'
            results['warning_components'] = warning_checks
        
        return results
    
    def check_app_integration(self) -> Dict[str, Any]:
        """检查app.py集成"""
        try:
            # 检查app.py是否能正确导入新模块
            from app import get_llm_manager, get_search_service
            from ebm_generator import EBMGenerator
            from professional_ebm_writer import ProfessionalEBMWriter
            
            # 检查EBMGenerator是否有新方法
            ebm_gen = EBMGenerator(get_llm_manager())
            
            required_methods = [
                'generate_professional_report',
                '_generate_narrative_with_professional_writer',
                '_generate_professional_results',
                '_generate_professional_discussion',
                '_generate_professional_conclusion'
            ]
            
            missing_methods = []
            for method in required_methods:
                if not hasattr(ebm_gen, method):
                    missing_methods.append(method)
            
            if missing_methods:
                return {
                    'status': 'FAIL',
                    'issue': f'EBMGenerator缺少方法: {missing_methods}',
                    'recommendation': '需要在EBMGenerator中添加缺失的专业写作方法'
                }
            
            # 检查app.py是否调用了正确的方法
            app_content = self._read_file('app.py')
            if 'generate_reports' in app_content and 'generate_professional_report' not in app_content:
                return {
                    'status': 'WARNING',
                    'issue': 'app.py仍在使用旧的generate_reports方法',
                    'recommendation': '建议更新app.py以使用新的generate_professional_report方法'
                }
            
            return {
                'status': 'PASS',
                'message': 'app.py集成检查通过'
            }
            
        except ImportError as e:
            return {
                'status': 'FAIL',
                'issue': f'导入错误: {e}',
                'recommendation': '检查模块路径和依赖关系'
            }
        except Exception as e:
            return {
                'status': 'FAIL',
                'issue': f'app.py集成检查失败: {e}',
                'recommendation': '检查app.py代码结构'
            }
    
    def check_llm_integration(self) -> Dict[str, Any]:
        """检查LLM管理器集成"""
        try:
            from llm_manager import LLMManager
            from professional_ebm_writer import ProfessionalEBMWriter
            
            llm_manager = LLMManager()
            writer = ProfessionalEBMWriter(llm_manager)
            
            # 检查LLM管理器是否支持所需功能
            required_methods = ['generate_content', 'get_available_models']
            missing_methods = []
            
            for method in required_methods:
                if not hasattr(llm_manager, method):
                    missing_methods.append(method)
            
            if missing_methods:
                return {
                    'status': 'FAIL',
                    'issue': f'LLMManager缺少方法: {missing_methods}',
                    'recommendation': '更新LLMManager以支持所需功能'
                }
            
            # 检查语言纯净性验证功能
            if not hasattr(writer, '_contains_english') or not hasattr(writer, '_contains_chinese'):
                return {
                    'status': 'WARNING',
                    'issue': '缺少语言纯净性验证功能',
                    'recommendation': '确保ProfessionalEBMWriter包含语言验证方法'
                }
            
            return {
                'status': 'PASS',
                'message': 'LLM集成检查通过'
            }
            
        except Exception as e:
            return {
                'status': 'FAIL',
                'issue': f'LLM集成检查失败: {e}',
                'recommendation': '检查LLM管理器和专业写作器的兼容性'
            }
    
    def check_search_integration(self) -> Dict[str, Any]:
        """检查搜索服务集成"""
        try:
            from literature_search import SearchService
            from llm_manager import LLMManager
            
            llm_manager = LLMManager()
            search_service = SearchService(llm_manager)
            
            # 检查搜索服务是否返回正确格式的数据
            required_fields = ['title', 'abstract', 'authors', 'year', 'clinical_data']
            
            # 模拟检查数据格式
            sample_result = {
                'title': 'Sample Study',
                'abstract': 'Sample abstract',
                'authors': ['Author 1'],
                'year': '2024',
                'clinical_data': {}
            }
            
            missing_fields = []
            for field in required_fields:
                if field not in sample_result:
                    missing_fields.append(field)
            
            if missing_fields:
                return {
                    'status': 'WARNING',
                    'issue': f'搜索结果可能缺少字段: {missing_fields}',
                    'recommendation': '确保搜索服务返回完整的研究数据'
                }
            
            return {
                'status': 'PASS',
                'message': '搜索服务集成检查通过'
            }
            
        except Exception as e:
            return {
                'status': 'FAIL',
                'issue': f'搜索服务集成检查失败: {e}',
                'recommendation': '检查搜索服务的可用性和数据格式'
            }
    
    def check_visualization_integration(self) -> Dict[str, Any]:
        """检查数据可视化集成"""
        try:
            # 检查可视化模块是否存在
            viz_path = 'extensions/visualization/data_visualizer.py'
            if not os.path.exists(viz_path):
                return {
                    'status': 'WARNING',
                    'issue': '数据可视化模块不存在',
                    'recommendation': '创建或恢复数据可视化模块'
                }
            
            # 检查可视化模块是否能正常导入
            import sys
            sys.path.append('extensions/visualization')
            
            try:
                from data_visualizer import DataVisualizer
                
                # 检查是否支持真实数据
                viz_content = self._read_file(viz_path)
                if '模拟数据' in viz_content or 'mock data' in viz_content.lower():
                    return {
                        'status': 'WARNING',
                        'issue': '数据可视化仍使用模拟数据',
                        'recommendation': '更新可视化模块以使用真实文献数据'
                    }
                
                return {
                    'status': 'PASS',
                    'message': '数据可视化集成检查通过'
                }
                
            except ImportError:
                return {
                    'status': 'FAIL',
                    'issue': '无法导入数据可视化模块',
                    'recommendation': '检查可视化模块的依赖和代码结构'
                }
            
        except Exception as e:
            return {
                'status': 'FAIL',
                'issue': f'数据可视化集成检查失败: {e}',
                'recommendation': '检查可视化模块的完整性'
            }
    
    def check_data_flow(self) -> Dict[str, Any]:
        """检查数据流完整性"""
        try:
            # 检查数据流：搜索 -> 处理 -> 写作 -> 可视化
            flow_issues = []
            
            # 1. 搜索到处理的数据流
            from literature_search import SearchService
            from ebm_generator import EBMGenerator
            
            # 2. 处理到写作的数据流
            from professional_ebm_writer import ProfessionalEBMWriter
            
            # 3. 检查数据格式兼容性
            sample_study = {
                'title': 'Test Study',
                'abstract': 'Test abstract',
                'authors': ['Test Author'],
                'year': '2024',
                'clinical_data': {
                    'study_design': 'RCT',
                    'population': 'Test population',
                    'intervention': 'Test intervention',
                    'sample_size': 100
                }
            }
            
            # 检查专业写作器是否能处理这种数据格式
            from llm_manager import LLMManager
            writer = ProfessionalEBMWriter(LLMManager())
            
            try:
                characteristics = writer._extract_study_characteristics([sample_study])
                if characteristics.total_studies != 1:
                    flow_issues.append('数据特征提取异常')
            except Exception as e:
                flow_issues.append(f'数据特征提取失败: {e}')
            
            if flow_issues:
                return {
                    'status': 'FAIL',
                    'issue': f'数据流问题: {flow_issues}',
                    'recommendation': '修复数据流中的兼容性问题'
                }
            
            return {
                'status': 'PASS',
                'message': '数据流检查通过'
            }
            
        except Exception as e:
            return {
                'status': 'FAIL',
                'issue': f'数据流检查失败: {e}',
                'recommendation': '检查各组件间的数据传递'
            }
    
    def check_language_consistency(self) -> Dict[str, Any]:
        """检查语言一致性"""
        try:
            issues = []
            
            # 检查英文报告是否包含中文
            output_dir = 'output'
            if os.path.exists(output_dir):
                for file in os.listdir(output_dir):
                    if file.endswith('_en.md'):
                        file_path = os.path.join(output_dir, file)
                        content = self._read_file(file_path)
                        if self._contains_chinese(content):
                            issues.append(f'英文报告{file}包含中文内容')
            
            # 检查中文报告是否包含过多英文
            if os.path.exists(output_dir):
                for file in os.listdir(output_dir):
                    if file.endswith('_cn.md'):
                        file_path = os.path.join(output_dir, file)
                        content = self._read_file(file_path)
                        if self._contains_excessive_english(content):
                            issues.append(f'中文报告{file}包含过多英文内容')
            
            if issues:
                return {
                    'status': 'FAIL',
                    'issue': f'语言一致性问题: {issues}',
                    'recommendation': '使用新的专业写作器确保语言纯净性'
                }
            
            return {
                'status': 'PASS',
                'message': '语言一致性检查通过'
            }
            
        except Exception as e:
            return {
                'status': 'WARNING',
                'issue': f'语言一致性检查失败: {e}',
                'recommendation': '手动检查生成报告的语言纯净性'
            }
    
    def _read_file(self, file_path: str) -> str:
        """安全读取文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                return f.read()
        except Exception:
            return ""
    
    def _contains_chinese(self, text: str) -> bool:
        """检查是否包含中文"""
        import re
        chinese_chars = re.findall(r'[\u4e00-\u9fff]', text)
        return len(chinese_chars) > 10
    
    def _contains_excessive_english(self, text: str) -> bool:
        """检查是否包含过多英文"""
        import re
        english_words = re.findall(r'\b[a-zA-Z]+\b', text)
        # 过滤医学术语
        allowed_terms = {'PICO', 'PRISMA', 'ROB', 'GRADE', 'RCT', 'OR', 'CI', 'HR', 'RR', 'MD', 'SMD'}
        significant_english = [w for w in english_words if w not in allowed_terms and len(w) > 2]
        return len(significant_english) > 50


def run_integration_check():
    """运行完整的集成检查"""
    checker = IntegrationChecker()
    results = checker.check_all_integrations()
    
    print("🔍 系统集成检查报告")
    print("=" * 50)
    
    for component, result in results.items():
        if component in ['overall_status', 'failed_components', 'warning_components']:
            continue

        if isinstance(result, dict):
            status_icon = "✅" if result.get('status') == 'PASS' else "⚠️" if result.get('status') == 'WARNING' else "❌"
            print(f"\n{status_icon} {component.upper()}")

            if result.get('status') == 'PASS':
                print(f"   {result.get('message', '检查通过')}")
            else:
                print(f"   问题: {result.get('issue', '未知问题')}")
                print(f"   建议: {result.get('recommendation', '无建议')}")
        else:
            print(f"\n❓ {component.upper()}")
            print(f"   未知结果类型: {type(result)}")
    
    print(f"\n📊 总体状态: {'✅ PASS' if results['overall_status'] == 'PASS' else '❌ FAIL'}")
    
    if results['overall_status'] == 'FAIL':
        print(f"失败组件: {results.get('failed_components', [])}")
    
    return results


if __name__ == "__main__":
    run_integration_check()

# comprehensive_ebm_usage_example.py
"""
全面EBM报告生成使用示例
展示如何使用瓦片式处理器处理所有60篇文献
确保每篇文献都被纳入分析，避免遗漏
"""

import logging
from typing import List, Dict, Any
from ebm_generator import EBMGenerator
from llm_manager import LLMManager

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def example_comprehensive_ebm_processing():
    """
    全面EBM处理示例
    演示如何处理大量文献（如60篇）并生成专业报告
    """
    
    # 1. 初始化系统
    print("🚀 初始化EBM生成系统...")
    
    # 假设已有LLM管理器和文献数据
    # llm_manager = LLMManager()
    # studies = load_studies_from_search()  # 60篇文献
    
    # 模拟60篇文献数据
    studies = create_mock_studies(60)
    
    # 创建EBM生成器
    # ebm_generator = EBMGenerator(llm_manager, update_callback=print_progress)
    
    print(f"📚 准备处理{len(studies)}篇文献...")
    
    # 2. 使用瓦片式处理器处理所有文献
    print("\n🔧 开始瓦片式处理...")
    
    # tile_result = ebm_generator.process_all_studies_with_tiles(
    #     topic="脓毒血症患者急性肾损伤与高血压的预后分析",
    #     studies=studies,
    #     provider="zhipuai",
    #     model="glm-4-flash"
    # )
    
    # 模拟处理结果
    tile_result = create_mock_tile_result(studies)
    
    if tile_result['success']:
        print("✅ 瓦片处理成功！")
        print(f"📊 处理统计:")
        summary = tile_result['processing_summary']
        print(f"  - 总文献数: {summary['total_studies']}")
        print(f"  - 瓦片数: {summary['total_tiles']}")
        print(f"  - 成功处理: {summary['processed_tiles']}")
        print(f"  - 成功率: {summary['success_rate']:.1%}")
        print(f"  - 证据点: {summary['total_evidence_points']}")
        print(f"  - 覆盖率: {summary['processing_efficiency']['processing_coverage']:.1%}")
    else:
        print(f"❌ 瓦片处理失败: {tile_result['error']}")
        return
    
    # 3. 生成全面的EBM报告
    print("\n📝 生成全面EBM报告...")
    
    # md_file, html_file = ebm_generator.generate_comprehensive_ebm_report(
    #     topic="脓毒血症患者急性肾损伤与高血压的预后分析",
    #     articles_for_report=studies,
    #     report_type="ebm",
    #     provider="zhipuai",
    #     model="glm-4-flash",
    #     is_chinese=True
    # )
    
    # 模拟报告生成
    md_file, html_file = "output/20241219_comprehensive_ebm_report_cn.md", "output/20241219_comprehensive_ebm_report_cn.html"
    
    print(f"✅ 报告生成完成！")
    print(f"📄 Markdown文件: {md_file}")
    print(f"🌐 HTML文件: {html_file}")
    
    # 4. 展示报告质量指标
    print("\n📈 报告质量指标:")
    print(f"  - 文献纳入率: 100% ({len(studies)}/{len(studies)})")
    print(f"  - 证据提取率: {summary['total_evidence_points']}/{len(studies)} = {summary['total_evidence_points']/len(studies):.1f}个/篇")
    print(f"  - 主题覆盖: {len(summary['tiles_by_theme'])}个主要主题")
    print(f"  - 处理效率: {summary['processing_efficiency']['studies_per_tile']:.1f}篇/瓦片")


def create_mock_studies(count: int) -> List[Dict[str, Any]]:
    """创建模拟研究数据"""
    studies = []
    
    study_types = ['RCT', 'Cohort Study', 'Case-Control Study', 'Cross-sectional Study', 'Systematic Review']
    populations = ['sepsis patients', 'hypertensive patients', 'cardiac patients', 'renal patients', 'pediatric patients']
    interventions = ['drug therapy', 'surgical intervention', 'management protocol', 'monitoring strategy']
    
    for i in range(count):
        study = {
            'id': f"study_{i+1:03d}",
            'title': f"Study {i+1}: Clinical analysis of {populations[i % len(populations)]} with {interventions[i % len(interventions)]}",
            'authors': [f"Author{j+1}" for j in range(3)],
            'year': str(2020 + (i % 5)),
            'abstract': f"This {study_types[i % len(study_types)]} investigated {populations[i % len(populations)]} receiving {interventions[i % len(interventions)]}. The study included {50 + i*10} participants and found significant clinical outcomes. Results showed improved patient outcomes with statistical significance (p<0.05).",
            'clinical_data': {
                'study_design': study_types[i % len(study_types)],
                'population': populations[i % len(populations)],
                'intervention': interventions[i % len(interventions)],
                'sample_size': 50 + i*10,
                'outcomes': 'mortality, morbidity, length of stay'
            },
            'url': f"https://example.com/study_{i+1}",
            'doi': f"10.1000/study.{i+1}",
            'journal': f"Journal of Medicine {(i % 5) + 1}",
            'relevance_score': 0.7 + (i % 3) * 0.1
        }
        studies.append(study)
    
    return studies


def create_mock_tile_result(studies: List[Dict[str, Any]]) -> Dict[str, Any]:
    """创建模拟瓦片处理结果"""
    total_studies = len(studies)
    total_tiles = (total_studies + 7) // 8  # 每个瓦片8篇文献
    
    # 模拟证据点
    evidence_points = []
    for i, study in enumerate(studies):
        evidence_point = {
            'study_id': study['id'],
            'study_title': study['title'],
            'evidence_type': 'Clinical Outcome',
            'key_finding': f"Significant improvement in patient outcomes (p<0.05)",
            'effect_size': f"OR: {1.2 + i*0.1:.2f}",
            'confidence_interval': f"95% CI: [{1.0 + i*0.05:.2f}, {1.5 + i*0.15:.2f}]",
            'p_value': 'p<0.05',
            'sample_size': study['clinical_data']['sample_size'],
            'quality_assessment': 'Moderate quality',
            'clinical_significance': 'Clinically significant',
            'limitations': 'Small sample size, single center',
            'tile_id': f"tile_{(i//8)+1:03d}",
            'theme': study['clinical_data']['study_design'],
            'sub_theme': study['clinical_data']['population']
        }
        evidence_points.append(evidence_point)
    
    # 模拟主题分布
    tiles_by_theme = {
        'RCT': total_tiles // 5,
        'Cohort Study': total_tiles // 4,
        'Case-Control Study': total_tiles // 6,
        'Cross-sectional Study': total_tiles // 8,
        'Systematic Review': total_tiles // 10
    }
    
    processing_summary = {
        'total_studies': total_studies,
        'total_tiles': total_tiles,
        'processed_tiles': total_tiles,
        'failed_tiles': 0,
        'success_rate': 1.0,
        'total_evidence_points': len(evidence_points),
        'average_evidence_per_tile': len(evidence_points) / total_tiles,
        'tiles_by_theme': tiles_by_theme,
        'processing_efficiency': {
            'studies_per_tile': total_studies / total_tiles,
            'evidence_per_study': len(evidence_points) / total_studies,
            'processing_coverage': 1.0
        }
    }
    
    return {
        'success': True,
        'evidence_points': evidence_points,
        'processing_summary': processing_summary,
        'tiles': [],  # 简化，实际应包含瓦片对象
        'detailed_results': {}
    }


def print_progress(message: str, is_error: bool = False):
    """进度回调函数"""
    if is_error:
        print(f"❌ {message}")
    else:
        print(f"ℹ️  {message}")


def demonstrate_tile_processing_benefits():
    """演示瓦片处理的优势"""
    print("\n🎯 瓦片式处理的优势:")
    print("1. ✅ 确保所有文献都被纳入分析 - 100%覆盖率")
    print("2. 🧠 适应小模型上下文限制 - 每个瓦片<2k tokens")
    print("3. 🎨 智能分组和主题识别 - 按研究类型、人群、干预分组")
    print("4. 🔄 容错处理机制 - API错误时自动重试")
    print("5. 📊 详细的处理统计 - 透明的质量指标")
    print("6. ⚡ 并行处理能力 - 提高处理效率")
    print("7. 🎯 专业的证据提取 - 结构化的证据点")
    print("8. 📈 质量评估集成 - ROB和GRADE系统")


def show_comparison_with_traditional_approach():
    """展示与传统方法的对比"""
    print("\n📊 与传统方法对比:")
    print("| 特性 | 传统方法 | 瓦片式处理 |")
    print("|------|----------|------------|")
    print("| 文献处理数量 | 20篇 (33%) | 60篇 (100%) |")
    print("| 上下文限制 | 经常溢出 | 完全适应 |")
    print("| 证据提取细度 | 粗糙('其他') | 细致分类 |")
    print("| 容错能力 | 单点失败 | 多重容错 |")
    print("| 处理透明度 | 黑盒 | 全程可见 |")
    print("| 质量保证 | 有限 | 全面验证 |")
    print("| 报告专业性 | 基础 | 专业标准 |")


if __name__ == "__main__":
    print("🔬 全面EBM报告生成系统演示")
    print("=" * 50)
    
    # 运行主要示例
    example_comprehensive_ebm_processing()
    
    # 展示优势
    demonstrate_tile_processing_benefits()
    
    # 显示对比
    show_comparison_with_traditional_approach()
    
    print("\n✨ 演示完成！")
    print("💡 提示: 实际使用时，请确保:")
    print("   1. 配置正确的LLM提供商和模型")
    print("   2. 准备真实的文献搜索数据")
    print("   3. 设置适当的API限制和重试参数")
    print("   4. 监控处理进度和错误日志")

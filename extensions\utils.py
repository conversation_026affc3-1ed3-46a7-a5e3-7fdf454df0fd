"""
实用工具函数
"""
import os
import json
import logging
from pathlib import Path
from typing import Any, Dict, List, Optional, Union
import pandas as pd
import numpy as np

logger = logging.getLogger(__name__)

def ensure_dir(dir_path: Union[str, Path]) -> Path:
    """
    确保目录存在，如果不存在则创建
    
    Args:
        dir_path: 目录路径
        
    Returns:
        Path: 目录路径对象
    """
    path = Path(dir_path)
    path.mkdir(parents=True, exist_ok=True)
    return path

def save_json(data: Any, file_path: Union[str, Path], indent: int = 2, **kwargs) -> str:
    """
    保存数据为JSON文件
    
    Args:
        data: 要保存的数据
        file_path: 文件路径
        indent: 缩进空格数
        **kwargs: 传递给json.dump的其他参数
        
    Returns:
        str: 保存的文件路径
    """
    file_path = Path(file_path)
    ensure_dir(file_path.parent)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=indent, **kwargs)
    
    return str(file_path)

def load_json(file_path: Union[str, Path], **kwargs) -> Any:
    """
    从JSON文件加载数据
    
    Args:
        file_path: 文件路径
        **kwargs: 传递给json.load的其他参数
        
    Returns:
        Any: 加载的数据
    """
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f, **kwargs)

def save_dataframe(
    df: pd.DataFrame, 
    file_path: Union[str, Path], 
    index: bool = False, 
    **kwargs
) -> str:
    """
    保存DataFrame到文件
    
    Args:
        df: 要保存的DataFrame
        file_path: 文件路径
        index: 是否保存索引
        **kwargs: 传递给pandas to_*方法的其他参数
        
    Returns:
        str: 保存的文件路径
    """
    file_path = Path(file_path)
    ensure_dir(file_path.parent)
    
    ext = file_path.suffix.lower()
    
    if ext == '.csv':
        df.to_csv(file_path, index=index, **kwargs)
    elif ext in ['.xlsx', '.xls']:
        df.to_excel(file_path, index=index, **kwargs)
    elif ext == '.json':
        df.to_json(file_path, force_ascii=False, indent=2, **kwargs)
    else:
        raise ValueError(f"不支持的格式: {ext}")
    
    return str(file_path)

def load_dataframe(file_path: Union[str, Path], **kwargs) -> pd.DataFrame:
    """
    从文件加载DataFrame
    
    Args:
        file_path: 文件路径
        **kwargs: 传递给pandas read_*方法的其他参数
        
    Returns:
        pd.DataFrame: 加载的DataFrame
    """
    file_path = Path(file_path)
    ext = file_path.suffix.lower()
    
    if ext == '.csv':
        return pd.read_csv(file_path, **kwargs)
    elif ext in ['.xlsx', '.xls']:
        return pd.read_excel(file_path, **kwargs)
    elif ext == '.json':
        return pd.read_json(file_path, **kwargs)
    else:
        raise ValueError(f"不支持的格式: {ext}")

def calculate_effect_size(
    group1: np.ndarray, 
    group2: np.ndarray, 
    effect_size_type: str = 'cohen_d'
) -> Dict[str, float]:
    """
    计算效应量
    
    Args:
        group1: 第一组数据
        group2: 第二组数据
        effect_size_type: 效应量类型，支持 'cohen_d', 'hedges_g', 'odds_ratio'
        
    Returns:
        Dict[str, float]: 包含效应量及其95%置信区间的字典
    """
    import scipy.stats as stats
    from scipy.stats import norm
    
    n1 = len(group1)
    n2 = len(group2)
    
    if n1 < 2 or n2 < 2:
        raise ValueError("每组至少需要2个观察值")
    
    # 计算均值和标准差
    mean1, mean2 = np.mean(group1), np.mean(group2)
    std1, std2 = np.std(group1, ddof=1), np.std(group2, ddof=1)
    
    # 计算合并标准差
    pooled_std = np.sqrt(((n1 - 1) * std1**2 + (n2 - 1) * std2**2) / (n1 + n2 - 2))
    
    # 计算标准误
    se = np.sqrt((std1**2 / n1) + (std2**2 / n2))
    
    # 计算t值和p值
    t_stat, p_value = stats.ttest_ind(group1, group2, equal_var=False)
    
    # 计算效应量
    if effect_size_type == 'cohen_d':
        effect_size = (mean1 - mean2) / pooled_std
        # 计算Hedges' g校正（小样本校正）
        if effect_size_type == 'hedges_g':
            df = n1 + n2 - 2
            correction = 1 - (3 / (4 * df - 1))
            effect_size *= correction
    
    # 计算95%置信区间
    z = norm.ppf(0.975)  # 95% CI的z值
    ci_lower = effect_size - z * np.sqrt((n1 + n2) / (n1 * n2) + (effect_size**2) / (2 * (n1 + n2)))
    ci_upper = effect_size + z * np.sqrt((n1 + n2) / (n1 * n2) + (effect_size**2) / (2 * (n1 + n2)))
    
    return {
        'effect_size': effect_size,
        'ci_lower': ci_lower,
        'ci_upper': ci_upper,
        'p_value': p_value,
        'n1': n1,
        'n2': n2,
        'mean1': mean1,
        'mean2': mean2,
        'std1': std1,
        'std2': std2
    }

def format_effect_size(
    effect_size: float, 
    ci_lower: float, 
    ci_upper: float, 
    precision: int = 2
) -> str:
    """
    格式化效应量和置信区间
    
    Args:
        effect_size: 效应量
        ci_lower: 置信区间下限
        ci_upper: 置信区间上限
        precision: 小数位数
        
    Returns:
        str: 格式化后的字符串，例如 "0.75 (95% CI: 0.65 to 0.85)"
    """
    return f"{effect_size:.{precision}f} (95% CI: {ci_lower:.{precision}f} to {ci_upper:.{precision}f})"

def calculate_i2(q: float, k: int) -> float:
    """
    计算I²统计量（异质性统计量）
    
    Args:
        q: Q统计量
        k: 研究数量
        
    Returns:
        float: I²值（0-100）
    """
    if q <= 0:
        return 0.0
    else:
        return max(0, 100 * (q - (k - 1)) / q)

def calculate_tau2(q: float, k: int, df: Optional[int] = None) -> float:
    """
    计算tau²（研究间方差）
    
    Args:
        q: Q统计量
        k: 研究数量
        df: 自由度，默认为k-1
        
    Returns:
        float: tau²值
    """
    if df is None:
        df = k - 1
    
    c = df  # 对于DerSimonian-Laird方法，c = df = k-1
    
    if q <= df:
        return 0.0
    else:
        return (q - df) / c

def safe_divide(numerator: float, denominator: float, default: float = 0.0) -> float:
    """
    安全除法，避免除零错误
    
    Args:
        numerator: 分子
        denominator: 分母
        default: 分母为零时的默认值
        
    Returns:
        float: 除法的结果或默认值
    """
    try:
        return numerator / denominator if denominator != 0 else default
    except (TypeError, ZeroDivisionError):
        return default

def setup_logger(
    name: str,
    log_file: Optional[str] = None,
    level: int = logging.INFO,
    console: bool = True,
    file_mode: str = 'a'
) -> logging.Logger:
    """
    设置日志记录器
    
    Args:
        name: 日志记录器名称
        log_file: 日志文件路径，如果为None则不保存到文件
        level: 日志级别
        console: 是否在控制台显示日志
        file_mode: 文件模式，'w'为覆盖，'a'为追加
        
    Returns:
        logging.Logger: 配置好的日志记录器
    """
    logger = logging.getLogger(name)
    logger.setLevel(level)
    
    # 避免重复添加处理器
    if logger.handlers:
        return logger
    
    # 创建格式化器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 控制台处理器
    if console:
        console_handler = logging.StreamHandler()
        console_handler.setLevel(level)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    # 文件处理器
    if log_file:
        # 确保日志目录存在
        log_dir = os.path.dirname(log_file)
        if log_dir:
            os.makedirs(log_dir, exist_ok=True)
            
        file_handler = logging.FileHandler(log_file, mode=file_mode, encoding='utf-8')
        file_handler.setLevel(level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    return logger

def timing_decorator(func):
    """
    计时装饰器，用于测量函数执行时间
    
    Args:
        func: 要计时的函数
        
    Returns:
        function: 包装后的函数
    """
    import time
    from functools import wraps
    
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        logger = logging.getLogger(func.__module__)
        logger.debug(f"{func.__name__} 执行时间: {end_time - start_time:.4f}秒")
        
        return result
    
    return wrapper

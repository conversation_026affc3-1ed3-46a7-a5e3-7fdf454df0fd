#!/usr/bin/env python3
"""
测试可视化修复
"""

import sys
import os
sys.path.append('.')

def test_quality_assessment():
    """测试质量评估修复"""
    print("=== 测试质量评估修复 ===")
    
    try:
        from ebm_professional_enhancements import ProfessionalEBMProcessor
        
        # 测试数据
        test_studies = [
            {'id': 'study1', 'title': 'Test RCT', 'abstract': 'randomized controlled trial', 'year': 2020},
            {'id': 'study2', 'title': 'Test Cohort', 'abstract': 'cohort study', 'year': 2021},
            {'id': 'study3', 'title': 'Test Case Control', 'abstract': 'case control study', 'year': 2022},
            {'id': 'study4', 'title': 'Test Observational', 'abstract': 'observational study', 'year': 2023}
        ]
        
        processor = ProfessionalEBMProcessor(None)
        quality_summary = processor.generate_quality_summary(test_studies)
        
        print(f"✅ 质量评估成功")
        print(f"   总研究数: {quality_summary.get('total_studies', 0)}")
        print(f"   质量分布: {quality_summary.get('quality_distribution', {})}")
        print(f"   整体推荐: {quality_summary.get('recommendation', '未知')}")
        
        # 检查是否包含"Not assessed"
        quality_dist = quality_summary.get('quality_distribution', {})
        if 'Not assessed' in quality_dist:
            print(f"   包含未评估研究: {quality_dist['Not assessed']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 质量评估失败: {e}")
        return False

def test_funnel_plot():
    """测试漏斗图修复"""
    print("\n=== 测试漏斗图修复 ===")
    
    try:
        from extensions.visualization.funnel_plot import FunnelPlot
        
        # 测试数据，包含None值
        funnel_data = {
            'studies': [
                {'effect_size': 0.5, 'standard_error': 0.1, 'sample_size': 100, 'label': 'Study 1'},
                {'effect_size': 0.3, 'standard_error': 0.15, 'sample_size': 80, 'label': 'Study 2'},
                {'effect_size': None, 'standard_error': None, 'sample_size': None, 'label': 'Study 3'},
                {'effect_size': 0.7, 'standard_error': 0, 'sample_size': 0, 'label': 'Study 4'},  # 测试零值
            ]
        }
        
        funnel_plot = FunnelPlot(funnel_data)
        print("✅ 漏斗图创建成功")
        
        # 测试HTML生成
        html_output = funnel_plot.to_html()
        if html_output:
            print("✅ HTML输出生成成功")
        else:
            print("⚠️ HTML输出为空")
        
        return True
        
    except Exception as e:
        print(f"❌ 漏斗图创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_visualizer():
    """测试数据可视化器修复"""
    print("\n=== 测试数据可视化器修复 ===")
    
    try:
        from extensions.visualization.data_visualizer import DataVisualizer
        from ebm_professional_enhancements import ProfessionalEBMProcessor
        
        # 准备测试数据
        test_studies = [
            {'id': 'study1', 'title': 'Test Study 1', 'abstract': 'randomized controlled trial', 'year': 2020},
            {'id': 'study2', 'title': 'Test Study 2', 'abstract': 'cohort study', 'year': 2021}
        ]
        
        processor = ProfessionalEBMProcessor(None)
        quality_summary = processor.generate_quality_summary(test_studies)
        
        visualizer = DataVisualizer(topic="test_report")
        
        test_data = {
            'quality_summary': quality_summary,
            'search_stats': {
                'identified': 10, 
                'after_duplicates': 8, 
                'screened': 6, 
                'included_qualitative': 4,
                'duplicates_removed': 2,
                'excluded_screening': 2,
                'excluded_full_text': 2
            },
            'studies': test_studies
        }
        
        # 测试证据质量图表生成
        quality_chart_path = visualizer.generate_evidence_quality_chart(quality_summary)
        if quality_chart_path:
            print("✅ 证据质量图表生成成功")
            print(f"   保存路径: {quality_chart_path}")
        else:
            print("⚠️ 证据质量图表生成失败或跳过")
        
        return True
        
    except Exception as e:
        print(f"❌ 数据可视化器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试可视化修复...")
    
    results = []
    results.append(test_quality_assessment())
    results.append(test_funnel_plot())
    results.append(test_data_visualizer())
    
    print(f"\n=== 测试结果总结 ===")
    print(f"成功: {sum(results)}/{len(results)}")
    
    if all(results):
        print("🎉 所有测试通过！修复成功。")
    else:
        print("⚠️ 部分测试失败，需要进一步检查。")

if __name__ == "__main__":
    main()

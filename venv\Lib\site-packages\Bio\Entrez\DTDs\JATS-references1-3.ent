<!-- ============================================================= -->
<!--  MODULE:    Bibliographic Reference (Citation) Class Elements -->
<!--  VERSION:   ANSI/NISO JATS Version 1.3 (Z39.96-2021)          -->
<!--  DATE:      June 2021                                         -->
<!--                                                               -->
<!-- ============================================================= -->
 
<!-- ============================================================= -->
<!--                    PUBLIC DOCUMENT TYPE DEFINITION            -->
<!--                        TYPICAL INVOCATION                     -->
<!--
"-//NLM//DTD JATS (Z39.96) JATS DTD Suite Bibliographic Reference (Citation) Class Elements v1.3 20210610//EN"
Delivered as file "JATS-references1-3.ent"                         -->
<!-- ============================================================= -->

<!-- ============================================================= -->
<!-- SYSTEM:     JATS DTD Suite                                    -->
<!--                                                               -->
<!-- PURPOSE:    Defines the bibliographic reference elements      -->
<!--                                                               -->
<!-- TAG SET SPONSOR                                               -->
<!--             National Center for Biotechnology                 -->
<!--                Information (NCBI)                             -->
<!--             National Library of Medicine (NLM)                -->
<!--                                                               -->
<!-- CREATED FOR:                                                  -->
<!--             This module was created for the JATS DTD Suite.   -->
<!--             Digital archives and publishers may use the       -->
<!--             DTD as is for markup of journal literature or     -->
<!--             related material for archiving and transferring   -->
<!--             such material between archives or create a        -->
<!--             custom XML DTD from the Suite for                 -->
<!--             these purposes.                                   -->
<!--                                                               -->
<!--             This DTD is in the public domain. An organization -->
<!--             that wishes to create its own DTD from the suite  -->
<!--             may do so without permission from NLM.            -->
<!--                                                               -->
<!--             The suite has been set up to be extended using a  -->
<!--             new DTD file and a new DTD-specific customization -->
<!--             module to redefine the many Parameter Entities.   -->
<!--             Do not modify the suite directly or redistribute  -->
<!--             modified versions of the suite.                   -->
<!--                                                               -->
<!--             In the interest of maintaining consistency and    -->
<!--             clarity for potential users, NLM requests:        -->
<!--                                                               -->
<!--             1. If you create a DTD from the JATS DTD Suite    -->
<!--                and intend to stay compatible with the suite,  -->
<!--                then please include the following statement    -->
<!--                as a comment in all of your DTD modules:       -->
<!--                   "Created from, and fully compatible with,   -->
<!--                    the ANSI/NISO Z39.96 Journal Article Tag   -->
<!--                    Suite (JATS)."                             -->
<!--                                                               -->
<!--             2. If you alter one or more modules of the suite, -->
<!--                then please rename your version and all its    -->
<!--                modules to avoid any confusion with the        -->
<!--                original suite. Also, please include the       -->
<!--                following statement as a comment in all your   -->
<!--                DTD modules:                                   -->
<!--                   "Based in part on, but not fully compatible -->
<!--                    with, the ANSI/NISO Z39.96 Journal Article -->
<!--                    Tag Suite (JATS)."                         -->
<!--                                                               -->
<!-- ORIGINAL CREATION DATE:                                       -->
<!--             December 2002                                     -->
<!--                                                               -->
<!-- CREATED BY: Mulberry Technologies, Inc. for the NISO Z39.96   -->
<!--             Working Group. Mulberry Technologies was          -->
<!--             supported by National Center for Biotechnology    -->
<!--             Information (NCBI), a center of the US National   -->
<!--             Library of Medicine (NLM).                        -->
<!--                                                               -->
<!--             This module is part of the JATS DTD Suite. The    -->
<!--             Suite is a continuation of work done by NCBI,     -->
<!--             Mulberry Technologies, and Inera Inc. on the NLM  -->
<!--             Journal Archiving and Interchange DTD Suite, which-->
<!--             was originally released in December, 2002.        -->
<!--                                                               -->
<!--             NLM thanks the Harvard University Libraries, both -->
<!--             for proposing that a draft archiving NLM DTD for  -->
<!--             life sciences journals be extended to accommodate -->
<!--             journals in all disciplines and for sponsoring    -->
<!--             Inera Inc.'s collaboration with other DTD         -->
<!--             authors in completing NLM Version 1.0. The        -->
<!--             Andrew W. Mellon Foundation provided support for  -->
<!--             these important contributions.                    -->
<!--                                                               -->
<!--             Suggestions for refinements and enhancements to   -->
<!--             this DTD should be sent in email to:              -->
<!--                 <EMAIL>                         -->
<!-- ============================================================= -->


<!-- ============================================================= -->
<!--                    DTD VERSION/CHANGE HISTORY                 -->
<!-- ============================================================= -->
<!--
Version  Reason/Occasion                   (who) vx.x (yyyy-mm-dd)
   ==============================================================
     JATS Version 1.3 (ANSI/NISO Z39.96-2021)
                                  (DAL/BTU) v1.3 (2021-06-10)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by both the NISO and ANSI memberships 
     to be changed. This version of the NISO JATS was approved 
     by ANSI vote on June 10, 2021, so it supersedes all Committee 
     Drafts as well as the draft version JATS 1.3 2021 (the version
     submitted for vote), becoming NISO JATS v1.3 20210610 
     (ANSI/NISO Z39.96-2019).

     The file names and formal public identifiers have been 
     changed in the modules and the catalogs.

 35. JATS 'v1.3d2 20201130' becomes 'v1.3 20210610' 
     (final ANSI vote date)
     'ANSI/NISO Z39.96-2019' becomes 'ANSI/NISO Z39.96-2021'

   ==============================================================
     JATS Version 1.3d2 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.3d2 (2020-11-30)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by the NISO and ANSI memberships 
     to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     of the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting; it is for Standing Committee review and
     testing. The formal public identifiers have been changed 
     in the modules and the catalogs.

 34. OBJECT ID Added <object-id> to: 
        <ref-list>
  
 33. EXPANSION OF FIXED ATTRIBUTES LIST: There are four   
     content-defined attributes which are CDATA values 
     (undetermined) in Archiving, but which are named value 
     lists in Publishing/Authoring:
       - @fn-type 
       - @person-group-type (override is in this module)
       - @pub-id-type
       - @ref-type 
     In order to give users of Publishing and Authoing
     more flexibility to name new values without 
     destroying the benefit of named value lists:
       - A new value “custom” has been added to the named
           value list of each of these four attributes.
       - A new attribute @custom-type has been added to
           each element that takes one of these attributes.
     The new attribute @custom-type was also added to
     Green, so that documents valid to Blue or Pumpkin 
     would also be also valid to Green.
     In this module:
      - The element <person-group> was given the new 
        attribute @custom-type. The value "custom" will be
        documented as a valid person-group type.
        As an example: 
           <person-group @person-group-type=”custom” 
                         custom-type=”statisticians”>
     The Best Practice rule (unenforceable in DTDs, but  
     enforceable in Schematron) is, that if you use the 
     value “custom” from one of these lists, you should 
     also record what type of 'custom' in the 
     @custom-type attribute.
     
 32. JATS "v1.3d2 20201130" becomes 'v1.3 20210610' (second 
     committee draft following the final ANSI/NISO 1.2 vote)

   ==============================================================
     JATS Version 1.3d1 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.3.d1 (2019-08-31)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by the NISO and ANSI memberships 
     to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     of the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
  
 31. JATS "1.2" becomes "v1.3d1 20190831" (first committee
     draft following the final ANSI/NISO vote) 

   ==============================================================
     JATS Version 1.2 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.2     (2019-02-08)
    
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the both the NISO and ANSI membership 
     to be changed. This version of the NISO JATS was approved 
     by ANSI vote on Feb 08, 2019, so it supersedes all Committee 
     Drafts as well as the draft version JATS 1.2 2018 (the version
     submitted for vote), becoming NISO JATS v1.2 20190208 
     (ANSI/NISO Z39.96-2021).
  
 30. JATS "1.2" becomes "v1.2 20190208" (final ANSI vote date)
     "ANSI/NISO Z39.96-2015" becomes "ANSI/NISO Z39.96-2019"
     
   ==============================================================
     JATS Version 1.2 (ANSI/NISO Z39.96-2018)
     
                                   (DAL/BTU) v1.2     (2018-11-30)
    
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the both the NISO and ANSI membership 
     to be changed. This version of the NISO JATS was approved 
     by ANSI and NISO vote, so it supersedes all Committee Drafts
     and becoming NISO JATS 1.2 (ANSI/NISO Z39.96-2018).
  
 29. 
     JATS "1.2d2" and "v1.2d2 20180401" became
     JATS "1.2" and "v1.2 20181130" 

    =============================================================
     
     JATS Version 1.2d2              (DAL/BTU) v1.2d2  (2018-04-01)

     JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
     BITS is under continuous maintenance, and is modified at the
     discretion of the working group.
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
  
 28. PUB-ID-TYPE - @pub-id-type changed back to a fixed list from 
     CDATA, reversing the decision of Version 1.2d1. Although 
     the value list is unchanged, best practice will be documented
     as using @pub-id-type for type and @assigning-authority
     for the responsible agency.
         - <pub-id>

 27. BITS "2.0" and "v2.0 20151225" remain unchanged
      
   
     JATS "1.2d1" and "v1.2d1 20171231" became
     JATS "1.2d2" and "v1.2d2 20180401". 

     No module names were changed.
 
    =============================================================
     JATS Version 1.2d1 (ANSI/NISO Z39.96-2015)
                                   (DAL/BTU) v1.2d1  (2017-12-31)
 
     NISO JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
  
 26. JATS "1.2d1" and "v1.2d1 20170631" became
     JATS "1.2d1" and "v1.2d1 20171231". 

    =============================================================
     JATS Version 1.2d1              (DAL/BTU) v1.2d1  (2017-06-31)

     NISO JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
 
 25. INLINE-GRAPHIC and PRIVATE-CHAR - both <inline-graphic> and
     <private-char> added to elements allowed in:
      - <data-title>
      - <std>
 
 24. PUB-ID-TYPE - @pub-id-type changed from a fixed list to
     CDATA for all usages of the attribute. [Rescinded,
     uses a named value list as before]

 23. SOURCE/CHAPTER TITLE/PART TITLE - Added the following elements
     to <source>, <chapter-title>, and <part-title> to more
     closely mirror the model of <article-title>:
        - abbrev class
             abbrev
        - inline-display class
             alternatives | inline-graphic | private-char
        - inline-math class 
             chem-struct | inline-formula
        - math class
             tex-math | mml:math
        - simple link class
             fn | target | xref 
     
 22. JATS became version "1.2d1" and "v1.2d1 20170631" 

     =============================================================
     JATS Version 1.1                (DAL/BTU) v1.1  (2015-12-15)

     JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
     JATS 1.1 was approved in late 2015, so the formal public 
     identifiers were changed in the modules and the catalogs.
     No model or attribute changes were made at this time. 
 
 21. JATS became version "1.1" and "v1.1 20151215"
     BITS remained version "2.0" but becomes "v2.0 20151225"

     =============================================================
     JATS Version 1.1d3             (DAL/BTU) v1.1d3  (2015-03-01)
   
     The changes in this release are in response to NISO 
     License and Indicators Recommended Practice.

     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the NISO membership to be changed. This
     Committee Draft 1.1d3 will be sent to the NISO voting
     membership, to become (if approved) NISO JATS 1.1.
     
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee.

 20. JATS became version "1.1d3" and "v1.1 20150301//EN"

     =============================================================
     JATS Version 1.1d2              (DAL/BTU) v1.1d2 (2014-09-30)
   
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the NISO membership to be changed. This
     Committee Draft 1.1d2 will be sent to the NISO voting
     membership, to become (if approved) NISO JATS 1.1.
     
     This catalog represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee.

 19. ATTRIBUTES FOR PUB-ID
   - Added the linking attributes (optional version) to <pub-id> , 
     so that a <pub-id> that is a DOI, for example, can carry the 
     linking attributes and be made into a live link. In this use
     of <pub-id>, the element is acting both as an identifier
     and as a link.
   - Added new attribute @assigning-authority to <pub-id>, to
     carry new values such as "CrossRef" and to take some of the
     semantic burden off the '@pub-id-type' attribute, when
     values are organizations such as "Genbank" or "PDB".
 
 18. VERSION FOR DATASETS - Added new element <version>
     for citing datasets and software. Similar to <edition>
     for printed material.
 
 17. DATA TITLE FOR DATASETS - Added new element <data-title>
     to name datasets and parts of datasets. Acts both as an
     <article-title> equivalent and as a <source> equivalent,
     since many levels of data may be cited.

 16. JATS became version "1.1d2" and "v1.1d2 ********//EN"
   
     =============================================================
     NISO JATS Version 1.1d1           (DAL/BTU) v1.1 (2013-11-15)
   
     ANSI/NISO Z39.96-2012 (pre-release for V1.0 BITS; Version 1.1d1) 

     NISO JATS Standing Committee met and answered the requests and
     suggestions from the NISO request forms.

     Details concerning ANSI/NISO Z39.96-2012 JATS-based DTDs,  
     XSDs, RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.3d2/

 15. ACCESS DATE ELEMENT
     Added attributes for @calendar and @iso-8601-date. The element is
     deprecated but still in use.

 14. NEW <era> ELEMENT
     Added <era> through date-parts.class to:
      - <date-in-citation>

 13. INSTITUTIONS TO STANDARDS ORGANIZATIONS
     Added the elements <institution-wrap> to the following elements
     through the institution-wrap.class:
      - <std-organization>

 12. GLOBAL ATTRIBUTES - Added the new parameter entity 
         %jats-common-atts;
     to every element in this module. This PE adds (for now) the
     @id attribute and the @xml:base attribute to every element,
     whether metadata or narrative.
     Since the @id in this parameter entity is optional, a second
     parameter entity jats-common-atts-id-required was also added.
     The two are kept in sync with the jats-base-atts parameter 
     entity.
   
     =============================================================
     NISO JATS Version 1.0             (DAL/BTU) v1.0 (2012-xx-xx)
   
     ANSI/NISO Z39.96-2012 (Version 1.0) 

     Details concerning ANSI/NISO Z39.96-2012 JATS-based DTDs,  
     XSDs, RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.3d2/
   
 11. EDITION 
       a) Changed the long name of the <edition> element
          ("Edition, Cited)") to "Edition Statement, Cited" to
          indicate that element MAY contain more than just the
          edition number.
       b) Changed the definition of <edition> to include the full
          edition statement and not just the edition number.
       c) Added new element @designator to hold the edition
          number, since the definition of edition is changing.
          (through %edition-atts).
   
 10. DATE IN CITATION ATTRIBUTES - Added to the attributes for
     <date-in-citation> (through @date-in-citation-atts):
       a) ISO 8601 standard date format attribute (@iso-8601-date) 
          Format  YYYY-MM-DDThh:mm:ss 
       b) Name of the calendar used (@calendar), to hold values
          such as "Gregorian", "Japanese" (Emperor years), or
          "Islamic".
   
  9. STANDARD ORGANIZATION - Added new element for the name of
     the standards body that created or that promulgates a
     standard. <std-organization> may be used as part of the
     description of a cited standard inside a citation.
     (New PEs: std-organization-atts; std-organization-elements)
   
  8. STANDARD MODEL - Updated the model for <std>, the description
     of a cited standard in side a citation to include several
     elements:
         - <source> contains the name of the standard
         - <pub-id> contains the standard designator
         - date elements <month>, <day>, and <year> name the 
              official date
         - <std-organization> (new element) names the standards 
             body
         - <named-content> takes anything else a publisher/archive 
             wishes to record, such as the standard status, et al.
      The following elements were retained form the older model
      and so will also be allowed inside <std:>

         - <styled-content> because archives cannot always know the
             reason behind a typographic change
         - the emphasis elements (all face markup)
         - <sub> and <sup>
   
 7.  Updated the DTD-version attribute to "1.0" and the formal
     public identifier to the date: "v1.0 20120330//EN".

     =============================================================
     Version 0.4                       (DAL/BTU) v0.4 (2011-01-31)
   
     This Tag Set is in the process of becoming a NISO standard.
     The version numbers are starting over from 0.4", as a Trial
     Use Draft, to be made into "Version 1.0" when the Tag Suite 
     becomes a NISO standard. Thus, the version number that would
     have been "NLM Version 3.1 Draft" has become NISO JATS
     "Version 0.4".

     Details on NISO Trial Use Draft Version 0.4 are available at
           http://jats.nlm.nih.gov/JATS-0.4.
   
  6. Updated the DTD-version attribute to "0.4" 
   
  5. Updated the public identifier to "v0.4 20110131", 
     modified the formal public identifier to include "JATS (Z39.96)",
     and the filename as delivered to include "JATS" and the
     new version number "0".

  4. @SPECIFIC-USE and @XML:LANG - Added the @specific-use and
     @xml:lang to the following elements:
      - access-date through access-date-atts (only @specific-use)
      - annotation through annotation-atts (both)
      - chapter-title through chapter-title-atts (both) NEW PE
      - comment through comment-atts (both)
      - date-in-citation through date-in-citation-atts (both)
      - edition through edition-atts (both)
      - gov through gov-atts (both)
      - note through note-atts (both)
      - part-title through part-title-atts (both) NEW PE
      - patent through patent-atts (both)
      - person-group through person-group-atts (both)
      - pub-id through pub-id-atts (@specific-use only)
      - ref through ref-atts (both)
      - ref-list through ref-list-atts (@specific-use only)
      - series through series-atts (both)
      - source through source-atts (@specific-use only;
          @xml:lang already)
      - std through std-atts (both)
      - time-stamp through time-stamp-atts (@specific-use only)
      - trans-source through trans-source-atts (@specific-use only;
          @xml:lang already)

  3. PERSON GROUP - Changed the model of <person-group> to mixed
     content. This entailed: creating new PE %person-group-elements;
     that contained the elements to be used and Changing PE
     %person-group-model to (#PCDATA %person-group-elements;)*.
     The PE person-group-model has been retained in this module for
     compatibility, but has been set to the mixed model using
     person-group-elements.

  2. ANNOTATION - Changed to content model to use the parameter
     entity %annotation-model; Content model unchanged.

     =============================================================
     Version 3.0                       (DAL/BTU) v3.0 (2007-10-31)

     Version 3.0 is the first non-backward-compatible release.
     In addition to the usual incremental changes, some
     elements and attributes have been renamed and/or remodeled
     to better meet user needs and to increase tag set consistency.
     All module change histories are available through the Tag Suite
     web site at http://dtd.nlm.nih.gov.

     Details on version 3.0 are available at
           http://dtd.nlm.nih.gov/3.0.

  1. Updated public identifier to "v3.0 20071031//EN"              -->

<!-- ============================================================= -->
<!-- Details concerning 
     ANSI/NISO Z39.96-2015 JATS-based DTDs, XSDs, 
     RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.1/                            -->

<!-- ============================================================= -->
<!--                    PARAMETER ENTITIES FOR #PCDATA MODELS      -->
<!-- ============================================================= -->


<!--                    SOURCE ELEMENTS                            -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        a <source> or <trans-source>.              -->
<!ENTITY % source-elements
                        "| %abbrev.class; |
                         %address-link.class; |%emphasis.class; |
                         %inline-display.class; |
                         %inline-math.class; | %math.class; |
                         %phrase-content.class; | 
                         %simple-link.class; | %subsup.class;"       >


<!-- ============================================================= -->
<!--                    PARAMETER ENTITIES FOR ATTRIBUTE LISTS     -->
<!-- ============================================================= -->


<!--                    ACCESS DATE ATTRIBUTES                     -->
<!--                    Attributes for the <access-date> element   -->
<!ENTITY % access-date-atts
            "%jats-common-atts;                                       
             iso-8601-date
                        CDATA                             #IMPLIED
             calendar   CDATA                             #IMPLIED
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED"  >


<!--                    ANNOTATION ATTRIBUTES                      -->
<!--                    Attributes for the <annotation> element    -->
<!ENTITY % annotation-atts
            "%jats-common-atts;                                       
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    CHAPTER TITLE ATTRIBUTES                   -->
<!--                    Attributes for the <chapter-title> element -->
<!ENTITY % chapter-title-atts
            "%jats-common-atts;                                       
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    COMMENT ATTRIBUTES                         -->
<!--                    Attributes for the <comment> element       -->
<!ENTITY % comment-atts
            "%jats-common-atts;                                       
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    DATA TITLE ATTRIBUTES                      -->
<!--                    Attributes for the <data-title> element    -->
<!ENTITY % data-title-atts
            "%jats-common-atts;                                       
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    DATE IN CITATION ATTRIBUTES                -->
<!--                    Attributes for the <date-in-citation>
                                                         element   -->
<!ENTITY % date-in-citation-atts
            "%jats-common-atts;                                       
             iso-8601-date
                        CDATA                             #IMPLIED
             calendar   CDATA                             #IMPLIED
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    EDITION ATTRIBUTES                         -->
<!--                    Attributes for the <edition> element       -->
<!ENTITY % edition-atts
            "%jats-common-atts;                                       
             designator CDATA                             #IMPLIED
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    GOVERNMENT ATTRIBUTES                      -->
<!--                    Attributes for the <gov> element           -->
<!ENTITY % gov-atts
            "%jats-common-atts;                                       
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    NOTE ATTRIBUTES                            -->
<!--                    Attributes for the <note> element          -->
<!ENTITY % note-atts
            "%jats-common-atts;                                       
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    PART TITLE ATTRIBUTES                      -->
<!--                    Attributes for the <part-title> element    -->
<!ENTITY % part-title-atts
            "%jats-common-atts;                                       
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    PATENT ATTRIBUTES                          -->
<!--                    Attributes for the <patent> element        -->
<!ENTITY % patent-atts
            "%jats-common-atts;                                       
             content-type
                        CDATA                             #IMPLIED
             country    CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    PERSON GROUP ATTRIBUTES                    -->
<!--                    Attributes for the <person-group> element  -->
<!ENTITY % person-group-atts
            "%jats-common-atts;                                       
             person-group-type
                        CDATA                             #IMPLIED
             custom-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    PUBLICATION IDENTIFIER ATTRIBUTES          -->
<!--                    Attributes for the <pub-id> element        -->
<!ENTITY % pub-id-atts
            "%jats-common-atts;                                       
             pub-id-type
                        (%pub-id-types;)                  #IMPLIED
             custom-type
                        CDATA                             #IMPLIED
             assigning-authority                
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             %might-link-atts;"                                      >


<!--                    REFERENCE ATTRIBUTES                       -->
<!--                    Attributes for the <ref> element           -->
<!ENTITY % ref-atts
            "%jats-common-atts;                                       
              content-type
                        CDATA                             #IMPLIED
              specific-use
                        CDATA                             #IMPLIED
              xml:lang  NMTOKEN                           #IMPLIED"  >


<!--                    REFERENCE LIST ATTRIBUTES                  -->
<!--                    Attributes for the <ref-list> element      -->
<!ENTITY % ref-list-atts
            "%jats-common-atts;                                       
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    SERIES ATTRIBUTES                          -->
<!--                    Attributes for the <series> element        -->
<!ENTITY % series-atts
            "%jats-common-atts;                                       
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    SOURCE ATTRIBUTES                          -->
<!--                    Attributes for the <source> element        -->
<!ENTITY % source-atts
            "%jats-common-atts;                                       
             content-type
                       CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    STANDARDS ATTRIBUTES                       -->
<!--                    Attributes for the <std> element           -->
<!ENTITY % std-atts
            "%jats-common-atts;                                       
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    STANDARDS ORGANIZATION ATTRIBUTES          -->
<!--                    Attributes for the <std-organization> 
                        element                                    -->
<!ENTITY % std-organization-atts
            "%jats-common-atts;                                       
             content-type
                       CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    TIME STAMP ATTRIBUTES                      -->
<!--                    Attributes for the <time-stamp> element    -->
<!ENTITY % time-stamp-atts
            "%jats-common-atts;                                       
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED"  >


<!--                    TRANSLATED SOURCE ATTRIBUTES               -->
<!--                    Attributes for the <trans-source> element  -->
<!ENTITY % trans-source-atts
            "%jats-common-atts;                                       
             content-type
                       CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    VERSION ATTRIBUTES                         -->
<!--                    Attributes for the <version> element       -->
<!ENTITY % version-atts
            "%jats-common-atts;                                       
             designator CDATA                             #IMPLIED
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!-- ============================================================= -->
<!--                    BIBLIOGRAPHIC REFERENCE LIST ELEMENTS      -->
<!-- ============================================================= -->


<!--ELEM   article-title
                        Defined in %common.ent;                    -->
<!--ELEM   collab       Defined in %common.ent;                    -->
<!--ELEM   conf-date    Defined in %common.ent;                    -->
<!--ELEM   conf-loc     Defined in %common.ent;                    -->
<!--ELEM   conf-name    Defined in %common.ent;                    -->
<!--ELEM   day          Defined in %common.ent;                    -->
<!--ELEM   elocation-id Defined in %common.ent;                    -->
<!--ELEM   email        Defined in %common.ent;                    -->
<!--ELEM   fpage        Defined in %common.ent;                    -->
<!--ELEM   issn         Defined in %common.ent;                    -->
<!--ELEM   issn-l       Defined in %common.ent;                    -->
<!--ELEM   issue        Defined in %common.ent;                    -->
<!--ELEM   lpage        Defined in %common.ent;                    -->
<!--ELEM   month        Defined in %common.ent;                    -->
<!--ELEM   publisher-loc
                        Defined in %common.ent;                    -->
<!--ELEM   publisher-name
                        Defined in %common.ent;                    -->
<!--ELEM   season       Defined in %common.ent;                    -->
<!--ELEM   title        Defined in %common.ent;                    -->
<!--ELEM   trans-title  Defined in %common.ent;                    -->
<!--ELEM   volume       Defined in %common.ent;                    -->
<!--ELEM   year         Defined in %common.ent;                    -->


<!--                    REFERENCE LIST MODEL                       -->
<!--                    Content model for the <ref-list> element   -->
<!ENTITY % ref-list-model
                        "((%id.class;)*, label?, title?, 
                          (%para-level;)*, ref*, 
                          (%ref-list.class;)* )"                     >


<!--                    REFERENCE LIST (BIBLIOGRAPHIC REFERENCE LIST)
                                                                   -->
<!--                    List of references (citations) for the
                        article.  Often called "References",
                        "Bibliography", or "Additional Reading". No
                        distinction is made between lists of cited
                        references and lists of suggested references.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=ref-list
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=ref-list
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=ref-list
                                                                   -->
<!ELEMENT  ref-list     %ref-list-model;                             >
<!ATTLIST  ref-list
             %ref-list-atts;                                         >


<!--                    REFERENCE ITEM MODEL                       -->
<!--                    Content model for the <ref> element        -->
<!ENTITY % ref-model    "(label?,
                         (%citation.class; | %note.class;)+ )"       >


<!--                    REFERENCE ITEM                             -->
<!--                    One item in a bibliographic list, typically
                        a citation describing a referenced work, but
                        some journals may place notes in this list as
                        well as citations.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=ref
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=ref
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=ref
                                                                   -->
<!ELEMENT  ref          %ref-model;                                  >
<!ATTLIST  ref
             %ref-atts;                                              >


<!--ELEM   element-citation
                        Defined in %common.ent;                    -->
<!--ELEM   mixed-citation
                        Defined in %common.ent;                    -->


<!--                    NOTE IN A REFERENCE LIST MODEL             -->
<!--                    Content model for a note in a reference
                        list element                               -->
<!ENTITY % note-model   "(label?,
                          (%just-para.class; | %product.class;)+ )"  >


<!--                    NOTE IN A REFERENCE LIST                   -->
<!--                    Used to tag non-citation material that
                        sometimes within a reference list, for
                        example, used to tag end note material when
                        such a note is placed within a reference
                        list.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=note
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=note
                                                                   -->
<!ELEMENT  note         %note-model;                                 >
<!ATTLIST  note
             %note-atts;                                             >


<!-- ============================================================= -->
<!--                    BIBLIOGRAPHIC REFERENCE CLASS              -->
<!-- ============================================================= -->


<!--                    ACCESS DATE ELEMENTS                       -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the Access Date <access-date> element      -->
<!ENTITY % access-date-elements
                        ""                                           >


<!--                    ACCESS DATE FOR CITED WORK                 -->
<!--                    The date on which the work which is cited
                        was examined. Some online resources are
                        changing so quickly that a citation to the
                        resource is not complete without the date
                        on which the cited resource was examined,
                        since a day before or a day later the
                        relevant material might be different.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=access-date
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=access-date
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=access-date
                                                                   -->
<!ELEMENT  access-date  (#PCDATA %access-date-elements;)*            >
<!ATTLIST  access-date
             %access-date-atts;                                      >


<!--                    ANNOTATION MODEL                           -->
<!--                    The content model for the <annotation>
                        element                                    -->
<!ENTITY % annotation-model
                        "((%just-para.class;)+)"                     >


<!--                    ANNOTATION IN A CITATION                   -->
<!--                    Most citations just provide the bibliographic
                        information for a cited reference but a few
                        describe or comment upon the nature or
                        quality of the reference or summarize its
                        findings.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=annotation
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=annotation
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=annotation
                                                                   -->
<!ELEMENT  annotation   %annotation-model;                           >
<!ATTLIST  annotation
             %annotation-atts;                                       >


<!--                    CHAPTER TITLE ELEMENTS                     -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the <chapter-title> element                -->
<!ENTITY % chapter-title-elements
                        "%source-elements;"                          >


<!--                    CHAPTER TITLE IN A CITATION                -->
<!--                    Within a citation (such as a <mixed-citation>
                        or an <element-citation>), the title of a
                        cited book is tagged as <source> and the
                        title of a chapter within that book is tagged
                        as <chapter-title>.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=chapter-title
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=chapter-title
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=chapter-title
                                                                   -->
<!ELEMENT  chapter-title
                        (#PCDATA %chapter-title-elements;)*          >
<!ATTLIST  chapter-title
             %chapter-title-atts;                                    >


<!--                    COMMENT ELEMENTS                           -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the Comment in a Citation <comment> element.
                        Design Note: All inline mixes begin with an
                        OR bar, but since %simple-phrase; is an
                        inline mix, the OR bar is already there.   -->
<!ENTITY % comment-elements
                        "%simple-phrase;"                            >


<!--                    COMMENT IN A CITATION                      -->
<!--                    Used to mark unstructured text within an
                        otherwise element structured reference.
                        In an unstructured reference, this text would
                        merely be data characters.
                        Typical comments could include:
                          <comment>[Abstract]</comment>
                          <comment> translated from Russian</comment>
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=comment
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=comment
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=comment
                                                                   -->
<!ELEMENT  comment      (#PCDATA %comment-elements;)*                >
<!ATTLIST  comment
             %comment-atts;                                          >


<!--                    DATA TITLE ELEMENTS                        -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        a <data-title>.                            -->
<!ENTITY % data-title-elements
                        "| %address-link.class; |      
                           %inline-display-noalt.class; | 
                           %emphasis.class; | 
                           %phrase-content.class; | %subsup.class;"  >


<!--                    DATA TITLE IN A CITATION                   -->
<!--                    Within a citation (such as a <mixed-citation>
                        or an <element-citation>), the title of a
                        cited data source such as a dataset or
                        spreadsheet. 
                        REMARKS: Since datasets can contain very
                        complex relationships, for citing data, this 
                        element may describe different levels, taking
                        the place of both <article-title> and <source>
                        when citing data.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=data-title
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=data-title
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=data-title
                                                                   -->
<!ELEMENT  data-title
                        (#PCDATA %data-title-elements;)*             >
<!ATTLIST  data-title
             %data-title-atts;                                       >

<!--                    DATE IN CITATION ELEMENTS                  -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the Date Inside Citation <date-in-citation>
                        element                                    -->
<!ENTITY % date-in-citation-elements
                        "| %date-parts.class;"                       >


<!--                    DATE INSIDE CITATION                       -->
<!--                    A container element for any date that may be
                        referenced in a citation, other than the
                        publication date of the cited work.
                        The "content-type" attribute should be used
                        to identify the purpose/type of date. For
                        example, if the element contains the date
                        on which the article contributor examined the
                        cited work, the attribute might be
                        "access-date".
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=date-in-citation
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=date-in-citation
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=date-in-citation
                                                                   -->
<!ELEMENT  date-in-citation
                        (#PCDATA %date-in-citation-elements;)*       >
<!ATTLIST  date-in-citation
             %date-in-citation-atts;                                 >


<!--                    EDITION ELEMENTS                           -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        <edition>
                        Design Note: -%just-rendition; begins with
                        an OR bar, so this inline mix begins with
                        an OR bar.                                 -->
<!ENTITY % edition-elements
                        "%just-rendition;"                           >


<!--                    EDITION STATEMENT, CITED                   -->
<!--                    The full edition statement for a cited or
                        referenced publication. This may include
                        textual words ("Third print edition revised"),
                        ordinals ("3rd"), superscripted ordinals
                        ("3<sup>rd</sup>), or simply the edition number
                        ("3"). 
                        Remarks: The @designator attribute may be used
                        to record the alphabetic or numerical 
                        edition number.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=edition
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=edition
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=edition
                                                                   -->
<!ELEMENT  edition      (#PCDATA %edition-elements;)*                >
<!ATTLIST  edition
             %edition-atts;                                          >


<!--                    GOVERNMENT REPORT ELEMENTS                 -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        <gov>
                        Design Note: -%rendition-plus; begins with
                        an OR bar, so this inline mix begins with
                        an OR bar.                                 -->
<!ENTITY % gov-elements "%rendition-plus;"                           >


<!--                    GOVERNMENT REPORT, CITED                   -->
<!--                    The identification information (typically the
                        title and/or an identification number) for
                        a cited governmental report or other
                        government publication
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=gov
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=gov
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=gov
                                                                   -->
<!ELEMENT  gov          (#PCDATA %gov-elements;)*                    >
<!ATTLIST  gov
             %gov-atts;                                              >


<!--                    PART TITLE ELEMENTS                        -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the <part-title> element                   -->
<!ENTITY % part-title-elements
                        "%source-elements;"                          >


<!--                    PART TITLE IN A CITATION                   -->
<!--                    Within a citation (<mixed-citation>,
                        <element-citation>), the title of a subpart
                        of a hierarchical source, for example
                        when books are divided into Parts, the title 
                        of the cited Part; for an episode of a TV
                        show or podcast, the title of the episode,
                        etc. The top-level source (the name of the
                        book, TV show, podcast, radio broadcast,
                        etc.) is tagged as <source>.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=part-title
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=part-title
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=part-title
                                                                   -->
<!ELEMENT  part-title   (#PCDATA %part-title-elements;)*             >
<!ATTLIST  part-title
             %part-title-atts;                                       >


<!--                    PATENT NUMBER ELEMENTS                     -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        <patent>
                        Design Note: -%just-rendition; begins with
                        an OR bar, so this inline mix begins with
                        an OR bar.                                 -->
<!ENTITY % patent-elements
                        "%just-rendition;"                           >


<!--                    PATENT NUMBER, CITED                       -->
<!--                    The identification information (typically the
                        patent number or number and name) for a
                        cited patent
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=patent
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=patent
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=patent
                                                                   -->
<!ELEMENT  patent       (#PCDATA %patent-elements;)*                 >
<!ATTLIST  patent
             %patent-atts;                                           >


<!--                    PERSON GROUP ELEMENTS                      -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        <person-group>                             -->
<!ENTITY % person-group-elements
                        "| %name.class; |
                          %person-group-info.class;"                 >


<!--                    PERSON GROUP MODEL                         -->
<!--                    Content model for the Person Group element -->
<!ENTITY % person-group-model
                        "(#PCDATA %person-group-elements;)*"         >


<!--                    PERSON GROUP FOR A CITED PUBLICATION       -->
<!--                    Wrapper element for one or more authors,
                        editors, translators, etc. named in a cited
                        reference.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=person-group
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=person-group
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=person-group
                                                                   -->
<!ELEMENT  person-group %person-group-model;                         >
<!ATTLIST  person-group
             %person-group-atts;                                     >


<!--                    PUBLICATION IDENTIFIER FOR A CITED PUBLICATION
                                                                   -->
<!--                    The identifier of a publication such as a
                        related journal article that is listed
                        within a citation (such as a <mixed-citation>
                        or an <element-citation>) inside the
                        bibliographic reference list <ref-list> of
                        an article.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=pub-id
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=pub-id
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=pub-id
                                                                   -->
<!ELEMENT  pub-id       (#PCDATA)                                    >
<!ATTLIST  pub-id
             %pub-id-atts;                                           >


<!--                    SERIES ELEMENTS                            -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        <series>
                        Design Note: -%rendition-plus; begins with
                        an OR bar, so this inline mix begins with
                        an OR bar.                                 -->
<!ENTITY % series-elements
                        "%rendition-plus;"                           >


<!--                    SERIES                                     -->
<!--                    Container element for any series information
                        used in a citation (such as a <mixed-citation>
                        or an <element-citation>). For example,
                        within a citation to a non-journal item that
                        spans multiple volumes, this element could
                        contain the unique title of the entire series:
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=series
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=series
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=series
                                                                   -->
<!ELEMENT  series       (#PCDATA %series-elements;)*                 >
<!ATTLIST  series
             %series-atts;                                           >


<!--                    STANDARD ELEMENTS                          -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        <std>
                        Design Note: -%rendition-plus; begins with
                        an OR bar, so this inline mix begins with
                        an OR bar.                                 -->
<!ENTITY % std-elements "| %emphasis.class; | 
                           %inline-display-noalt.class; |
                           %phrase-content.class; | %std.class; | 
                           %subsup.class;"                           >


<!--                    STANDARD, CITED                            -->
<!--                    The identification information (typically the
                        standard number, organization, and name) for
                        a cited standard, where "standard" is defined
                        as a document produced by a recognized
                        standards body such ISO, IEEE, OASIS, ANSI,
                        etc.
                        The elements that can be used in the mixed
                        content model of a <std> include:
                          <source> contains the name of the standard
                          <pub-id> contains the standard designator
                          date elements name the official date
                          <std-organization> names the standards body
                          <named-content> takes anything else a 
                            publisher/archive needs.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=std
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=std
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=std
                                                                   -->
<!ELEMENT  std          (#PCDATA %std-elements;)*                    >
<!ATTLIST  std
             %std-atts;                                              >


<!--                    STANDARDS ORGANIZATION ELEMENTS            -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        <std-organization>.                        -->
<!ENTITY % std-organization-elements 
                        "| %emphasis.class; | 
                         %institution-wrap.class; | %subsup.class;"  >


<!--                    STANDARDS ORGANIZATION                     -->
<!--                    The name of the standards body that created 
                        or that promulgates a standard, such as
                        NISO, ISO, ANSI, or industry standards
                        organizations.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=std-organization
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=std-organization
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=std-organization
                                                                   -->
<!ELEMENT  std-organization
                        (#PCDATA %std-organization-elements;)*       >
<!ATTLIST  std-organization
             %std-organization-atts;                                 >


<!--                    SOURCE                                     -->
<!--                    Within a citation, this is the title of a
                        journal, book, conference proceedings, 
                        video, podcast, radio blog, or other resource
                        that is the source of the cited material.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=source
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=source
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=source
                                                                   -->
<!ELEMENT  source       (#PCDATA %source-elements;)*                 >
<!ATTLIST  source
             %source-atts;                                           >


<!--                    TIME STAMP ELEMENTS                        -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        a <time-stamp>.                            -->
<!ENTITY % time-stamp-elements
                        ""                                           >


<!--                    TIME STAMP FOR CITED WORK                  -->
<!--                    Used to record any time stamp that was
                        found on the cited resource when it was
                        examined, for resources such as databases
                        that may use a time signature to identify
                        different versions.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=time-stamp
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=time-stamp
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=time-stamp
                                                                   -->
<!ELEMENT  time-stamp   (#PCDATA %time-stamp-elements;)*             >
<!ATTLIST  time-stamp
             %time-stamp-atts;                                       >


<!--                    TRANSLATED SOURCE                          -->
<!--                    Within a citation, this is the title of a
                        journal, book, conference proceedings, etc.
                        that is the source of the cited material,
                        but with the source name given in a different
                        language from the source as given in the
                        <source> element. For example, if an article
                        is originally in French, the <source> name
                        would be the French name and the
                        <trans-source> might be in English.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=trans-source
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=trans-source
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=trans-source
                                                                   -->
<!ELEMENT  trans-source (#PCDATA %source-elements;)*                 >
<!ATTLIST  trans-source
             %trans-source-atts;                                     >


<!--                    VERSION ELEMENTS                           -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        <version>.
                        Design Note: -%just-rendition; begins with
                        an OR bar, so this inline mix begins with
                        an OR bar.                                 -->
<!ENTITY % version-elements
                        "%just-rendition;"                           >


<!--                    VERSION STATEMENT, CITED                   -->
<!--                    The version number for a cited dataset or
                        software package. This is similar to an
                        edition statement for a publication, in that it
                        may include textual words ("9th"), superscripted 
                        ordinals ("3<sup>rd</sup>), or simply the 
                        version number ("16.2"). 
                        Remarks: The @designator attribute may be used
                        to record the alphabetic or numerical 
                        version number, when the version number contains
                        more than just a number, so the numeric version
                        can be preserved for search and comparison.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=version
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=version
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=version
                                                                   -->
<!ELEMENT  version      (#PCDATA %version-elements;)*                >
<!ATTLIST  version
             %version-atts;                                          >


<!-- ================== End Bibliographic Class Module =========== -->

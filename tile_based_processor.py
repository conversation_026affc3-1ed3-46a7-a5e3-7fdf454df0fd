# tile_based_processor.py
"""
瓦片式文献处理器
专门设计用于处理大量文献（如60篇），确保所有文献都被纳入分析
采用智能分组和瓦片式处理策略，适应小模型上下文限制
"""

import logging
import json
import re
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict, Counter
from dataclasses import dataclass
import time

logger = logging.getLogger(__name__)


@dataclass
class LiteratureTile:
    """文献瓦片数据结构"""
    tile_id: str
    studies: List[Dict[str, Any]]
    theme: str
    sub_theme: str
    processing_priority: int
    estimated_tokens: int
    processed: bool = False
    evidence_points: List[Dict] = None
    
    def __post_init__(self):
        if self.evidence_points is None:
            self.evidence_points = []


class TileBasedProcessor:
    """
    瓦片式文献处理器
    将大量文献智能分组为小瓦片，确保每个瓦片都在上下文限制内
    """
    
    def __init__(self, llm_manager, max_tile_size: int = 8, max_tokens_per_tile: int = 1500):
        self.llm = llm_manager
        self.max_tile_size = max_tile_size  # 每个瓦片最大文献数
        self.max_tokens_per_tile = max_tokens_per_tile  # 每个瓦片最大token数
        self.tiles: List[LiteratureTile] = []
        self.processing_stats = {
            'total_studies': 0,
            'total_tiles': 0,
            'processed_tiles': 0,
            'failed_tiles': 0,
            'total_evidence_points': 0
        }
        
    def create_intelligent_tiles(self, studies: List[Dict[str, Any]], topic: str) -> List[LiteratureTile]:
        """
        智能创建文献瓦片
        根据主题、研究类型、发表年份等进行分组
        """
        try:
            self.processing_stats['total_studies'] = len(studies)
            logger.info(f"开始为{len(studies)}篇文献创建智能瓦片...")
            
            # 1. 预处理和清洗文献
            cleaned_studies = self._clean_and_validate_studies(studies, topic)
            logger.info(f"清洗后保留{len(cleaned_studies)}篇有效文献")
            
            # 2. 智能分组
            grouped_studies = self._intelligent_grouping(cleaned_studies, topic)
            logger.info(f"创建了{len(grouped_studies)}个主题组")
            
            # 3. 创建瓦片
            tiles = []
            tile_counter = 0
            
            for theme, theme_studies in grouped_studies.items():
                # 按子主题进一步分组
                sub_groups = self._create_sub_groups(theme_studies, theme)
                
                for sub_theme, sub_studies in sub_groups.items():
                    # 将子组分割为适当大小的瓦片
                    sub_tiles = self._split_into_tiles(sub_studies, theme, sub_theme, tile_counter)
                    tiles.extend(sub_tiles)
                    tile_counter += len(sub_tiles)
            
            self.tiles = tiles
            self.processing_stats['total_tiles'] = len(tiles)
            
            logger.info(f"成功创建{len(tiles)}个文献瓦片")
            return tiles
            
        except Exception as e:
            logger.error(f"创建智能瓦片失败: {e}")
            return []
    
    def _clean_and_validate_studies(self, studies: List[Dict[str, Any]], topic: str) -> List[Dict[str, Any]]:
        """根据主题清洗和验证文献"""
        cleaned = []
        topic_keywords = self._extract_topic_keywords(topic)
        
        for i, study in enumerate(studies):
            try:
                # 基本验证
                if not isinstance(study, dict):
                    continue
                
                title = study.get('title', '').strip()
                abstract = study.get('abstract', '').strip()
                
                if not title or len(title) < 10:
                    logger.warning(f"研究{i+1}标题过短或缺失")
                    continue
                
                if not abstract or len(abstract) < 50:
                    logger.warning(f"研究{i+1}摘要过短或缺失")
                    continue
                
                # 主题相关性验证
                relevance_score = self._calculate_relevance_score(study, topic_keywords)
                if relevance_score < 0.3:  # 相关性阈值
                    logger.info(f"研究{i+1}与主题相关性较低，跳过")
                    continue
                
                # 标准化研究数据
                cleaned_study = self._standardize_study_data(study, i+1)
                cleaned_study['relevance_score'] = relevance_score
                cleaned.append(cleaned_study)
                
            except Exception as e:
                logger.warning(f"清洗研究{i+1}时出错: {e}")
                continue
        
        return cleaned
    
    def _extract_topic_keywords(self, topic: str) -> List[str]:
        """从主题中提取关键词"""
        # 简单的关键词提取，可以根据需要改进
        keywords = []
        
        # 中文关键词提取
        chinese_keywords = re.findall(r'[\u4e00-\u9fff]+', topic)
        keywords.extend([kw for kw in chinese_keywords if len(kw) >= 2])
        
        # 英文关键词提取
        english_keywords = re.findall(r'[a-zA-Z]+', topic)
        keywords.extend([kw.lower() for kw in english_keywords if len(kw) >= 3])
        
        return list(set(keywords))
    
    def _calculate_relevance_score(self, study: Dict[str, Any], topic_keywords: List[str]) -> float:
        """计算研究与主题的相关性评分"""
        if not topic_keywords:
            return 1.0
        
        title = study.get('title', '').lower()
        abstract = study.get('abstract', '').lower()
        text = f"{title} {abstract}"
        
        matches = 0
        for keyword in topic_keywords:
            if keyword.lower() in text:
                matches += 1
        
        return matches / len(topic_keywords) if topic_keywords else 0.0
    
    def _standardize_study_data(self, study: Dict[str, Any], study_id: int) -> Dict[str, Any]:
        """标准化研究数据格式"""
        return {
            'id': study.get('id', f"study_{study_id}"),
            'title': study.get('title', '').strip(),
            'authors': study.get('authors', []),
            'year': str(study.get('year', 'N/A')),
            'abstract': study.get('abstract', '').strip(),
            'clinical_data': study.get('clinical_data', {}),
            'url': study.get('url', ''),
            'doi': study.get('doi', ''),
            'journal': study.get('journal', ''),
            'keywords': study.get('keywords', []),
            'study_type': self._identify_study_type(study),
            'population': self._extract_population(study),
            'intervention': self._extract_intervention(study),
            'outcome': self._extract_outcome(study)
        }
    
    def _identify_study_type(self, study: Dict[str, Any]) -> str:
        """识别研究类型"""
        title = study.get('title', '').lower()
        abstract = study.get('abstract', '').lower()
        text = f"{title} {abstract}"
        
        # 研究类型关键词
        if any(keyword in text for keyword in ['randomized', 'rct', 'random']):
            return 'RCT'
        elif any(keyword in text for keyword in ['cohort', 'prospective']):
            return 'Cohort Study'
        elif any(keyword in text for keyword in ['case-control', 'case control']):
            return 'Case-Control Study'
        elif any(keyword in text for keyword in ['cross-sectional', 'cross sectional']):
            return 'Cross-sectional Study'
        elif any(keyword in text for keyword in ['systematic review', 'meta-analysis']):
            return 'Systematic Review'
        elif any(keyword in text for keyword in ['case report', 'case series']):
            return 'Case Report/Series'
        else:
            return 'Other'
    
    def _extract_population(self, study: Dict[str, Any]) -> str:
        """提取研究人群信息"""
        clinical_data = study.get('clinical_data', {})
        population = clinical_data.get('population', '')
        
        if not population:
            # 从摘要中尝试提取
            abstract = study.get('abstract', '')
            # 简单的人群提取逻辑
            population_patterns = [
                r'patients?\s+with\s+([^.]+)',
                r'(\d+)\s+patients?',
                r'adults?\s+with\s+([^.]+)',
                r'children\s+with\s+([^.]+)'
            ]
            
            for pattern in population_patterns:
                match = re.search(pattern, abstract, re.IGNORECASE)
                if match:
                    population = match.group(1) if len(match.groups()) > 0 else match.group(0)
                    break
        
        return population[:100] if population else 'Not specified'
    
    def _extract_intervention(self, study: Dict[str, Any]) -> str:
        """提取干预措施信息"""
        clinical_data = study.get('clinical_data', {})
        intervention = clinical_data.get('intervention', '')
        
        if not intervention:
            # 从标题和摘要中尝试提取
            title = study.get('title', '')
            abstract = study.get('abstract', '')
            text = f"{title} {abstract}"
            
            # 简单的干预提取逻辑
            intervention_patterns = [
                r'treatment\s+with\s+([^.]+)',
                r'therapy\s+with\s+([^.]+)',
                r'administration\s+of\s+([^.]+)',
                r'use\s+of\s+([^.]+)'
            ]
            
            for pattern in intervention_patterns:
                match = re.search(pattern, text, re.IGNORECASE)
                if match:
                    intervention = match.group(1)
                    break
        
        return intervention[:100] if intervention else 'Not specified'
    
    def _extract_outcome(self, study: Dict[str, Any]) -> str:
        """提取结局指标信息"""
        clinical_data = study.get('clinical_data', {})
        outcomes = clinical_data.get('outcomes', '')
        
        if not outcomes:
            # 从摘要中尝试提取
            abstract = study.get('abstract', '')
            outcome_patterns = [
                r'mortality',
                r'survival',
                r'efficacy',
                r'safety',
                r'outcome',
                r'endpoint'
            ]
            
            found_outcomes = []
            for pattern in outcome_patterns:
                if re.search(pattern, abstract, re.IGNORECASE):
                    found_outcomes.append(pattern)
            
            outcomes = ', '.join(found_outcomes) if found_outcomes else 'Not specified'
        
        return outcomes[:100] if outcomes else 'Not specified'

    def _intelligent_grouping(self, studies: List[Dict[str, Any]], topic: str) -> Dict[str, List[Dict[str, Any]]]:
        """智能分组文献"""
        groups = defaultdict(list)

        # 1. 按研究类型分组
        type_groups = defaultdict(list)
        for study in studies:
            study_type = study.get('study_type', 'Other')
            type_groups[study_type].append(study)

        # 2. 按主题相关性进一步细分
        for study_type, type_studies in type_groups.items():
            if len(type_studies) <= self.max_tile_size:
                # 如果数量较少，直接作为一组
                groups[f"{study_type}"] = type_studies
            else:
                # 按相关性和其他特征进一步分组
                sub_groups = self._create_thematic_subgroups(type_studies, study_type)
                for sub_name, sub_studies in sub_groups.items():
                    groups[f"{study_type}_{sub_name}"] = sub_studies

        return dict(groups)

    def _create_thematic_subgroups(self, studies: List[Dict[str, Any]], base_theme: str) -> Dict[str, List[Dict[str, Any]]]:
        """创建主题子组"""
        sub_groups = defaultdict(list)

        # 按人群类型分组
        population_groups = defaultdict(list)
        for study in studies:
            population = study.get('population', 'Not specified')
            # 提取关键人群特征
            if 'sepsis' in population.lower() or 'septic' in population.lower():
                pop_key = 'sepsis'
            elif 'hypertension' in population.lower() or 'hypertensive' in population.lower():
                pop_key = 'hypertension'
            elif 'cardiac' in population.lower() or 'heart' in population.lower():
                pop_key = 'cardiac'
            elif 'renal' in population.lower() or 'kidney' in population.lower():
                pop_key = 'renal'
            elif 'pediatric' in population.lower() or 'children' in population.lower():
                pop_key = 'pediatric'
            elif 'maternal' in population.lower() or 'pregnancy' in population.lower():
                pop_key = 'maternal'
            else:
                pop_key = 'general'

            population_groups[pop_key].append(study)

        # 如果某个人群组太大，按年份进一步分组
        for pop_key, pop_studies in population_groups.items():
            if len(pop_studies) <= self.max_tile_size:
                sub_groups[pop_key] = pop_studies
            else:
                # 按发表年份分组
                year_groups = self._group_by_year(pop_studies)
                for year_range, year_studies in year_groups.items():
                    sub_groups[f"{pop_key}_{year_range}"] = year_studies

        return dict(sub_groups)

    def _group_by_year(self, studies: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """按年份分组"""
        year_groups = defaultdict(list)

        for study in studies:
            year = study.get('year', 'N/A')
            try:
                year_int = int(year) if year != 'N/A' else 2020
                # 按5年区间分组
                year_range = f"{(year_int // 5) * 5}-{(year_int // 5) * 5 + 4}"
                year_groups[year_range].append(study)
            except (ValueError, TypeError):
                year_groups['unknown'].append(study)

        return dict(year_groups)

    def _create_sub_groups(self, studies: List[Dict[str, Any]], theme: str) -> Dict[str, List[Dict[str, Any]]]:
        """创建子组"""
        if len(studies) <= self.max_tile_size:
            return {f"{theme}_main": studies}

        # 按干预类型分组
        intervention_groups = defaultdict(list)
        for study in studies:
            intervention = study.get('intervention', 'Not specified').lower()

            if 'drug' in intervention or 'medication' in intervention:
                int_key = 'pharmacological'
            elif 'surgery' in intervention or 'surgical' in intervention:
                int_key = 'surgical'
            elif 'therapy' in intervention or 'treatment' in intervention:
                int_key = 'therapeutic'
            elif 'management' in intervention or 'care' in intervention:
                int_key = 'management'
            else:
                int_key = 'other'

            intervention_groups[int_key].append(study)

        # 确保每个子组不超过最大大小
        final_groups = {}
        for int_key, int_studies in intervention_groups.items():
            if len(int_studies) <= self.max_tile_size:
                final_groups[int_key] = int_studies
            else:
                # 进一步分割
                chunks = self._split_list(int_studies, self.max_tile_size)
                for i, chunk in enumerate(chunks):
                    final_groups[f"{int_key}_{i+1}"] = chunk

        return final_groups

    def _split_into_tiles(self, studies: List[Dict[str, Any]], theme: str, sub_theme: str, start_id: int) -> List[LiteratureTile]:
        """将研究分割为瓦片"""
        tiles = []

        # 按优先级排序（相关性高的优先）
        sorted_studies = sorted(studies, key=lambda x: x.get('relevance_score', 0), reverse=True)

        # 分割为瓦片
        chunks = self._split_list(sorted_studies, self.max_tile_size)

        for i, chunk in enumerate(chunks):
            tile_id = f"tile_{start_id + i + 1:03d}"

            # 估算token数
            estimated_tokens = self._estimate_tokens(chunk)

            # 确定处理优先级
            priority = self._calculate_priority(chunk, theme, sub_theme)

            tile = LiteratureTile(
                tile_id=tile_id,
                studies=chunk,
                theme=theme,
                sub_theme=sub_theme,
                processing_priority=priority,
                estimated_tokens=estimated_tokens
            )

            tiles.append(tile)

        return tiles

    def _split_list(self, items: List, chunk_size: int) -> List[List]:
        """将列表分割为指定大小的块"""
        return [items[i:i + chunk_size] for i in range(0, len(items), chunk_size)]

    def _estimate_tokens(self, studies: List[Dict[str, Any]]) -> int:
        """估算瓦片的token数量"""
        total_chars = 0
        for study in studies:
            title = study.get('title', '')
            abstract = study.get('abstract', '')
            total_chars += len(title) + len(abstract)

        # 粗略估算：4个字符约等于1个token
        return total_chars // 4

    def _calculate_priority(self, studies: List[Dict[str, Any]], theme: str, sub_theme: str) -> int:
        """计算处理优先级"""
        # 基础优先级
        base_priority = 5

        # 根据研究类型调整
        study_types = [study.get('study_type', 'Other') for study in studies]
        if 'RCT' in study_types:
            base_priority += 3
        elif 'Cohort Study' in study_types:
            base_priority += 2
        elif 'Systematic Review' in study_types:
            base_priority += 4

        # 根据相关性调整
        avg_relevance = sum(study.get('relevance_score', 0) for study in studies) / len(studies)
        base_priority += int(avg_relevance * 3)

        # 根据主题重要性调整
        if 'sepsis' in theme.lower() or 'hypertension' in theme.lower():
            base_priority += 2

        return min(base_priority, 10)  # 最大优先级为10

    def process_all_tiles(self, provider: str, model: str, max_retries: int = 3) -> Dict[str, Any]:
        """
        处理所有瓦片，确保所有文献都被分析
        包含容错处理机制
        """
        try:
            logger.info(f"开始处理{len(self.tiles)}个瓦片...")

            # 按优先级排序
            sorted_tiles = sorted(self.tiles, key=lambda x: x.processing_priority, reverse=True)

            all_evidence_points = []
            processing_results = {
                'successful_tiles': 0,
                'failed_tiles': 0,
                'total_evidence_points': 0,
                'processing_errors': [],
                'tile_results': {}
            }

            for i, tile in enumerate(sorted_tiles):
                logger.info(f"处理瓦片 {i+1}/{len(sorted_tiles)}: {tile.tile_id} ({tile.theme}/{tile.sub_theme})")

                # 处理单个瓦片，包含重试机制
                tile_result = self._process_single_tile(tile, provider, model, max_retries)

                if tile_result['success']:
                    tile.processed = True
                    tile.evidence_points = tile_result['evidence_points']
                    all_evidence_points.extend(tile_result['evidence_points'])
                    processing_results['successful_tiles'] += 1
                    processing_results['total_evidence_points'] += len(tile_result['evidence_points'])

                    logger.info(f"✅ 瓦片 {tile.tile_id} 处理成功，提取{len(tile_result['evidence_points'])}个证据点")
                else:
                    processing_results['failed_tiles'] += 1
                    processing_results['processing_errors'].append({
                        'tile_id': tile.tile_id,
                        'error': tile_result['error']
                    })
                    logger.error(f"❌ 瓦片 {tile.tile_id} 处理失败: {tile_result['error']}")

                processing_results['tile_results'][tile.tile_id] = tile_result

                # 短暂延迟，避免API限制
                time.sleep(0.5)

            # 更新统计信息
            self.processing_stats.update({
                'processed_tiles': processing_results['successful_tiles'],
                'failed_tiles': processing_results['failed_tiles'],
                'total_evidence_points': processing_results['total_evidence_points']
            })

            logger.info(f"瓦片处理完成: {processing_results['successful_tiles']}/{len(sorted_tiles)} 成功")

            return {
                'success': True,
                'evidence_points': all_evidence_points,
                'processing_stats': self.processing_stats,
                'detailed_results': processing_results
            }

        except Exception as e:
            logger.error(f"瓦片处理过程中出现严重错误: {e}")
            return {
                'success': False,
                'error': str(e),
                'evidence_points': [],
                'processing_stats': self.processing_stats
            }

    def _process_single_tile(self, tile: LiteratureTile, provider: str, model: str, max_retries: int) -> Dict[str, Any]:
        """
        处理单个瓦片，包含容错机制
        """
        for attempt in range(max_retries):
            try:
                logger.debug(f"瓦片 {tile.tile_id} 第{attempt+1}次尝试...")

                # 创建针对该瓦片的专门提示词
                prompt = self._create_tile_processing_prompt(tile)

                # 调用LLM处理
                response = self.llm.generate_content(prompt, provider, model)

                if not response or response.startswith("ERROR"):
                    raise ValueError(f"LLM响应无效: {response}")

                # 解析响应
                evidence_points = self._parse_tile_response(response, tile)

                return {
                    'success': True,
                    'evidence_points': evidence_points,
                    'attempts': attempt + 1,
                    'response': response
                }

            except Exception as e:
                logger.warning(f"瓦片 {tile.tile_id} 第{attempt+1}次尝试失败: {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # 指数退避
                else:
                    return {
                        'success': False,
                        'error': str(e),
                        'evidence_points': [],
                        'attempts': attempt + 1
                    }

    def _create_tile_processing_prompt(self, tile: LiteratureTile) -> str:
        """为瓦片创建专门的处理提示词"""
        studies_info = []
        for i, study in enumerate(tile.studies):
            study_info = f"""
研究{i+1}:
- 标题: {study.get('title', '')[:100]}...
- 类型: {study.get('study_type', 'Unknown')}
- 人群: {study.get('population', 'Not specified')[:50]}...
- 干预: {study.get('intervention', 'Not specified')[:50]}...
- 结局: {study.get('outcome', 'Not specified')[:50]}...
- 摘要: {study.get('abstract', '')[:200]}...
"""
            studies_info.append(study_info)

        prompt = f"""
你是循证医学专家，请分析以下{len(tile.studies)}项关于"{tile.theme}/{tile.sub_theme}"的研究，提取关键证据点。

研究信息:
{''.join(studies_info)}

请为每项研究提取以下信息（JSON格式）：
1. 研究设计和质量
2. 主要发现和效应量
3. 统计显著性
4. 临床意义
5. 局限性

输出格式：
{{
  "evidence_points": [
    {{
      "study_id": "研究ID",
      "study_title": "研究标题",
      "evidence_type": "主要证据类型",
      "key_finding": "关键发现",
      "effect_size": "效应量",
      "confidence_interval": "置信区间",
      "p_value": "P值",
      "sample_size": "样本量",
      "quality_assessment": "质量评估",
      "clinical_significance": "临床意义",
      "limitations": "主要局限性"
    }}
  ]
}}

请确保提取的信息准确、具体，避免模糊表述。
"""
        return prompt

    def _parse_tile_response(self, response: str, tile: LiteratureTile) -> List[Dict[str, Any]]:
        """解析瓦片处理响应"""
        try:
            # 尝试解析JSON响应
            if '{' in response and '}' in response:
                # 提取JSON部分
                start_idx = response.find('{')
                end_idx = response.rfind('}') + 1
                json_str = response[start_idx:end_idx]

                parsed_data = json.loads(json_str)
                evidence_points = parsed_data.get('evidence_points', [])

                # 验证和标准化证据点
                validated_points = []
                for point in evidence_points:
                    validated_point = self._validate_evidence_point(point, tile)
                    if validated_point:
                        validated_points.append(validated_point)

                return validated_points
            else:
                # 如果不是JSON格式，尝试文本解析
                return self._parse_text_response(response, tile)

        except json.JSONDecodeError as e:
            logger.warning(f"JSON解析失败: {e}，尝试文本解析")
            return self._parse_text_response(response, tile)
        except Exception as e:
            logger.error(f"响应解析失败: {e}")
            return []

    def _validate_evidence_point(self, point: Dict[str, Any], tile: LiteratureTile) -> Optional[Dict[str, Any]]:
        """验证和标准化证据点"""
        try:
            # 必需字段检查
            required_fields = ['study_id', 'study_title', 'evidence_type', 'key_finding']
            for field in required_fields:
                if not point.get(field):
                    logger.warning(f"证据点缺少必需字段: {field}")
                    return None

            # 标准化证据点
            validated_point = {
                'study_id': str(point.get('study_id', '')),
                'study_title': str(point.get('study_title', ''))[:200],
                'evidence_type': str(point.get('evidence_type', 'General')),
                'key_finding': str(point.get('key_finding', '')),
                'effect_size': str(point.get('effect_size', 'Not reported')),
                'confidence_interval': str(point.get('confidence_interval', 'Not reported')),
                'p_value': str(point.get('p_value', 'Not reported')),
                'sample_size': self._parse_sample_size(point.get('sample_size', 'Not reported')),
                'quality_assessment': str(point.get('quality_assessment', 'Not assessed')),
                'clinical_significance': str(point.get('clinical_significance', 'Not specified')),
                'limitations': str(point.get('limitations', 'Not specified')),
                'tile_id': tile.tile_id,
                'theme': tile.theme,
                'sub_theme': tile.sub_theme,
                'processing_priority': tile.processing_priority
            }

            return validated_point

        except Exception as e:
            logger.error(f"验证证据点失败: {e}")
            return None

    def _parse_sample_size(self, sample_size_str: str) -> int:
        """解析样本量"""
        try:
            if isinstance(sample_size_str, int):
                return sample_size_str

            # 提取数字
            numbers = re.findall(r'\d+', str(sample_size_str))
            if numbers:
                return int(numbers[0])
            else:
                return 0
        except (ValueError, TypeError):
            return 0

    def _parse_text_response(self, response: str, tile: LiteratureTile) -> List[Dict[str, Any]]:
        """解析文本格式的响应"""
        evidence_points = []

        try:
            # 简单的文本解析逻辑
            lines = response.split('\n')
            current_point = {}

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 检查是否是新的研究开始
                if line.startswith('研究') or line.startswith('Study'):
                    if current_point:
                        evidence_points.append(current_point)
                    current_point = {
                        'study_id': f"tile_{tile.tile_id}_study_{len(evidence_points)+1}",
                        'study_title': line,
                        'evidence_type': 'Text_Parsed',
                        'key_finding': '',
                        'tile_id': tile.tile_id,
                        'theme': tile.theme,
                        'sub_theme': tile.sub_theme
                    }
                elif current_point:
                    # 累积关键发现
                    current_point['key_finding'] += f" {line}"

            # 添加最后一个证据点
            if current_point:
                evidence_points.append(current_point)

            return evidence_points

        except Exception as e:
            logger.error(f"文本解析失败: {e}")
            return []

    def get_processing_summary(self) -> Dict[str, Any]:
        """获取处理摘要"""
        return {
            'total_studies': self.processing_stats['total_studies'],
            'total_tiles': self.processing_stats['total_tiles'],
            'processed_tiles': self.processing_stats['processed_tiles'],
            'failed_tiles': self.processing_stats['failed_tiles'],
            'success_rate': self.processing_stats['processed_tiles'] / self.processing_stats['total_tiles'] if self.processing_stats['total_tiles'] > 0 else 0,
            'total_evidence_points': self.processing_stats['total_evidence_points'],
            'average_evidence_per_tile': self.processing_stats['total_evidence_points'] / self.processing_stats['processed_tiles'] if self.processing_stats['processed_tiles'] > 0 else 0,
            'tiles_by_theme': self._get_tiles_by_theme(),
            'processing_efficiency': self._calculate_processing_efficiency()
        }

    def _get_tiles_by_theme(self) -> Dict[str, int]:
        """按主题统计瓦片数量"""
        theme_counts = defaultdict(int)
        for tile in self.tiles:
            theme_counts[tile.theme] += 1
        return dict(theme_counts)

    def _calculate_processing_efficiency(self) -> Dict[str, float]:
        """计算处理效率"""
        if not self.tiles:
            return {'studies_per_tile': 0, 'evidence_per_study': 0}

        total_studies = sum(len(tile.studies) for tile in self.tiles)
        processed_studies = sum(len(tile.studies) for tile in self.tiles if tile.processed)

        return {
            'studies_per_tile': total_studies / len(self.tiles),
            'evidence_per_study': self.processing_stats['total_evidence_points'] / total_studies if total_studies > 0 else 0,
            'processing_coverage': processed_studies / total_studies if total_studies > 0 else 0
        }

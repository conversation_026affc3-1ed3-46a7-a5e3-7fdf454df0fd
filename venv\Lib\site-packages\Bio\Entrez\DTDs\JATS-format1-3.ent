<!-- ============================================================= -->
<!--  MODULE:    Formatting Element Classes                        -->
<!--  VERSION:   ANSI/NISO JATS Version 1.3 (Z39.96-2021)          -->
<!--  DATE:      June 2021                                         -->
<!--                                                               -->
<!-- ============================================================= -->

<!-- ============================================================= -->
<!--                    PUBLIC DOCUMENT TYPE DEFINITION            -->
<!--                        TYPICAL INVOCATION                     -->
<!--
 "-//NLM//DTD JATS (Z39.96) JATS DTD Suite Formatting Element Classes v1.3 20210610//EN"
  Delivered as file "JATS-format1-3.ent"                           -->
<!-- ============================================================= -->

<!-- ============================================================= -->
<!-- SYSTEM:     JATS DTD Suite                                    -->
<!--                                                               -->
<!-- PURPOSE:    Elements concerned with rendition of output, for  -->
<!--             example printing on a page or display on a screen -->
<!--                                                               -->
<!-- TAG SET SPONSOR                                               -->
<!--             National Center for Biotechnology                 -->
<!--                Information (NCBI)                             -->
<!--             National Library of Medicine (NLM)                -->
<!--                                                               -->
<!-- CREATED FOR:                                                  -->
<!--             This module was created for the JATS DTD Suite.   -->
<!--             Digital archives and publishers may use the       -->
<!--             DTD as is for markup of journal literature or     -->
<!--             related material for archiving and transferring   -->
<!--             such material between archives or create a        -->
<!--             custom XML DTD from the Suite for                 -->
<!--             these purposes.                                   -->
<!--                                                               -->
<!--             This DTD is in the public domain. An organization -->
<!--             that wishes to create its own DTD from the suite  -->
<!--             may do so without permission from NLM.            -->
<!--                                                               -->
<!--             The suite has been set up to be extended using a  -->
<!--             new DTD file and a new DTD-specific customization -->
<!--             module to redefine the many Parameter Entities.   -->
<!--             Do not modify the suite directly or redistribute  -->
<!--             modified versions of the suite.                   -->
<!--                                                               -->
<!--             In the interest of maintaining consistency and    -->
<!--             clarity for potential users, NLM requests:        -->
<!--                                                               -->
<!--             1. If you create a DTD from the JATS DTD Suite    -->
<!--                and intend to stay compatible with the suite,  -->
<!--                then please include the following statement    -->
<!--                as a comment in all of your DTD modules:       -->
<!--                   "Created from, and fully compatible with,   -->
<!--                    the ANSI/NISO Z39.96 Journal Article Tag   -->
<!--                    Suite (JATS)."                             -->
<!--                                                               -->
<!--             2. If you alter one or more modules of the suite, -->
<!--                then please rename your version and all its    -->
<!--                modules to avoid any confusion with the        -->
<!--                original suite. Also, please include the       -->
<!--                following statement as a comment in all your   -->
<!--                DTD modules:                                   -->
<!--                   "Based in part on, but not fully compatible -->
<!--                    with, the ANSI/NISO Z39.96 Journal Article -->
<!--                    Tag Suite (JATS)."                         -->
<!--                                                               -->
<!-- ORIGINAL CREATION DATE:                                       -->
<!--             December 2002                                     -->
<!--                                                               -->
<!-- CREATED BY: Mulberry Technologies, Inc. for the NISO Z39.96   -->
<!--             Working Group. Mulberry Technologies was          -->
<!--             supported by National Center for Biotechnology    -->
<!--             Information (NCBI), a center of the US National   -->
<!--             Library of Medicine (NLM).                        -->
<!--                                                               -->
<!--             This module is part of the JATS DTD Suite. The    -->
<!--             Suite is a continuation of work done by NCBI,     -->
<!--             Mulberry Technologies, and Inera, Inc. on the NLM -->
<!--             Journal Archiving and Interchange DTD Suite, which-->
<!--             was originally released in December, 2002.        -->
<!--                                                               -->
<!--             NLM thanks the Harvard University Libraries, both -->
<!--             for proposing that a draft archiving NLM DTD for  -->
<!--             life sciences journals be extended to accommodate -->
<!--             journals in all disciplines and for sponsoring    -->
<!--             Inera Inc.'s collaboration with other DTD         -->
<!--             authors in completing NLM Version 1.0. The        -->
<!--             Andrew W. Mellon Foundation provided support for  -->
<!--             these important contributions.                    -->
<!--                                                               -->
<!--             Suggestions for refinements and enhancements to   -->
<!--             this DTD should be sent in email to:              -->
<!--                 <EMAIL>                         -->
<!-- ============================================================= -->


<!-- ============================================================= -->
<!--                    DTD VERSION/CHANGE HISTORY                 -->
<!-- ============================================================= -->
<!--
Version  Reason/Occasion                   (who) vx.x (yyyy-mm-dd)
   ==============================================================
     JATS Version 1.3 (ANSI/NISO Z39.96-2021)
                                  (DAL/BTU) v1.3 (2021-06-10)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by both the NISO and ANSI memberships 
     to be changed. This version of the NISO JATS was approved 
     by ANSI vote on June 10, 2021, so it supersedes all Committee 
     Drafts as well as the draft version JATS 1.3 2021 (the version
     submitted for vote), becoming NISO JATS v1.3 20210610 
     (ANSI/NISO Z39.96-2019).

     The file names and formal public identifiers have been 
     changed in the modules and the catalogs.

 22. JATS 'v1.3d2 20201130' becomes 'v1.3 20210610' 
     (final ANSI vote date)
     'ANSI/NISO Z39.96-2019' becomes 'ANSI/NISO Z39.96-2021'

  ==============================================================
     JATS Version 1.3d2 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.3d2 (2020-11-30)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by the NISO and ANSI memberships 
     to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     of the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting; it is for Standing Committee review and
     testing. The formal public identifiers have been changed 
     in the modules and the catalogs.
 
 21. JATS "v1.3d2 20201130" becomes 'v1.3 20210610' (second 
     committee draft following the final ANSI/NISO 1.2 vote)

   ==============================================================
     JATS Version 1.3d1 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.3.d1 (2019-08-31)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by the NISO and ANSI memberships 
     to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     of the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
  
 20. JATS "1.2" becomes "v1.3d1 20190831" (first committee
     draft following the final ANSI/NISO vote) 

   ==============================================================
     JATS Version 1.2 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.2     (2019-02-08)
    
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the both the NISO and ANSI membership 
     to be changed. This version of the NISO JATS was approved 
     by ANSI vote on Feb 08, 2019, so it supersedes all Committee 
     Drafts as well as the draft version JATS 1.2 2018 (the version
     submitted for vote), becoming NISO JATS v1.2 20190208 
     (ANSI/NISO Z39.96-2021).
  
 19. JATS "1.2" becomes "v1.2 20190208" (final ANSI vote date)
     "ANSI/NISO Z39.96-2015" becomes "ANSI/NISO Z39.96-2019"
     
   ==============================================================
     JATS Version 1.2 (ANSI/NISO Z39.96-2018)
                                   (DAL/BTU) v1.2     (2018-11-30)
    
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the both the NISO and ANSI membership 
     to be changed. This version of the NISO JATS was approved 
     by ANSI and NISO vote, so it supersedes all Committee Drafts
     and becoming NISO JATS 1.2 (ANSI/NISO Z39.96-2018).
  
 18. JATS "1.2d2" and "v1.2d2 20180401" became
     JATS "1.2" and "v1.2 20181130" 

    =============================================================
     JATS Version 1.2d2              (DAL/BTU) v1.2d2  (2018-04-01)

     JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
     BITS is under continuous maintenance, and is modified at the
     discretion of the working group.
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
 
 17. BITS "2.0" and "v2.0 20151225" remain unchanged
   
     JATS "1.2d1" and "v1.2d1 20171231" became
     JATS "1.2d2" and "v1.2d2 20180401". 

     No module names were changed.
 
    =============================================================
     JATS Version 1.2d1 (ANSI/NISO Z39.96-2015)
                                   (DAL/BTU) v1.2d1  (2017-12-31)
 
     NISO JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
  
 16. JATS "1.2d1" and "v1.2d1 20170631" became
     JATS "1.2d1" and "v1.2d1 20171231". 
     
    =============================================================
     JATS Version 1.2d1              (DAL/BTU) v1.2d1  (2017-06-31)

     NISO JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
 
 15. JATS became version "1.2d1" and "v1.2d1 20170631" 

     =============================================================
     JATS Version 1.1                (DAL/BTU) v1.1  (2015-12-15)

     JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
     JATS 1.1 was approved in late 2015, so the formal public 
     identifiers were changed in the modules and the catalogs.
     No model or attribute changes were made at this time. 
 
 14. JATS became version "1.1" and "v1.1 20151215"
     BITS remained version "2.0" but becomes "v2.0 20151225"

     =============================================================
     JATS Version 1.1d3             (DAL/BTU) v1.1d3  (2015-03-01)
   
     The changes in this release are in response to NISO 
     License and Indicators Recommended Practice.

     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the NISO membership to be changed. This
     Committee Draft 1.1d3 will be sent to the NISO voting
     membership, to become (if approved) NISO JATS 1.1.
     
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee.

 13. JATS became version "1.1d3" and "v1.1 20150301//EN"
 
     =============================================================
     JATS Version 1.1d2              (DAL/BTU) v1.1d2 (2014-09-30)
   
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the NISO membership to be changed. This
     Committee Draft 1.1d2 will be sent to the NISO voting
     membership, to become (if approved) NISO JATS 1.1.
     
     This catalog represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee.

  12. RUBY MODEL CHANGED TO MATCH HTML5
      - changed the Ruby content model to allow only a single
        Ruby annotation on a single Ruby base:
            (rb, rt+)
        The Ruby textual annotation may NOT take a pair of
        Ruby parentheses. This is a simplification of the
        HTML5 model:
           (rb, (rt | (rp, rt, rp)) )
        The simplification does not allow Ruby parenthesis.
        Archiving and BITS both use the full model above.

  11. JATS became version "1.1d2" and "v1.1d2 20140930//EN"
   
     =============================================================
     NISO JATS Version 1.1d1           (DAL/BTU) v1.1 (2013-11-15)
   
     ANSI/NISO Z39.96-2012 (pre-release for V1.0 BITS; Version 1.1d1) 

     NISO JATS Standing Committee met and answered the requests and
     suggestions from the NISO request forms.

     Details concerning ANSI/NISO Z39.96-2012 JATS-based DTDs,  
     XSDs, RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.3d2/

 10. RUBY ANNOTATIONS - Added new elements for:
      - <ruby> The Ruby wrapper element, which contains the base
          text and the annotation.
      - <rb> Ruby Base text, acts in the same manner as face markup,
        surrounding some text in the narrative.
      - <rt> Ruby Text, the annotation that modifies a piece of
         the narrative text, frequently typeset above the text.

  9. GLOBAL ATTRIBUTES - Added the new parameter entity 
         %jats-common-atts;
     to every element in this module. This PE adds (for now) the
     @id attribute and the @xml:base attribute to every element,
     whether metadata or narrative.
     Since the @id in this parameter entity is optional, a second
     parameter entity jats-common-atts-id-required was also added.
     The two are kept in sync with the jats-base-atts parameter 
     entity.
     This added a new attribute list to:
      - break 
      - hr      
      - rp               

  8. ADDED NEW ELEMENT <fixed-case> 
     - defined as meaning that the case of the content is not to
       change, even if the content around it is styled to all
       upper case or all lower. For example "PhD".

  7. ADDED NEW ATTRIBUTE - @toggle added to:
     - bold, italic (default="yes"), monospace, overline, 
       roman (default="no"), sans-serif, sc, strike, and underline.
   
     =============================================================
     NISO JATS Version 1.0             (DAL/BTU) v1.0 (2012-xx-xx)
   
     ANSI/NISO Z39.96-2012 (Version 1.0) 

     Details concerning ANSI/NISO Z39.96-2012 JATS-based DTDs,  
     XSDs, RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.3d2/
  
  6. Updated the DTD-version attribute to "1.0" and the formal
     public identifier to the date: "v1.0 20120330//EN".

     =============================================================
     Version 0.4                       (DAL/BTU) v0.4 (2011-01-31)
   
     This Tag Set is in the process of becoming a NISO standard.
     The version numbers are starting over from 0.4", as a Trial
     Use Draft, to be made into "Version 1.0" when the Tag Suite 
     becomes a NISO standard. Thus, the version number that would
     have been "NLM Version 3.1 Draft" has become NISO JATS
     "Version 0.4".

     Details on NISO Trial Use Draft Version 0.4 are available at
           http://jats.nlm.nih.gov/JATS-0.4.
   
  5. Updated the DTD-version attribute to "0.4" 
   
  4. Updated the public identifier to "v0.4 20110131", 
     modified the formal public identifier to include "JATS (Z39.96)",
     and the filename as delivered to include "JATS" and the
     new version number "0".
           http://jats.nlm.nih.gov/0.4.

  3. @SPECIAL-USE - Added the attribute @special-use to the
     all 14 formatting (emphasis) elements. This includes all the
     ones listed in 2. below as well as:
       - sub             sub-atts  
       - sup             sup-atts
       - overline        overline-atts
       - overline-start  overline-start-atts
       - overline-end    overline-end-atts
       - underline       underline-atts
       - underline-start underline-start-atts
       - underline-end   underline-end-atts

  2. PEs FOR ATTLISTS - Added parameter entity redefinitions for the
     attribute lists of the following elements: (no attributes or
     values were changed)
       - bold            bold-atts       NEW PE
       - italic          italic-atts     NEW PE
       - monospace       monospace-atts  NEW PE
       - roman           roman-atts      NEW PE
       - sans-serif      sans-serif-atts NEW PE
       - sc              sc-atts         NEW PE
       - strike          strike-atts     NEW PE

     =============================================================
     Version 3.0                       (DAL/BTU) v3.0 (2007-10-31)

     Version 3.0 is the first non-backward-compatible release.
     In addition to the usual incremental changes, some
     elements and attributes have been renamed and/or remodeled
     to better meet user needs and to increase tag set consistency.
     All module change histories are available through the Tag Suite
     web site at http://dtd.nlm.nih.gov.

     Details on version 3.0 are available at
           http://dtd.nlm.nih.gov/3.0.

  1. Updated public identifier to "v3.0 20071031//EN"              -->

<!-- ============================================================= -->
<!-- Details concerning 
     ANSI/NISO Z39.96-2015 JATS-based DTDs, XSDs, 
     RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.1/                            -->

<!-- ============================================================= -->
<!--                    PARAMETER ENTITIES FOR ATTRIBUTE LISTS     -->
<!-- ============================================================= -->


<!--                    BOLD ATTRIBUTES                            -->
<!--                    Attributes for the <bold> element          -->
<!ENTITY % bold-atts
            "%jats-common-atts;                                       
             toggle     (yes | no)                        #IMPLIED
             specific-use
                        CDATA                             #IMPLIED"  >


<!--                    BREAK ATTRIBUTES                           -->
<!--                    Attributes for the <break> element         -->
<!ENTITY % break-atts
            "%jats-common-atts;"                                     >


<!--                    FIXED CASE ATTRIBUTES                      -->
<!--                    Attributes for the <fixed-case> element    -->
<!ENTITY % fixed-case-atts
            "%jats-common-atts;                                       
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED"  >


<!--                    HORIZONTAL RULE ATTRIBUTES                 -->
<!--                    Attributes for the <hr> element            -->
<!ENTITY % hr-atts
            "%jats-common-atts;"                                     >


<!--                    ITALIC ATTRIBUTES                          -->
<!--                    Attributes for the <italic> element        -->
<!ENTITY % italic-atts
            "%jats-common-atts;                                       
             toggle     (yes | no)                        'yes'
             specific-use
                        CDATA                             #IMPLIED"  >


<!--                    MONOSPACE ATTRIBUTES                       -->
<!--                    Attributes for the <bold> element          -->
<!ENTITY % monospace-atts
            "%jats-common-atts;                                       
             toggle     (yes | no)                        #IMPLIED
             specific-use
                        CDATA                             #IMPLIED"  >


<!--                    OVERLINE START ATTRIBUTES                  -->
<!--                    Attributes for the <overline> element      -->
<!ENTITY % overline-atts
            "%jats-common-atts;                                       
             toggle     (yes | no)                        #IMPLIED
             specific-use
                        CDATA                             #IMPLIED"  >


<!--                    OVERLINE START ATTRIBUTES                  -->
<!--                    Attributes for the <overline-start> element-->
<!ENTITY % overline-start-atts
            "%jats-common-atts-id-required;                                       
             specific-use
                        CDATA                             #IMPLIED"  >


<!--                    OVERLINE END ATTRIBUTES                    -->
<!--                    Attributes for the <overline-end> element  -->
<!ENTITY % overline-end-atts
            "%jats-common-atts;                                       
             rid        IDREF                             #REQUIRED
             specific-use
                        CDATA                             #IMPLIED"  >


<!--                    ROMAN ATTRIBUTES                           -->
<!--                    Attributes for the <roman> element         -->
<!ENTITY % roman-atts
            "%jats-common-atts;                                       
             toggle     (yes | no)                        'no'
             specific-use
                        CDATA                             #IMPLIED"  >


<!--                    RUBY BASE ATTRIBUTES                       -->
<!--                    Attributes for the <rb> element            -->
<!ENTITY % rb-atts
            "%jats-common-atts;                                       
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    RUBY PARENTHESIS ATTRIBUTES                -->
<!--                    Attributes for the <rp> element            -->
<!ENTITY % rp-atts
            "%jats-common-atts;"                                     >


<!--                    RUBY TEXTUAL ANNOTATION ATTRIBUTES         -->
<!--                    Attributes for the <rt> element            -->
<!ENTITY % rt-atts
            "%jats-common-atts;                                       
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    RUBY ATTRIBUTES                            -->
<!--                    Attributes for the <ruby> element          -->
<!ENTITY % ruby-atts
            "%jats-common-atts;                                       
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED"  >


<!--                    SANS SERIF ATTRIBUTES                      -->
<!--                    Attributes for the <sans-serif> element    -->
<!ENTITY % sans-serif-atts
            "%jats-common-atts;                                       
             toggle     (yes | no)                        #IMPLIED
             specific-use
                        CDATA                             #IMPLIED"  >


<!--                    SMALL CAPS ATTRIBUTES                      -->
<!--                    Attributes for the <sc> element            -->
<!ENTITY % sc-atts
            "%jats-common-atts;                                       
             toggle     (yes | no)                        #IMPLIED
             specific-use
                        CDATA                             #IMPLIED"  >


<!--                    STRIKE THROUGH ATTRIBUTES                  -->
<!--                    Attributes for the <strike> element        -->
<!ENTITY % strike-atts
            "%jats-common-atts;                                       
             toggle     (yes | no)                        #IMPLIED
             specific-use
                        CDATA                             #IMPLIED"  >


<!--                    SUBSCRIPT ATTRIBUTES                       -->
<!--                    Attributes for the subscript <sub> element -->
<!ENTITY % sub-atts
            "%jats-common-atts;                                       
             arrange    (stack | stagger)                 #IMPLIED
             specific-use
                        CDATA                             #IMPLIED"  >


<!--                    SUPERSCRIPT ATTRIBUTES                     -->
<!--                    Attributes for the superscript <sup>
                        element                                    -->
<!ENTITY % sup-atts
            "%jats-common-atts;                                       
             arrange    (stack | stagger)                 #IMPLIED
             specific-use
                        CDATA                             #IMPLIED"  >


<!--                    UNDERLINE ATTRIBUTES                       -->
<!--                    Attributes for the <underline> element     -->
<!ENTITY % underline-atts
            "%jats-common-atts;                                       
             toggle     (yes | no)                        #IMPLIED
             underline-style
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED"  >


<!--                    UNDERLINE START ATTRIBUTES                 -->
<!--                    Attributes for the <underline-start>
                        element                                    -->
<!ENTITY % underline-start-atts
            "%jats-common-atts-id-required;                                       
             specific-use
                        CDATA                             #IMPLIED"  >


<!--                    UNDERLINE END ATTRIBUTES                   -->
<!--                    Attributes for the <underline-end> element -->
<!ENTITY % underline-end-atts
            "%jats-common-atts;                                       
             rid        IDREF                             #REQUIRED
             specific-use
                        CDATA                             #IMPLIED"  >


<!-- ============================================================= -->
<!--                    APPEARANCE CLASS ELEMENTS                  -->
<!-- ============================================================= -->


<!--                    HORIZONTAL RULE                            -->
<!--                    Defined to allow the user to specify an
                        explicit (non machine-generated) rule.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=hr
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=hr
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=hr
                                                                   -->
<!ELEMENT  hr           EMPTY                                        >
<!ATTLIST  hr 
             %hr-atts;                                               >


<!-- ============================================================= -->
<!--                    BREAK CLASS ELEMENTS                       -->
<!-- ============================================================= -->


<!--                    LINE BREAK                                 -->
<!--                    Defined to allow the user to specify an
                        explicit (non machine-generated) line break.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=break
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=break
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=break
                                                                   -->
<!ELEMENT  break        EMPTY                                        >
<!ATTLIST  break
             %break-atts;                                            >


<!-- ============================================================= -->
<!--                    EMPHASIS/RENDITION CLASS ELEMENTS          -->
<!-- ============================================================= -->


<!--                    BOLD                                       -->
<!--                    Used to mark text that should appear in bold
                        face type for print or display
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=bold
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=bold
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=bold
                                                                   -->
<!ELEMENT  bold         (#PCDATA %emphasized-text;)*                 >
<!ATTLIST  bold
             %bold-atts;                                             >


<!--                    FIXED CASE                                 -->
<!--                    Used to mark text in which the case of the 
                        content should not be changed, even if the 
                        content around it is styled to all
                        upper case or all lower. For example "PhD".
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=fixed-case
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=fixed-case
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=fixed-case
                                                                   -->
<!ELEMENT  fixed-case   (#PCDATA %emphasized-text;)*                 >
<!ATTLIST  fixed-case
             %fixed-case-atts;                                       >


<!--                    ITALIC                                     -->
<!--                    Used to mark text that should appear in
                        italic type on output.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=italic
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=italic
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=italic
                                                                   -->
<!ELEMENT  italic       (#PCDATA %emphasized-text;)*                 >
<!ATTLIST  italic
             %italic-atts;                                           >


<!--                    MONOSPACE TEXT (TYPEWRITER TEXT)           -->
<!--                    Used to mark text that should appear in
                        a non-proportional font, such as courier
                        for display or print.  This is common for
                        computer code examples, man-machine
                        dialogues, etc.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=monospace
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=monospace
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=monospace
                                                                   -->
<!ELEMENT  monospace    (#PCDATA %emphasized-text;)*                 >
<!ATTLIST  monospace
             %monospace-atts;                                        >


<!--                    ROMAN                                      -->
<!--                    Used to mark text that should remain in
                        roman script no matter what style the
                        surrounding text takes on.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=roman
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=roman
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=roman
                                                                   -->
<!ELEMENT  roman        (#PCDATA %emphasized-text;)*                 >
<!ATTLIST  roman
             %roman-atts;                                            >



<!--                    SANS SERIF                                 -->
<!--                    Used to mark text that should appear in a
                        special sans-serif font, typically used in
                        math or chemistry
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=sans-serif
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=sans-serif
                                                                   -->
<!ELEMENT  sans-serif   (#PCDATA %emphasized-text;)*                 >
<!ATTLIST  sans-serif
             %sans-serif-atts;                                       >


<!--                    SMALL CAPS                                 -->
<!--                    Used to mark text that should appear in a
                        font which creates smaller capital letters
                        on output.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=sc
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=sc
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=sc
                                                                   -->
<!ELEMENT  sc           (#PCDATA %emphasized-text;)*                 >
<!ATTLIST  sc
             %sc-atts;                                               >


<!--                    OVERLINE                                   -->
<!--                    Used to mark text that should appear with a
                        horizontal line above each character in
                        display or print. There is no PE for
                        override because this was added to handle
                        a specific publisher tag set and should not
                        grow beyond what they need.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=overline
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=overline
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=overline
                                                                   -->
<!ELEMENT  overline     (#PCDATA %emphasized-text;)*                 >
<!ATTLIST  overline
             %overline-atts;                                         >


<!--                    STRIKE THROUGH                             -->
<!--                    Used to mark text that should appear with
                        a line through it so as to appear "struck out"
                        on display or print
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=strike
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=strike
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=strike
                                                                   -->
<!ELEMENT  strike       (#PCDATA %emphasized-text;)*                 >
<!ATTLIST  strike
             %strike-atts;                                           >


<!--                    SUBSCRIPT                                  -->
<!--                    A number or expression that is set lower
                        than the baseline and slightly smaller,
                        to act as an inferior or subscript
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=sub
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=sub
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=sub
                                                                   -->
<!ELEMENT  sub          (#PCDATA %emphasized-text;)*                 >
<!ATTLIST  sub
             %sub-atts;                                              >


<!--                    SUPERSCRIPT                                -->
<!--                    A number or expression that is set higher
                        than the baseline and slightly smaller,
                        to act as a superior or superscript
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=sup
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=sup
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=sup
                                                                   -->
<!ELEMENT  sup          (#PCDATA %emphasized-text;)*                 >
<!ATTLIST  sup
             %sup-atts;                                              >


<!--                    UNDERLINE                                  -->
<!--                    Used to mark text that should appear with a
                        horizontal line below each character in
                        display or print.
                        Details at:
                         http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=underline
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=underline
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=underline
                                                                   -->
<!ELEMENT  underline    (#PCDATA %emphasized-text;)*                 >
<!ATTLIST  underline
             %underline-atts;                                        >


<!-- ============================================================= -->
<!--                    ADVANCED UNDERLINE AND OVERLINE ELEMENTS
                        (From the Elsevier DTD, used mostly within
                        mathematics, when ordinary MathML underline
                        and overline functions are inadequate. The
                        elements act as milestone tags marking the
                        start and end of over or underlining that
                        may extend across element boundaries.)     -->
<!-- ============================================================= -->


<!--                    OVERLINE START                             -->
<!--                    The start of a milestone-created overline, the
                        end of ornament will be marked by an
                        Overline End element.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=overline-start
                                                                   -->
<!ELEMENT  overline-start
                        EMPTY                                        >
<!ATTLIST  overline-start
             %overline-start-atts;                                   >


<!--                    OVERLINE END                               -->
<!--                    The end of a milestone-created overline, the
                        start of ornament will be marked by an
                        Overline Start element.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=overline-end
                                                                   -->
<!ELEMENT  overline-end EMPTY                                        >
<!ATTLIST  overline-end
             %overline-end-atts;                                     >


<!--                    UNDERLINE START                            -->
<!--                    The start of a milestone-created underline,
                        the end of ornament will be marked by an
                        Underline End element
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=underline-start
                                                                   -->
<!ELEMENT  underline-start
                        EMPTY                                        >
<!ATTLIST  underline-start
             %underline-start-atts;                                  >


<!--                    UNDERLINE END                              -->
<!--                    The end of a milestone-created underline, the
                        start of ornament will be marked by an
                        Underline Start element.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=underline-end
                                                                   -->
<!ELEMENT  underline-end
                        EMPTY                                        >
<!ATTLIST  underline-end
             %underline-end-atts;                                    >


<!-- ============================================================= -->
<!--                    RUBY MARKUP                                -->
<!-- ============================================================= -->


<!--                    RUBY WRAPPER Model                         -->
<!--                    Content model for the <ruby> element       -->
<!ENTITY % ruby-model   "(rb, rt)"                                   >


<!--                    RUBY WRAPPER                               -->
<!--                    A Ruby textual annotation is an annotation,
                        typically short, applied to a letter, word,
                        phrase, or name that appears in the narrative
                        text. Ruby annotations can indicate,
                        for example, pronunciation advice, notes for 
                        translation, semantic annotations, et al.
                          The <ruby> element is an inline wrapper  
                        element that contains some of the document 
                        narrative text (inside a Ruby Base <rb> 
                        element) and one or more Ruby textual 
                        annotations (inside <rt> elements) that are
                        associated with the base text.
                          In display or print, the characters of the
                        Ruby annotation are frequently placed above 
                        the characters they modify, in parenthesis 
                        after the characters they modify, or to the
                        right of vertically set text.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=ruby
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=ruby
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=ruby
                                                                   -->
<!ELEMENT  ruby         %ruby-model;                                 >
<!ATTLIST  ruby
             %ruby-atts;                                             >


<!--                    RUBY BASE ELEMENTS                         -->
<!--                    Those elements that may mix with the data
                        characters inside a Ruby Base <rb>.        -->
<!ENTITY % rb-elements  "| %face-markup.class;"                      >


<!--                    RUBY BASE                                  -->
<!--                    The <rb> element is one half of a Ruby 
                        Annotation <ruby>, and it contains the 
                        narrative text to which a Ruby textual 
                        annotation <rt> should be applied.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=rb
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=rb
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=rb
                                                                   -->
<!ELEMENT  rb           (#PCDATA %rb-elements;)*                     >
<!ATTLIST  rb
             %rb-atts;                                               >


<!--                    RUBY TEXTUAL ANNOTATION ELEMENTS           -->
<!--                    Those elements that may mix with the data
                        characters inside a Ruby Textual Annotation
                        <rt>.                                      -->
<!ENTITY % rt-elements  ""                                           >


<!--                    RUBY TEXTUAL ANNOTATION                    -->
<!--                    The <rt> element is one half of a Ruby 
                        Annotation <ruby>, and it contains the 
                        Ruby textual annotation that is applied to
                        the Ruby Base text <rb>.
                        Details at:
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=rt
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=rt
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=rt
                                                                   -->
<!ELEMENT  rt           (#PCDATA %rt-elements;)*                     >
<!ATTLIST  rt
             %rt-atts;                                               >



<!--                    RUBY PARENTHESIS                           -->
<!--                    The <rp> element is an optional element used
                        to provide spacing and a parenthesis to 
                        surround Ruby Text <rt>, for older systems 
                        that do not handle Ruby properly.
                        Remarks. This optional element never holds
                        any text except a space, an open 
                        parenthesis, or a close parenthesis:
                          <ruby><rb>text</rb>
                          <rp> (</rp><rt>annotation</rt><rp>) </rp>
                          </ruby>
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=rp
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=rp
                        http://jats.nlm.nih.gov/articleauthoring/tag-library/1.3/index.html?elem=rp
                                                                   -->
<!ELEMENT  rp           (#PCDATA)                                    >
<!ATTLIST  rp
             %rp-atts;                                               >


<!-- ================== End Format Class Module ================== -->

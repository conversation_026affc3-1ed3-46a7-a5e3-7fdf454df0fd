# ebm_integration_guide.py
"""
EBM内容优化器集成指南
展示如何将新的EBM内容优化器集成到现有系统中
针对小模型2k上下文限制和真实文献数据的优化方案
"""

import logging
from typing import Dict, List, Any, Optional
from ebm_content_optimizer import EBMContentOptimizer, EBMSection

logger = logging.getLogger(__name__)


class EnhancedEBMGenerator:
    """
    增强的EBM生成器
    集成了内容优化器，专门处理小模型上下文限制问题
    """
    
    def __init__(self, llm_manager, update_callback=None):
        self.llm = llm_manager
        self.update_callback = update_callback
        self.content_optimizer = EBMContentOptimizer(llm_manager)
        
    def generate_optimized_ebm_report(self, topic: str, studies: List[Dict[str, Any]], 
                                    provider: str, model: str, is_chinese: bool = True) -> Dict[str, Any]:
        """
        生成优化的EBM报告
        使用分块处理策略适应小模型上下文限制
        确保所有内容基于真实文献数据
        """
        try:
            self._update_progress("开始生成优化的EBM报告...")
            
            # 1. 使用内容优化器生成完整的报告结构
            self._update_progress("正在优化报告结构...")
            optimized_report = self.content_optimizer.optimize_ebm_report_structure(
                topic, studies, provider, model, is_chinese
            )
            
            if not optimized_report:
                raise ValueError("报告结构优化失败")
            
            # 2. 提取优化后的各个章节
            sections = optimized_report.get('sections', {})
            statistics = optimized_report.get('statistics', {})
            quality_metrics = optimized_report.get('quality_metrics', {})
            compliance_check = optimized_report.get('compliance_check', {})
            
            # 3. 组装完整的报告内容
            self._update_progress("正在组装报告内容...")
            full_report = self._assemble_full_report(sections, statistics, is_chinese)
            
            # 4. 生成报告摘要和质量评估
            report_summary = self._generate_report_summary(
                sections, statistics, quality_metrics, compliance_check, is_chinese
            )
            
            self._update_progress("EBM报告生成完成！")
            
            return {
                'success': True,
                'report_content': full_report,
                'sections': sections,
                'statistics': statistics,
                'quality_metrics': quality_metrics,
                'compliance_check': compliance_check,
                'summary': report_summary,
                'word_count': quality_metrics.get('word_count_total', 0),
                'quality_score': quality_metrics.get('overall_quality', 0.0)
            }
            
        except Exception as e:
            logger.error(f"生成优化EBM报告失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'report_content': '',
                'sections': {},
                'statistics': {},
                'quality_metrics': {},
                'compliance_check': {},
                'summary': ''
            }
    
    def _assemble_full_report(self, sections: Dict[str, EBMSection], 
                            statistics: Dict[str, Any], is_chinese: bool) -> str:
        """组装完整的报告内容"""
        try:
            if is_chinese:
                report_parts = [
                    f"# {sections.get('title', EBMSection('title', '标题缺失', 0, 0.0, False)).content}",
                    "",
                    "## 摘要",
                    sections.get('abstract', EBMSection('abstract', '摘要缺失', 0, 0.0, False)).content,
                    "",
                    "## 引言",
                    sections.get('introduction', EBMSection('introduction', '引言缺失', 0, 0.0, False)).content,
                    "",
                    "## 方法",
                    sections.get('methods', EBMSection('methods', '方法缺失', 0, 0.0, False)).content,
                    "",
                    "## 结果",
                    sections.get('results', EBMSection('results', '结果缺失', 0, 0.0, False)).content,
                    "",
                    "## 讨论",
                    sections.get('discussion', EBMSection('discussion', '讨论缺失', 0, 0.0, False)).content,
                    "",
                    "## 结论",
                    sections.get('conclusion', EBMSection('conclusion', '结论缺失', 0, 0.0, False)).content,
                    "",
                    "## 补充材料",
                    sections.get('supplementary', EBMSection('supplementary', '补充材料缺失', 0, 0.0, False)).content,
                    "",
                    "---",
                    "",
                    "## 报告统计信息",
                    f"- 纳入研究：{statistics.get('total_studies', 0)}项",
                    f"- 总参与者：{statistics.get('total_participants', 0)}人",
                    f"- 主要研究设计：{', '.join(list(statistics.get('study_designs', {}).keys())[:3])}",
                    f"- 发表年份范围：{min(statistics.get('publication_years', {}).keys()) if statistics.get('publication_years') else 'N/A'}-{max(statistics.get('publication_years', {}).keys()) if statistics.get('publication_years') else 'N/A'}",
                    ""
                ]
            else:
                report_parts = [
                    f"# {sections.get('title', EBMSection('title', 'Title Missing', 0, 0.0, False)).content}",
                    "",
                    "## Abstract",
                    sections.get('abstract', EBMSection('abstract', 'Abstract Missing', 0, 0.0, False)).content,
                    "",
                    "## Introduction",
                    sections.get('introduction', EBMSection('introduction', 'Introduction Missing', 0, 0.0, False)).content,
                    "",
                    "## Methods",
                    sections.get('methods', EBMSection('methods', 'Methods Missing', 0, 0.0, False)).content,
                    "",
                    "## Results",
                    sections.get('results', EBMSection('results', 'Results Missing', 0, 0.0, False)).content,
                    "",
                    "## Discussion",
                    sections.get('discussion', EBMSection('discussion', 'Discussion Missing', 0, 0.0, False)).content,
                    "",
                    "## Conclusions",
                    sections.get('conclusion', EBMSection('conclusion', 'Conclusions Missing', 0, 0.0, False)).content,
                    "",
                    "## Supplementary Materials",
                    sections.get('supplementary', EBMSection('supplementary', 'Supplementary Missing', 0, 0.0, False)).content,
                    "",
                    "---",
                    "",
                    "## Report Statistics",
                    f"- Included Studies: {statistics.get('total_studies', 0)} studies",
                    f"- Total Participants: {statistics.get('total_participants', 0)} participants",
                    f"- Main Study Designs: {', '.join(list(statistics.get('study_designs', {}).keys())[:3])}",
                    f"- Publication Years: {min(statistics.get('publication_years', {}).keys()) if statistics.get('publication_years') else 'N/A'}-{max(statistics.get('publication_years', {}).keys()) if statistics.get('publication_years') else 'N/A'}",
                    ""
                ]
            
            return "\n".join(report_parts)
            
        except Exception as e:
            logger.error(f"组装报告内容失败: {e}")
            return "报告组装失败" if is_chinese else "Report assembly failed"
    
    def _generate_report_summary(self, sections: Dict[str, EBMSection], 
                               statistics: Dict[str, Any], quality_metrics: Dict[str, float],
                               compliance_check: Dict[str, bool], is_chinese: bool) -> str:
        """生成报告摘要和质量评估"""
        try:
            if is_chinese:
                summary_parts = [
                    "## 报告质量摘要",
                    "",
                    f"**整体质量评分：** {quality_metrics.get('overall_quality', 0.0):.2f}/1.00",
                    f"**总字数：** {quality_metrics.get('word_count_total', 0):,} 字",
                    f"**基于真实数据比例：** {quality_metrics.get('real_data_percentage', 0.0):.1%}",
                    f"**章节完整度：** {quality_metrics.get('section_completeness', 0.0):.1%}",
                    "",
                    "### PRISMA 2020合规性检查",
                    f"- 标题充分性：{'✅' if compliance_check.get('title_adequate', False) else '❌'}",
                    f"- 结构化摘要：{'✅' if compliance_check.get('abstract_structured', False) else '❌'}",
                    f"- 引言完整性：{'✅' if compliance_check.get('introduction_complete', False) else '❌'}",
                    f"- 方法详细性：{'✅' if compliance_check.get('methods_detailed', False) else '❌'}",
                    f"- 结果全面性：{'✅' if compliance_check.get('results_comprehensive', False) else '❌'}",
                    f"- 讨论平衡性：{'✅' if compliance_check.get('discussion_balanced', False) else '❌'}",
                    f"- 结论基于证据：{'✅' if compliance_check.get('conclusion_evidence_based', False) else '❌'}",
                    f"- 补充材料：{'✅' if compliance_check.get('supplementary_provided', False) else '❌'}",
                    "",
                    "### 数据统计",
                    f"- 纳入研究：{statistics.get('total_studies', 0)}项",
                    f"- 总参与者：{statistics.get('total_participants', 0):,}人",
                    f"- 研究设计分布：{dict(list(statistics.get('study_designs', {}).items())[:3])}",
                    f"- 样本量范围：{statistics.get('sample_size_range', {}).get('min', 0)}-{statistics.get('sample_size_range', {}).get('max', 0)}人",
                    ""
                ]
            else:
                summary_parts = [
                    "## Report Quality Summary",
                    "",
                    f"**Overall Quality Score:** {quality_metrics.get('overall_quality', 0.0):.2f}/1.00",
                    f"**Total Word Count:** {quality_metrics.get('word_count_total', 0):,} words",
                    f"**Real Data Percentage:** {quality_metrics.get('real_data_percentage', 0.0):.1%}",
                    f"**Section Completeness:** {quality_metrics.get('section_completeness', 0.0):.1%}",
                    "",
                    "### PRISMA 2020 Compliance Check",
                    f"- Title Adequacy: {'✅' if compliance_check.get('title_adequate', False) else '❌'}",
                    f"- Structured Abstract: {'✅' if compliance_check.get('abstract_structured', False) else '❌'}",
                    f"- Introduction Complete: {'✅' if compliance_check.get('introduction_complete', False) else '❌'}",
                    f"- Methods Detailed: {'✅' if compliance_check.get('methods_detailed', False) else '❌'}",
                    f"- Results Comprehensive: {'✅' if compliance_check.get('results_comprehensive', False) else '❌'}",
                    f"- Discussion Balanced: {'✅' if compliance_check.get('discussion_balanced', False) else '❌'}",
                    f"- Evidence-based Conclusion: {'✅' if compliance_check.get('conclusion_evidence_based', False) else '❌'}",
                    f"- Supplementary Provided: {'✅' if compliance_check.get('supplementary_provided', False) else '❌'}",
                    "",
                    "### Data Statistics",
                    f"- Included Studies: {statistics.get('total_studies', 0)} studies",
                    f"- Total Participants: {statistics.get('total_participants', 0):,} participants",
                    f"- Study Design Distribution: {dict(list(statistics.get('study_designs', {}).items())[:3])}",
                    f"- Sample Size Range: {statistics.get('sample_size_range', {}).get('min', 0)}-{statistics.get('sample_size_range', {}).get('max', 0)}",
                    ""
                ]
            
            return "\n".join(summary_parts)
            
        except Exception as e:
            logger.error(f"生成报告摘要失败: {e}")
            return "摘要生成失败" if is_chinese else "Summary generation failed"
    
    def _update_progress(self, message: str, is_error: bool = False):
        """更新进度信息"""
        if self.update_callback:
            self.update_callback(message, is_error)
        else:
            if is_error:
                logger.error(message)
            else:
                logger.info(message)


# 使用示例
def example_usage():
    """
    使用示例：如何使用增强的EBM生成器
    """
    # 假设已有的LLM管理器和研究数据
    # llm_manager = LLMManager()
    # studies = [...]  # 真实的研究数据
    
    # 创建增强的EBM生成器
    # enhanced_generator = EnhancedEBMGenerator(llm_manager)
    
    # 生成优化的EBM报告
    # result = enhanced_generator.generate_optimized_ebm_report(
    #     topic="阿司匹林预防心血管疾病",
    #     studies=studies,
    #     provider="openai",
    #     model="gpt-3.5-turbo",
    #     is_chinese=True
    # )
    
    # 检查结果
    # if result['success']:
    #     print(f"报告生成成功！质量评分：{result['quality_score']:.2f}")
    #     print(f"总字数：{result['word_count']:,}")
    #     print("报告内容：")
    #     print(result['report_content'])
    # else:
    #     print(f"报告生成失败：{result['error']}")
    
    pass


if __name__ == "__main__":
    example_usage()

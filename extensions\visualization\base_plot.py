"""
基础绘图类
提供所有可视化图表共用的基础功能
"""
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional
import plotly.graph_objects as go
import plotly.io as pio

class BasePlot(ABC):
    """
    基础绘图抽象类，所有具体图表类都应继承此类
    """
    def __init__(self, data: Dict[str, Any], config: Optional[Dict] = None):
        """
        初始化基础绘图
        
        Args:
            data: 图表数据
            config: 配置选项，可选
        """
        self.data = data
        self.config = config or self.default_config()
        self.fig = self._create_figure()
        self._apply_theme()
        
    @abstractmethod
    def _create_figure(self) -> go.Figure:
        """
        创建图表的抽象方法，子类必须实现
        
        Returns:
            go.Figure: Plotly图表对象
        """
        pass
        
    def _apply_theme(self) -> None:
        """应用主题和样式"""
        # 更新布局
        self.fig.update_layout(
            template=self.config['template'],
            font=dict(
                family=self.config['font_family'],
                size=self.config['font_size'],
                color=self.config['font_color']
            ),
            margin=dict(l=50, r=50, t=50, b=50),
            paper_bgcolor='rgba(0,0,0,0)',
            plot_bgcolor='rgba(0,0,0,0)',
        )
    
    def to_html(self, **kwargs) -> str:
        """
        将图表转换为HTML字符串
        
        Args:
            **kwargs: 传递给plotly的to_html方法的参数
            
        Returns:
            str: HTML字符串
        """
        default_kwargs = {
            'full_html': False,
            'include_plotlyjs': 'cdn',
            'config': {'responsive': True}
        }
        default_kwargs.update(kwargs)
        return self.fig.to_html(**default_kwargs)
    
    def to_image(self, format: str = 'png', **kwargs) -> bytes:
        """
        将图表导出为图片
        
        Args:
            format: 图片格式，支持 'png', 'jpg', 'jpeg', 'webp', 'svg', 'pdf', 'eps'
            **kwargs: 传递给plotly的to_image方法的参数
            
        Returns:
            bytes: 图片二进制数据
        """
        default_kwargs = {
            'width': 1200,
            'height': 800,
            'scale': 2
        }
        default_kwargs.update(kwargs)
        return self.fig.to_image(format=format, **default_kwargs)
    
    def show(self, **kwargs):
        """
        显示图表（在Jupyter Notebook中）
        """
        return self.fig.show(**kwargs)
    
    @classmethod
    def default_config(cls) -> Dict:
        """
        获取默认配置
        
        Returns:
            Dict: 默认配置字典
        """
        return {
            'template': 'plotly_white',
            'font_family': 'Arial, SimHei, sans-serif',
            'font_size': 12,
            'font_color': '#333333',
            'color_scale': 'Plotly',
            'margin': dict(l=50, r=50, t=50, b=50)
        }

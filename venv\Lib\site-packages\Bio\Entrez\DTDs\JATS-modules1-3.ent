<!-- ============================================================= -->
<!--  MODULE:    JATS DTD Suite Module of Modules                  -->
<!--  VERSION:   ANSI/NISO JATS Version 1.3 (Z39.96-2021)          -->
<!--  DATE:      June 2021                                         -->
<!--                                                               -->
<!-- ============================================================= -->

<!-- ============================================================= -->
<!--                    PUBLIC DOCUMENT TYPE DEFINITION            -->
<!--                        TYPICAL INVOCATION                     -->
<!--
"-//NLM//DTD JATS (Z39.96) JATS DTD Suite Module of Modules v1.3 20210610//EN"
     Delivered as file "JATS-modules1-3.ent"                       -->
<!-- ============================================================= -->

<!-- ============================================================= -->
<!-- SYSTEM:     JATS DTD Suite                                    -->
<!--                                                               -->
<!-- PURPOSE:    For naming all the external modules (except       -->
<!--             this module itself and the customization modules) -->
<!--             that are part of the JATS DTD Suite. A specific   -->
<!--             DTD will select from these modules by referencing -->
<!--             the external Parameter Entities defined below that-->
<!--             name the modules of the suite. To include a set   -->
<!--             of elements (such as all the lists or the MathML  -->
<!--             elements), reference the external Parameter Entity-->
<!--             of the module that contains these declarations,   -->
<!--             then modify the classes or content models to use  -->
<!--             the new elements.                                 -->
<!--                                                               -->
<!-- CONTAINS:   Entity declarations and public names for all the  -->
<!--             JATS DTD Suite external modules. Note: The modules-->
<!--             are NOT referenced (called/invoked) in this       -->
<!--             module, they are merely DECLARED.  The DTD or     -->
<!--             a setup module (such as for the XHTML tables)     -->
<!--             will invoke the external parameter entity to      -->
<!--             call each module.                                 -->
<!--                                                               -->
<!-- TAG SET SPONSOR                                               -->
<!--             National Center for Biotechnology                 -->
<!--                Information (NCBI)                             -->
<!--             National Library of Medicine (NLM)                -->
<!--                                                               -->
<!-- CREATED FOR:                                                  -->
<!--             This module was created for the JATS DTD Suite.   -->
<!--             Digital archives and publishers may use the       -->
<!--             DTD as is for markup of journal literature or     -->
<!--             related material for archiving and transferring   -->
<!--             such material between archives or create a        -->
<!--             custom XML DTD from the Suite for                 -->
<!--             these purposes.                                   -->
<!--                                                               -->
<!--             This DTD is in the public domain. An organization -->
<!--             that wishes to create its own DTD from the suite  -->
<!--             may do so without permission from NLM.            -->
<!--                                                               -->
<!--             The suite has been set up to be extended using a  -->
<!--             new DTD file and a new DTD-specific customization -->
<!--             module to redefine the many Parameter Entities.   -->
<!--             Do not modify the suite directly or redistribute  -->
<!--             modified versions of the suite.                   -->
<!--                                                               -->
<!--             In the interest of maintaining consistency and    -->
<!--             clarity for potential users, NLM requests:        -->
<!--                                                               -->
<!--             1. If you create a DTD from the JATS DTD Suite    -->
<!--                and intend to stay compatible with the suite,  -->
<!--                then please include the following statement    -->
<!--                as a comment in all of your DTD modules:       -->
<!--                   "Created from, and fully compatible with,   -->
<!--                    the ANSI/NISO Z39.96 Journal Article Tag   -->
<!--                    Suite (JATS)."                             -->
<!--                                                               -->
<!--             2. If you alter one or more modules of the suite, -->
<!--                then please rename your version and all its    -->
<!--                modules to avoid any confusion with the        -->
<!--                original suite. Also, please include the       -->
<!--                following statement as a comment in all your   -->
<!--                DTD modules:                                   -->
<!--                   "Based in part on, but not fully compatible -->
<!--                    with, the ANSI/NISO Z39.96 Journal Article -->
<!--                    Tag Suite (JATS)."                         -->
<!--                                                               -->
<!-- ORIGINAL CREATION DATE:                                       -->
<!--             December 2002                                     -->
<!--                                                               -->
<!-- CREATED BY: Mulberry Technologies, Inc. for the NISO Z39.96   -->
<!--             Working Group. Mulberry Technologies was          -->
<!--             supported by National Center for Biotechnology    -->
<!--             Information (NCBI), a center of the US National   -->
<!--             Library of Medicine (NLM).                        -->
<!--                                                               -->
<!--             This module is part of the JATS DTD Suite. The    -->
<!--             Suite is a continuation of work done by NCBI,     -->
<!--             Mulberry Technologies, and Inera Inc. on the NLM  -->
<!--             Journal Archiving and Interchange DTD Suite, which-->
<!--             was originally released in December, 2002.        -->
<!--                                                               -->
<!--             NLM thanks the Harvard University Libraries, both -->
<!--             for proposing that a draft archiving NLM DTD for  -->
<!--             life sciences journals be extended to accommodate -->
<!--             journals in all disciplines and for sponsoring    -->
<!--             Inera Inc.'s collaboration with other DTD         -->
<!--             authors in completing NLM Version 1.0. The        -->
<!--             Andrew W. Mellon Foundation provided support for  -->
<!--             these important contributions.                    -->
<!--                                                               -->
<!--             Suggestions for refinements and enhancements to   -->
<!--             this DTD should be sent in email to:              -->
<!--                 <EMAIL>                         -->
<!-- ============================================================= -->


<!-- ============================================================= -->
<!--                    DTD VERSION/CHANGE HISTORY                 -->
<!-- ============================================================= -->
<!--
Version  Reason/Occasion                   (who) vx.x (yyyy-mm-dd)
   ==============================================================
     JATS Version 1.3 (ANSI/NISO Z39.96-2021)
                                  (DAL/BTU) v1.3 (2021-06-10)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by both the NISO and ANSI memberships 
     to be changed. This version of the NISO JATS was approved 
     by ANSI vote on June 10, 2021, so it supersedes all Committee 
     Drafts as well as the draft version JATS 1.3 2021 (the version
     submitted for vote), becoming NISO JATS v1.3 20210610 
     (ANSI/NISO Z39.96-2019).

     The file names and formal public identifiers have been 
     changed in the modules and the catalogs.

 18. JATS 'v1.3d2 20201130' becomes 'v1.3 20210610' 
     (final ANSI vote date)
     'ANSI/NISO Z39.96-2019' becomes 'ANSI/NISO Z39.96-2021'

   ==============================================================
     JATS Version 1.3d2 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.3d2 (2020-11-30)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by the NISO and ANSI memberships 
     to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     of the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting; it is for Standing Committee review and
     testing. The formal public identifiers have been changed 
     in the modules and the catalogs.
 
 17. JATS "v1.3d2 20201130" becomes 'v1.3 20210610' (second 
     committee draft following the final ANSI/NISO 1.2 vote)

   ==============================================================
     JATS Version 1.3d1 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.3.d1 (2019-08-31)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by the NISO and ANSI memberships 
     to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     of the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
     
 16. QUESTIONS AND ANSWERS - Added the BITS modules question and 
     answer modules, so quizzes can be constructed for articles.
     
 15. JATS "1.2" becomes "v1.3d1 20190831" (first committee
     draft following the final ANSI/NISO vote) 

   ==============================================================
     JATS Version 1.2 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.2     (2019-02-08)
    
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the both the NISO and ANSI membership 
     to be changed. This version of the NISO JATS was approved 
     by ANSI vote on Feb 08, 2019, so it supersedes all Committee 
     Drafts as well as the draft version JATS 1.2 2018 (the version
     submitted for vote), becoming NISO JATS v1.2 20190208 
     (ANSI/NISO Z39.96-2021).
  
 14. JATS "1.2" becomes "v1.2 20190208" (final ANSI vote date)
     "ANSI/NISO Z39.96-2015" becomes "ANSI/NISO Z39.96-2019"
     
   ==============================================================
     JATS Version 1.2 (ANSI/NISO Z39.96-2018)
     
                                   (DAL/BTU) v1.2     (2018-11-30)
    
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the both the NISO and ANSI membership 
     to be changed. This version of the NISO JATS was approved 
     by ANSI and NISO vote, so it supersedes all Committee Drafts
     and becoming NISO JATS 1.2 (ANSI/NISO Z39.96-2018).
  
 13. 
     JATS "1.2d2" and "v1.2d2 20180401" became
     JATS "1.2" and "v1.2 20181130" 

    =============================================================
     
     JATS Version 1.2d2              (DAL/BTU) v1.2d2  (2018-04-01)

     JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
     BITS is under continuous maintenance, and is modified at the
     discretion of the working group.
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.

 12. INLINE EMBEDDED INDEX - Added definition of BITS module that
     defines inline (embedded) index terms.
 
 11. BITS "2.0" and "v2.0 20151225" remain unchanged
      
   
     JATS "1.2d1" and "v1.2d1 20171231" became
     JATS "1.2d2" and "v1.2d2 20180401". 

     No module names were changed.
 
    =============================================================
     JATS Version 1.2d1 (ANSI/NISO Z39.96-2015)
                                   (DAL/BTU) v1.2d1  (2017-12-31)
 
     NISO JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
  
 10. JATS "1.2d1" and "v1.2d1 20170631" became
     JATS "1.2d1" and "v1.2d1 20171231". 

    =============================================================
     JATS Version 1.2d1              (DAL/BTU) v1.2d1  (2017-06-31)

     NISO JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
    
  9. JATS became version "1.2d1" and "v1.2d1 20170631" 

     =============================================================
     JATS Version 1.1                (DAL/BTU) v1.1  (2015-12-15)

     JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
     JATS 1.1 was approved in late 2015, so the formal public 
     identifiers were changed in the modules and the catalogs.
     No model or attribute changes were made at this time. 
 
  8. JATS became version "1.1" and "v1.1 20151215"
     BITS remained version "2.0" but becomes "v2.0 20151225"

     =============================================================
     JATS Version 1.1d3             (DAL/BTU) v1.1d3  (2015-03-01)
   
     The changes in this release are in response to NISO 
     License and Indicators Recommended Practice.

     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the NISO membership to be changed. This
     Committee Draft 1.1d3 will be sent to the NISO voting
     membership, to become (if approved) NISO JATS 1.1.
     
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee.

  7. JATS became version "1.1d3" and "v1.1d3 20150301"

     =============================================================
     JATS Version 1.1d2              (DAL/BTU) v1.1d2 (2014-09-30)
   
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the NISO membership to be changed. This
     Committee Draft 1.1d2 will be sent to the NISO voting
     membership, to become (if approved) NISO JATS 1.1.
     
     This catalog represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee.

  6. JATS became version "1.1d2" and "v1.1d2 20140930//EN"
   
     =============================================================
     NISO JATS Version 1.1d1           (DAL/BTU) v1.1 (2013-11-15)
   
     ANSI/NISO Z39.96-2012 (pre-release for V1.0 BITS; Version 1.1d1) 

     NISO JATS Standing Committee met and answered the requests and
     suggestions from the NISO request forms.

     Details concerning ANSI/NISO Z39.96-2012 JATS-based 
     DTDs, XSDs, RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.3d2/

     =============================================================
     NISO JATS Version 1.0             (DAL/BTU) v1.0 (2012-xx-xx)
   
     ANSI/NISO Z39.96-2012 (Version 1.0) 

     Details concerning ANSI/NISO Z39.96-2012 JATS-based DTDs,  
     XSDs, RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.3d2/
   
  5. NLMCITATION - Added module for deprecated element <nlm-citation>
     because the element is called in a default class 
     (citation.class) that, for reasons of backwards compatibility,
     will not be changed at this time.
   
  4. Updated the DTD-version attribute to "1.0" and the formal
     public identifier to the date: "v1.0 20120330//EN".

     =============================================================
     Version 0.4                       (DAL/BTU) v0.4 (2011-01-31)
   
     This Tag Set is in the process of becoming a NISO standard.
     The version numbers are starting over from 0.4", as a Trial
     Use Draft, to be made into "Version 1.0" when the Tag Suite 
     becomes a NISO standard. Thus, the version number that would
     have been "NLM Version 3.1 Draft" has become NISO JATS
     "Version 0.4".

     Details on NISO Trial Use Draft Version 0.4 are available at
           http://jats.nlm.nih.gov/JATS-0.4.
   
  3. Updated the DTD-version attribute to "0.4" 
   
  2. Updated the public identifier to "v0.4 20110131", 
     modified the formal public identifier to include "JATS (Z39.96)",
     and the filename as delivered to include "JATS" and the
     new version number "0".

     =============================================================
     Version 3.0                       (DAL/BTU) v3.0 (2007-10-31)

     Version 3.0 is the first non-backward-compatible release.
     In addition to the usual incremental changes, some
     elements and attributes have been renamed and/or remodeled
     to better meet user needs and to increase tag set consistency.
     All module change histories are available through the Tag Suite
     web site at http://dtd.nlm.nih.gov.

     Details on version 3.0 are available at
           http://dtd.nlm.nih.gov/3.0.

  1. Updated public identifier to "v3.0 20071031//EN"              -->

<!-- ============================================================= -->
<!-- Details concerning 
     ANSI/NISO Z39.96-2015 JATS-based DTDs, XSDs, 
     RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.3d2/                          -->

<!-- ============================================================= -->
<!--                    DEFAULT CLASSES AND MIXES                  -->
<!-- ============================================================= -->


<!--                    DEFAULT ELEMENT CLASSES MODULE             -->
<!--                    Set up the Parameter Entities and element
                        class definitions that will be used to
                        establish the content models for the
                        Archiving and Interchange DTD.             -->
<!ENTITY % default-classes.ent
                        PUBLIC
"-//NLM//DTD JATS (Z39.96) Default Element Classes Module v1.3 20210610//EN"
"JATS-default-classes1-3.ent"                                      >


<!--                    DEFAULT ELEMENT MIXES MODULE               -->
<!--                    Set up the Parameter Entities and element
                        mix definitions that will be used in
                        content models for the Archiving and
                        Interchange DTD.                           -->
<!ENTITY % default-mixes.ent
                        PUBLIC
"-//NLM//DTD JATS (Z39.96) Default Element Mixes Module v1.3 20210610//EN"
"JATS-default-mixes1-3.ent"                                        >


<!-- ============================================================= -->
<!--                    COMMON ATTRIBUTES SHARED BY ALL ELEMENTS   -->
<!-- ============================================================= -->


<!--                    COMMON ATTRIBUTES SHARED BY ALL ELEMENTS   -->
<!ENTITY % JATS-common-atts.ent
                        PUBLIC
"-//NLM//DTD JATS (Z39.96) JATS DTD Suite Common Attributes (for all elements) v1.3 20210610//EN"
"JATS-common-atts1-3.ent"                                          >


<!-- ============================================================= -->
<!--                    COMMON ELEMENTS SHARED BY MANY MODULES     -->
<!-- ============================================================= -->


<!--                    COMMON (SHARED) ELEMENT DECLARATIONS       -->
<!ENTITY % common.ent
                        PUBLIC
"-//NLM//DTD JATS (Z39.96) JATS DTD Suite Common (Shared) Elements Module v1.3 20210610//EN"
"JATS-common1-3.ent"                                               >


<!-- ============================================================= -->
<!--                    CLASS MODULES (ALPHABETICAL ORDER)         -->
<!-- ============================================================= -->


<!--                    ARTICLE METADATA ELEMENTS                  -->
<!ENTITY % articlemeta.ent
                        PUBLIC
"-//NLM//DTD JATS (Z39.96) JATS DTD Suite Journal Article Metadata Elements v1.3 20210610//EN"
"JATS-articlemeta1-3.ent"                                          >


<!--                    BACK MATTER ELEMENTS                       -->
<!ENTITY % backmatter.ent
                        PUBLIC
"-//NLM//DTD JATS (Z39.96) JATS DTD Suite Back Matter Elements v1.3 20210610//EN"
"JATS-backmatter1-3.ent"                                           >


<!--                    DISPLAY (GRAPHICAL) ELEMENTS INVOCATION    -->
<!ENTITY % display.ent
                        PUBLIC
"-//NLM//DTD JATS (Z39.96) JATS DTD Suite Display Class Elements v1.3 20210610//EN"
"JATS-display1-3.ent"                                              >


<!--                    FORMATTING ELEMENTS                        -->
<!--                    Elements that change rendition/display. This
                        module includes the Appearance Class, the
                        Break Class, and the Emphasis Class        -->
<!ENTITY % format.ent
                        PUBLIC
"-//NLM//DTD JATS (Z39.96) JATS DTD Suite Formatting Element Classes v1.3 20210610//EN"
"JATS-format1-3.ent"                                               >


<!--                    FUNDING ELEMENTS                           -->
<!--                    Elements that model open access, grant,
                        sponsorship, or other funding information,
                        for example the grant number (<award-id>)
                        and the <principal-investigator>           -->
<!ENTITY % funding.ent
                        PUBLIC
"-//NLM//DTD JATS (Z39.96) JATS DTD Suite Funding Elements v1.3 20210610//EN"
"JATS-funding1-3.ent"                                              >


<!--                    JOURNAL METADATA ELEMENTS                  -->
<!ENTITY % journalmeta.ent PUBLIC
"-//NLM//DTD JATS (Z39.96) JATS DTD Suite Journal Metadata Elements v1.3 20210610//EN"
"JATS-journalmeta1-3.ent"                                          >


<!--                    LINK ELEMENTS                              -->
<!ENTITY % link.ent  PUBLIC
"-//NLM//DTD JATS (Z39.96) JATS DTD Suite Link Class Elements v1.3 20210610//EN"
"JATS-link1-3.ent"                                                 >


<!--                    LIST ELEMENTS                              -->
<!ENTITY % list.ent  PUBLIC
"-//NLM//DTD JATS (Z39.96) JATS DTD Suite List Class Elements v1.3 20210610//EN"
"JATS-list1-3.ent"                                                 >


<!--                    MATH ELEMENTS                              -->
<!ENTITY % math.ent  PUBLIC
"-//NLM//DTD JATS (Z39.96) JATS DTD Suite Math Class Elements v1.3 20210610//EN"
"JATS-math1-3.ent"                                                 >


<!--                    PARAGRAPH-LEVEL ELEMENTS                   -->
<!ENTITY % para.ent  PUBLIC
"-//NLM//DTD JATS (Z39.96) JATS DTD Suite Paragraph-Like Elements v1.3 20210610//EN"
"JATS-para1-3.ent"                                                 >


<!--                    PHRASE-LEVEL CONTENT ELEMENTS              -->
<!ENTITY % phrase.ent
                        PUBLIC
"-//NLM//DTD JATS (Z39.96) JATS DTD Suite Subject Phrase Class Elements v1.3 20210610//EN"
"JATS-phrase1-3.ent"                                               >


<!--                    BIBLIOGRAPHY REFERENCES (CITATION) ELEMENTS-->
<!ENTITY % references.ent
                        PUBLIC
"-//NLM//DTD JATS (Z39.96) JATS DTD Suite Bibliographic Reference (Citation) Class Elements v1.3 20210610//EN"
"JATS-references1-3.ent"                                           >


<!--                    RELATED OBJECT ELEMENT                     -->
<!--                    Defines the <related-object> element to
                        describe a related object such as a
                        related book or a dataset.                 -->
<!ENTITY % related-object.ent
                       PUBLIC
"-//NLM//DTD JATS (Z39.96) JATS DTD Suite Related Object Element v1.3 20210610//EN"
"JATS-related-object1-3.ent"                                       >


<!--                    SECTION ELEMENTS                           -->
<!ENTITY % section.ent
                        PUBLIC
"-//NLM//DTD JATS (Z39.96) JATS DTD Suite Section Class Elements v1.3 20210610//EN"
"JATS-section1-3.ent"                                              >


<!-- ============================================================= -->
<!--                    TABLES: XHTML TABLE MODULES                -->
<!-- ============================================================= -->


<!--                    XHTML TABLE SETUP MODULE                   -->
<!--                    Set all Parameter Entities needed by the
                        HTML 4.0 (XHTML) table model, and then
                        call the module containing that model.
                        Authoring Note: If wanted, this module
                        will be invoked in the DTD module          -->
<!ENTITY % XHTMLtablesetup.ent
                        PUBLIC
"-//NLM//DTD JATS (Z39.96) JATS DTD Suite XHTML Table Setup Module v1.3 20210610//EN"
"JATS-XHTMLtablesetup1-3.ent"                                      >


<!--                    XHTML TABLE MODEL                          -->
<!--                    The public XML version of the XHTML 1.1
                        table model. This module is invoked in the
                         module %JATS-XHTMLtablesetup1-3d2.ent;    -->
<!ENTITY % xhtml-table-1.mod
                        PUBLIC
"-//W3C//ELEMENTS XHTML Tables 1.0//EN"
"xhtml-table-1.mod"                                                  >


<!--                    XHTML TABLE INLINE STYLE MODULE            -->
<!--                    The public XML version of the XHTML 1.1
                        inline style module for use in XHTML tables.
                        This module is invoked in the
                         module %JATS-XHTMLtablesetup1-3d2.ent;             -->
<!ENTITY % xhtml-inlstyle-1.mod
                        PUBLIC
"-//W3C//ENTITIES XHTML Inline Style 1.0//EN"
"xhtml-inlstyle-1.mod"                                                  >


<!-- ============================================================= -->
<!--                    ALI: NISO ACCESS AND INDICATORS            -->
<!-- ============================================================= -->


<!--                    NISO ACCESS AND INDICATORS (RP-22-1015)    -->
<!--                    Set all Parameter Entities needed by the
                        NISO ALI licensing elements.
                        This module is invoked by each DTD.        -->
<!ENTITY % JATS-ali-namespace.ent
                        PUBLIC
"-//NLM//DTD JATS ALI Namespace Module v1.3 20210610//EN"
"JATS-ali-namespace1-3.ent"                                        >


<!-- ============================================================= -->
<!--                    TABLES: OASIS EXCHANGE TABLE MODULES       -->
<!-- ============================================================= -->


<!--                    OASIS XML TABLE SETUP MODULE               -->
<!--                    Set all Parameter Entities needed by the
                        OASIS (CALS) Table Exchange table model.
                        Authoring Note: If wanted, this module
                        will be invoked in the DTD module          -->
<!ENTITY % oasis-tablesetup.ent
                        PUBLIC
"-//NLM//DTD JATS (Z39.96) Namespaced OASIS XML Table Setup Module v1.3 20210610//EN"
"JATS-oasis-tablesetup1-3.ent"                                     >


<!--                    OASIS XML TABLE MODEL                      -->
<!--                    The OASIS (CALS) Table Exchange table model
                        This module is invoked in
                        %JATS-oasis-tablesetup1-3d2.ent;           -->
<!ENTITY % oasis-exchange.ent
                        PUBLIC
"-//OASIS//DTD XML Exchange Table Model 19990315//EN"
"oasis-exchange.ent"                                                 >


<!-- ============================================================= -->
<!--                    SPECIAL CHARACTER MODULES                  -->
<!-- ============================================================= -->


<!--                    SPECIAL CHARACTERS DECLARATIONS            -->
<!--                    Declares any standard XML special character
                        entities used in this DTD                  -->
<!ENTITY % xmlspecchars.ent
                        PUBLIC
"-//NLM//DTD JATS (Z39.96) JATS DTD Suite XML Special Characters Module v1.3 20210610//EN"
"JATS-xmlspecchars1-3.ent"                                         >


<!--                    CUSTOM SPECIAL CHARACTERS DECLARATIONS     -->
<!--                    Declares any custom special character
                        entities created for this Suite            -->
<!ENTITY % chars.ent PUBLIC
"-//NLM//DTD JATS (Z39.96) JATS DTD Suite Custom Special Characters Module v1.3 20210610//EN"
"JATS-chars1-3.ent"                                                >


<!-- ============================================================= -->
<!--                    MATH: MATHML MODULES                       -->
<!--                    Only one of the modules below may be       -->
<!--                    invoked by a DTD.                          -->
<!-- ============================================================= -->


<!--                    MATHML 2.0 SETUP MODULE                    -->
<!--                    Called from the DTD to include the MathML 2.0
                        elements in the tag set.                   -->
<!ENTITY % mathmlsetup.ent
                        PUBLIC
"-//NLM//DTD JATS (Z39.96) JATS DTD Suite MathML Setup Module v1.3 20210610//EN"
"JATS-mathmlsetup1-3.ent"                                          >


<!--                    MATHML 3.0 SETUP MODULE                    -->
<!--                    Called from the DTD to include the MathML 3.0
                        elements in the tag set.                   -->
<!ENTITY % mathml3-mathmlsetup.ent
                        PUBLIC
"-//NLM//DTD JATS (Z39.96) JATS DTD Suite MathML 3.0 Setup Module v1.3 20210610//EN"
"JATS-mathml3-mathmlsetup1-3.ent"                                  >


<!-- ============================================================= -->
<!--                     MATHML 2.0 MODULES (below for JATS V1)    -->
<!--                     MATHML 3.0 modules are invoked in the DTD -->
<!-- ============================================================= -->


<!--                    MATHML 2.0 QUALIFIED NAMES                 -->
<!ENTITY % mathml-qname.mod
                        PUBLIC
"-//W3C//ENTITIES MathML 2.0 Qualified Names 1.0//EN"
"mathml2-qname-1.mod"                                                >


<!--                    MATHML 2.0 DTD                             -->
<!ENTITY % mathml.dtd   PUBLIC
"-//W3C//DTD MathML 2.0//EN"
"mathml2.dtd"                                                        >


<!--                    MATHML 2.0 EXTRA ENTITIES                  -->
<!ENTITY % ent-mmlextra
                        PUBLIC
"-//W3C//ENTITIES Extra for MathML 2.0//EN"
"mathml/mmlextra.ent"                                               >


<!--                    MATHML 2.0 ALIASES                         -->
<!ENTITY % ent-mmlalias
                        PUBLIC
"-//W3C//ENTITIES Aliases for MathML 2.0//EN"
"mathml/mmlalias.ent"                                               >


<!-- ============================================================= -->
<!--                     NOTATIONS MODULE                          -->
<!-- ============================================================= -->


<!--                    NOTATION DECLARATIONS MODULE               -->
<!--                    Container module for the Notation Declarations
                        to be used with this DTD suite.  Placed in
                        their own module for easy expansion or
                        replacement.                               -->
<!ENTITY % notat.ent PUBLIC
"-//NLM//DTD JATS (Z39.96) JATS DTD Suite Notation Declarations v1.3 20210610//EN"
"JATS-notat1-3.ent"                                                >

<!-- ============================================================= -->
<!--                     NLM CITATION (DEPRECATED ELEMENT) MODULE  -->
<!-- ============================================================= -->


<!--                    NLM CITATION MODULE                        -->
<!--                    The only new element created for the
                        Publishing DTD, the highly structured NLM
                        citation, to enforce a slightly loose version
                        of an NLM-structured bibliographic reference.
                        Sequence is enforced and interior punctuation
                        is expected to be generated.               -->
<!ENTITY % nlmcitation.ent
                        PUBLIC
"-//NLM//DTD JATS (Z39.96) NLM Citation v1.3 20210610//EN"
"JATS-nlmcitation1-3.ent"                                          >

<!-- ============================================================= -->
<!--                     MODULES TO ADD BITS INLINE INDEX ELEMENTS -->
<!-- ============================================================= -->


<!--                    BITS 2.0 EMBEDDED INDEX ELEMENTS MOD       -->
<!--                    Element declarations the index elements
                        which are embedded in the document
                        narrative.                                 -->
<!ENTITY % index-term.ent
                        PUBLIC
"-//NLM//DTD BITS Embedded Index Element Module v2.0 20151225//EN"
"BITS-embedded-index2.ent"                                           >

<!-- ============================================================= -->
<!--                     MODULES TO ADD BITS QUESTION/ANSWERS      -->
<!-- ============================================================= -->


<!--                    BITS 2.0 QUESTION AND ANSWER MODULE        -->
<!--                    Element declarations for BITS questions
                        and answers (not complete tests, but used
                        to put build quizzes and tests.            -->
<!ENTITY % question-answer.ent
                        PUBLIC
"-//NLM//DTD BITS Question and Answer Module v2.0 20151225//EN"
"BITS-question-answer2.ent"                                          >

<!-- ============================================================= -->
<!--                     NAMESPACE FOR W3C XSI PREFIX FOR DTDS
                         THAT DO NOT USE MATHML                    -->
<!-- ============================================================= -->

<!--<!ENTITY % JATS-xsi-schema-namespace.ent
                        PUBLIC
"-//NLM//DTD JATS (Z39.96) JATS DTD Suite XSI Namespace Setup Module v1.3 20210610//EN"
"JATS-xsi-schema-namespace1-3.ent"                                  >
-->
<!-- =================== End Journal Article Module of Modules === -->


<!--
     File isomscr.ent produced by the XSL script characters.xsl
     from input data in unicode.xml.

     Please report any errors to <PERSON>
     via the public W3<NAME_EMAIL>.

     The numeric character values assigned to each entity
     (should) match the Unicode assignments in Unicode 4.0.

     Entity names in this file are derived from files carrying the
     following notice:

     (C) International Organization for Standardization 1991
     Permission to copy in any form is granted for use with
     conforming SGML systems and applications as defined in
     ISO 8879, provided this notice is included in all copies.

-->

<!ENTITY % plane1D  "&#38;#38;#x1D">

<!ENTITY Ascr             "%plane1D;49C;" ><!--/scr A, script letter A -->
<!ENTITY ascr             "%plane1D;4B6;" ><!--/scr a, script letter a -->
<!ENTITY Bscr             "&#x0212C;" ><!--/scr B, script letter B -->
<!ENTITY bscr             "%plane1D;4B7;" ><!--/scr b, script letter b -->
<!ENTITY Cscr             "%plane1D;49E;" ><!--/scr C, script letter C -->
<!ENTITY cscr             "%plane1D;4B8;" ><!--/scr c, script letter c -->
<!ENTITY Dscr             "%plane1D;49F;" ><!--/scr D, script letter D -->
<!ENTITY dscr             "%plane1D;4B9;" ><!--/scr d, script letter d -->
<!ENTITY Escr             "&#x02130;" ><!--/scr E, script letter E -->
<!ENTITY escr             "&#x0212F;" ><!--/scr e, script letter e -->
<!ENTITY Fscr             "&#x02131;" ><!--/scr F, script letter F -->
<!ENTITY fscr             "%plane1D;4BB;" ><!--/scr f, script letter f -->
<!ENTITY Gscr             "%plane1D;4A2;" ><!--/scr G, script letter G -->
<!ENTITY gscr             "&#x0210A;" ><!--/scr g, script letter g -->
<!ENTITY Hscr             "&#x0210B;" ><!--/scr H, script letter H -->
<!ENTITY hscr             "%plane1D;4BD;" ><!--/scr h, script letter h -->
<!ENTITY Iscr             "&#x02110;" ><!--/scr I, script letter I -->
<!ENTITY iscr             "%plane1D;4BE;" ><!--/scr i, script letter i -->
<!ENTITY Jscr             "%plane1D;4A5;" ><!--/scr J, script letter J -->
<!ENTITY jscr             "%plane1D;4BF;" ><!--/scr j, script letter j -->
<!ENTITY Kscr             "%plane1D;4A6;" ><!--/scr K, script letter K -->
<!ENTITY kscr             "%plane1D;4C0;" ><!--/scr k, script letter k -->
<!ENTITY Lscr             "&#x02112;" ><!--/scr L, script letter L -->
<!ENTITY lscr             "%plane1D;4C1;" ><!--/scr l, script letter l -->
<!ENTITY Mscr             "&#x02133;" ><!--/scr M, script letter M -->
<!ENTITY mscr             "%plane1D;4C2;" ><!--/scr m, script letter m -->
<!ENTITY Nscr             "%plane1D;4A9;" ><!--/scr N, script letter N -->
<!ENTITY nscr             "%plane1D;4C3;" ><!--/scr n, script letter n -->
<!ENTITY Oscr             "%plane1D;4AA;" ><!--/scr O, script letter O -->
<!ENTITY oscr             "&#x02134;" ><!--/scr o, script letter o -->
<!ENTITY Pscr             "%plane1D;4AB;" ><!--/scr P, script letter P -->
<!ENTITY pscr             "%plane1D;4C5;" ><!--/scr p, script letter p -->
<!ENTITY Qscr             "%plane1D;4AC;" ><!--/scr Q, script letter Q -->
<!ENTITY qscr             "%plane1D;4C6;" ><!--/scr q, script letter q -->
<!ENTITY Rscr             "&#x0211B;" ><!--/scr R, script letter R -->
<!ENTITY rscr             "%plane1D;4C7;" ><!--/scr r, script letter r -->
<!ENTITY Sscr             "%plane1D;4AE;" ><!--/scr S, script letter S -->
<!ENTITY sscr             "%plane1D;4C8;" ><!--/scr s, script letter s -->
<!ENTITY Tscr             "%plane1D;4AF;" ><!--/scr T, script letter T -->
<!ENTITY tscr             "%plane1D;4C9;" ><!--/scr t, script letter t -->
<!ENTITY Uscr             "%plane1D;4B0;" ><!--/scr U, script letter U -->
<!ENTITY uscr             "%plane1D;4CA;" ><!--/scr u, script letter u -->
<!ENTITY Vscr             "%plane1D;4B1;" ><!--/scr V, script letter V -->
<!ENTITY vscr             "%plane1D;4CB;" ><!--/scr v, script letter v -->
<!ENTITY Wscr             "%plane1D;4B2;" ><!--/scr W, script letter W -->
<!ENTITY wscr             "%plane1D;4CC;" ><!--/scr w, script letter w -->
<!ENTITY Xscr             "%plane1D;4B3;" ><!--/scr X, script letter X -->
<!ENTITY xscr             "%plane1D;4CD;" ><!--/scr x, script letter x -->
<!ENTITY Yscr             "%plane1D;4B4;" ><!--/scr Y, script letter Y -->
<!ENTITY yscr             "%plane1D;4CE;" ><!--/scr y, script letter y -->
<!ENTITY Zscr             "%plane1D;4B5;" ><!--/scr Z, script letter Z -->
<!ENTITY zscr             "%plane1D;4CF;" ><!--/scr z, script letter z -->

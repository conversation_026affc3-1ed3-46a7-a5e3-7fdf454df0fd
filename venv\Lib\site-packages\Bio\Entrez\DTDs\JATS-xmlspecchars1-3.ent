<!-- ============================================================= -->
<!--  MODULE:    XML Special Characters Module                     -->
<!--  VERSION:   ANSI/NISO JATS Version 1.3 (Z39.96-2021)          --> 
<!--  DATE:      June 2021                                         --> 
<!--                                                               -->
<!-- ============================================================= -->

<!-- ============================================================= -->
<!--                    PUBLIC DOCUMENT TYPE DEFINITION            -->
<!--                        TYPICAL INVOCATION                     -->
<!--
"-//NLM//DTD JATS (Z39.96) JATS DTD Suite XML Special Characters Module v1.3 20210610//EN"
     Delivered as file "JATS-xmlspecchars1-3.ent"                  -->
<!-- ============================================================= -->

<!-- ============================================================= -->
<!-- SYSTEM:     JATS DTD Suite                                    -->
<!--                                                               -->
<!-- PURPOSE:    External Parameter Entities for calling in the    -->
<!--             special character entities                        -->
<!--                                                               -->
<!-- MODULES REQUIRED:                                             -->
<!--             The standard ISO special character entity sets    -->
<!--             (see below)                                       -->
<!--                                                               -->
<!-- TAG SET SPONSOR                                               -->
<!--             National Center for Biotechnology                 -->
<!--                Information (NCBI)                             -->
<!--             National Library of Medicine (NLM)                -->
<!--                                                               -->
<!-- CREATED FOR:                                                  -->
<!--             This module was created for the JATS DTD Suite.   -->
<!--             Digital archives and publishers may use the       -->
<!--             DTD as is for markup of journal literature or     -->
<!--             related material for archiving and transferring   -->
<!--             such material between archives or create a        -->
<!--             custom XML DTD from the Suite for these purposes. -->
<!--                                                               -->
<!--             This DTD is in the public domain. An organization -->
<!--             that wishes to create its own DTD from the suite  -->
<!--             may do so without permission from NLM.            -->
<!--                                                               -->
<!--             The suite has been set up to be extended using a  -->
<!--             new DTD file and a new DTD-specific customization -->
<!--             module to redefine the many Parameter Entities.   -->
<!--             Do not modify the suite directly or redistribute  -->
<!--             modified versions of the suite.                   -->
<!--                                                               -->
<!--             In the interest of maintaining consistency and    -->
<!--             clarity for potential users, NLM requests:        -->
<!--                                                               -->
<!--             1. If you create a DTD from the JATS DTD Suite    -->
<!--                and intend to stay compatible with the suite,  -->
<!--                then please include the following statement    -->
<!--                as a comment in all of your DTD modules:       -->
<!--                   "Created from, and fully compatible with,   -->
<!--                    the ANSI/NISO Z39.96 Journal Article Tag   -->
<!--                    Suite (JATS)."                             -->
<!--                                                               -->
<!--             2. If you alter one or more modules of the suite, -->
<!--                then please rename your version and all its    -->
<!--                modules to avoid any confusion with the        -->
<!--                original suite. Also, please include the       -->
<!--                following statement as a comment in all your   -->
<!--                DTD modules:                                   -->
<!--                   "Based in part on, but not fully compatible -->
<!--                    with, the ANSI/NISO Z39.96 Journal Article -->
<!--                    Tag Suite (JATS)."                         -->
<!--                                                               -->
<!-- ORIGINAL CREATION DATE:                                       -->
<!--             December 2002                                     -->
<!--                                                               -->
<!-- CREATED BY: Mulberry Technologies, Inc. for the NISO Z39.96   -->
<!--             Working Group. Mulberry Technologies was          -->
<!--             supported by National Center for Biotechnology    -->
<!--             Information (NCBI), a center of the US National   -->
<!--             Library of Medicine (NLM).                        -->
<!--                                                               -->
<!--             This module is part of the JATS DTD Suite. The    -->
<!--             Suite is a continuation of work done by NCBI,     -->
<!--             Mulberry Technologies, and Inera Inc. on the NLM  -->
<!--             Journal Archiving and Interchange DTD Suite, which-->
<!--             was originally released in December, 2002.        -->
<!--                                                               -->
<!--             NLM thanks the Harvard University Libraries, both -->
<!--             for proposing that a draft archiving NLM DTD for  -->
<!--             life sciences journals be extended to accommodate -->
<!--             journals in all disciplines and for sponsoring    -->
<!--             Inera Inc.'s collaboration with other DTD         -->
<!--             authors in completing NLM Version 1.0. The        -->
<!--             Andrew W. Mellon Foundation provided support for  -->
<!--             these important contributions.                    -->
<!--                                                               -->
<!--             Suggestions for refinements and enhancements to   -->
<!--             this DTD should be sent in email to:              -->
<!--                 <EMAIL>                         -->
<!-- ============================================================= -->


<!-- ============================================================= -->
<!--                    DTD VERSION/CHANGE HISTORY                 -->
<!-- ============================================================= -->
<!--
Version  Reason/Occasion                   (who) vx.x (yyyy-mm-dd)
   ==============================================================
     JATS Version 1.3 (ANSI/NISO Z39.96-2021)
                                  (DAL/BTU) v1.3 (2021-06-10)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by both the NISO and ANSI memberships 
     to be changed. This version of the NISO JATS was approved 
     by ANSI vote on June 10, 2021, so it supersedes all Committee 
     Drafts as well as the draft version JATS 1.3 2021 (the version
     submitted for vote), becoming NISO JATS v1.3 20210610 
     (ANSI/NISO Z39.96-2019).

     The file names and formal public identifiers have been 
     changed in the modules and the catalogs.

 15. JATS 'v1.3d2 20201130' becomes 'v1.3 20210610' 
     (final ANSI vote date)
     'ANSI/NISO Z39.96-2019' becomes 'ANSI/NISO Z39.96-2021'

   ==============================================================
     JATS Version 1.3d2 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.3d2 (2020-11-30)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by the NISO and ANSI memberships 
     to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     of the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting; it is for Standing Committee review and
     testing. The formal public identifiers have been changed 
     in the modules and the catalogs.
 
 14. JATS "v1.3d2 20201130" becomes 'v1.3 20210610' (second 
     committee draft following the final ANSI/NISO 1.2 vote)

   ==============================================================
     JATS Version 1.3d1 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.3.d1 (2019-08-31)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by the NISO and ANSI memberships 
     to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     of the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
  
 13. JATS "1.2" becomes "v1.3d1 20190831" (first committee
     draft following the final ANSI/NISO vote) 

   ==============================================================
     JATS Version 1.2 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.2     (2019-02-08)
    
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the both the NISO and ANSI membership 
     to be changed. This version of the NISO JATS was approved 
     by ANSI vote on Feb 08, 2019, so it supersedes all Committee 
     Drafts as well as the draft version JATS 1.2 2018 (the version
     submitted for vote), becoming NISO JATS v1.2 20190208 
     (ANSI/NISO Z39.96-2021).
  
 12. JATS "1.2" becomes "v1.2 20190208" (final ANSI vote date)
     "ANSI/NISO Z39.96-2015" becomes "ANSI/NISO Z39.96-2019"
     
   ==============================================================
     JATS Version 1.2 (ANSI/NISO Z39.96-2018)
                                   (DAL/BTU) v1.2     (2018-11-30)
    
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the both the NISO and ANSI membership 
     to be changed. This version of the NISO JATS was approved 
     by ANSI and NISO vote, so it supersedes all Committee Drafts
     and becoming NISO JATS 1.2 (ANSI/NISO Z39.96-2018).
  
 11. JATS "1.2d2" and "v1.2d2 20180401" became
     JATS "1.2" and "v1.2 20181130" 

    =============================================================
     JATS Version 1.2d2              (DAL/BTU) v1.2d2  (2018-04-01)

     JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
     BITS is under continuous maintenance, and is modified at the
     discretion of the working group.
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.

 10. BITS "2.0" and "v2.0 20151225" remains unchanged
   
     JATS "1.2d1" and "v1.2d1 20171231" became
     JATS "1.2d2" and "v1.2d2 20180401". 

     No module names were changed.
 
    =============================================================
     JATS Version 1.2d1 (ANSI/NISO Z39.96-2015)
                                   (DAL/BTU) v1.2d1  (2017-12-31)
 
     NISO JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
  
  9. JATS "1.2d1" and "v1.2d1 20170631" became
     JATS "1.2d1" and "v1.2d1 20171231". 

    =============================================================
     JATS Version 1.2d1              (DAL/BTU) v1.2d1  (2017-06-31)

     NISO JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
    
  8. JATS became version "1.2d1" and "v1.2d1 20170631" 

     =============================================================
     JATS Version 1.1                (DAL/BTU) v1.1  (2015-12-15)

     JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
     JATS 1.1 was approved in late 2015, so the formal public 
     identifiers were changed in the modules and the catalogs.
     No model or attribute changes were made at this time. 
 
  7. JATS became version "1.1" and "v1.1 20151215"
     BITS remained version "2.0" but becomes "v2.0 20151225"

     =============================================================
     JATS Version 1.1d3             (DAL/BTU) v1.1d3  (2015-03-01)
   
     The changes in this release are in response to NISO 
     License and Indicators Recommended Practice.

     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the NISO membership to be changed. This
     Committee Draft 1.1d3 will be sent to the NISO voting
     membership, to become (if approved) NISO JATS 1.1.
     
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee.

  6. JATS became version "1.1d3" and "v1.1 20150301//EN"
   
     =============================================================
     NISO JATS Version 1.0             (DAL/BTU) v1.0 (2012-xx-xx)
   
     ANSI/NISO Z39.96-2012 (Version 1.0) 

     Details concerning ANSI/NISO Z39.96-2012 JATS-based DTDs,  
     XSDs, RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.3d2/
   
  5. OASIS TABLE NAMESPACE MODIFICATIONS - Added a new module
     that sets up the namespace URI and namespace prefix for
     the OASIS tables models.
   
     =============================================================
     NISO JATS Version 1.0             (DAL/BTU) v1.0 (2012-xx-xx)
   
     ANSI/NISO Z39.96-2012 (Version 1.0) 

     Details concerning ANSI/NISO Z39.96-2012 JATS-based DTDs,  
     XSDs, RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.3d2/
   
  4. Updated the DTD-version attribute to "1.0" and the formal
     public identifier to the date: "v1.0 20120330//EN".

     =============================================================
     Version 0.4                       (DAL/BTU) v0.4 (2011-01-31)
   
     This Tag Set is in the process of becoming a NISO standard.
     The version numbers are starting over from 0.4", as a Trial
     Use Draft, to be made into "Version 1.0" when the Tag Suite 
     becomes a NISO standard. Thus, the version number that would
     have been "NLM Version 3.1 Draft" has become NISO JATS
     "Version 0.4".

     Details on NISO Trial Use Draft Version 0.4 are available at
           http://jats.nlm.nih.gov/JATS-0.4.
   
  3. Updated the DTD-version attribute to "0.4" 
   
  2. Updated the public identifier to "v0.4 20110131", 
     modified the formal public identifier to include "JATS (Z39.96)",
     and the filename as delivered to include "JATS" and the
     new version number "0".

     =============================================================
     Version 3.0                       (DAL/BTU) v3.0 (2007-10-31)

     Version 3.0 is the first non-backward-compatible release.
     In addition to the usual incremental changes, some
     elements and attributes have been renamed and/or remodeled
     to better meet user needs and to increase tag set consistency.
     All module change histories are available through the Tag Suite
     web site at http://dtd.nlm.nih.gov.

     Details on version 3.0 are available at
           http://dtd.nlm.nih.gov/3.0.

  1. Updated public identifier to "v3.0 20071031//EN"              -->

<!-- ============================================================= -->
<!-- Details concerning 
     ANSI/NISO Z39.96-2015 JATS-based DTDs, XSDs, 
     RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.1/                            -->

<!-- ============================================================= -->
<!--                    ENTITY SETS FROM INFORMATIVE ANNEX TO      -->
<!--                       ISO 8879:1986 (SGML)                    -->
<!-- ============================================================= -->


<!--                    ISO STANDARD ADDED LATIN 1                 -->
<!ENTITY % ISOlat1 PUBLIC
"-//W3C//ENTITIES Added Latin 1 for MathML 2.0//EN"
"iso8879/isolat1.ent"                                                >


<!--                    ISO STANDARD ADDED LATIN 2                 -->
<!ENTITY % ISOlat2 PUBLIC
"-//W3C//ENTITIES Added Latin 2 for MathML 2.0//EN"
"iso8879/isolat2.ent"                                                >


<!--                    ISO BOX AND LINE DRAWING                   -->
<!ENTITY % ISObox       PUBLIC
"-//W3C//ENTITIES Box and Line Drawing for MathML 2.0//EN"
"iso8879/isobox.ent"                                                 >


<!--                    ISO STANDARD DIACRITICAL MARKS             -->
<!ENTITY % ISOdia PUBLIC
"-//W3C//ENTITIES Diacritical Marks for MathML 2.0//EN"
"iso8879/isodia.ent"                                                 >


<!--                    ISO STANDARD NUMERIC AND SPECIAL GRAPHIC   -->
<!ENTITY % ISOnum PUBLIC
"-//W3C//ENTITIES Numeric and Special Graphic for MathML 2.0//EN"
"iso8879/isonum.ent"                                                 >


<!--                    ISO STANDARD PUBLISHING                    -->
<!ENTITY % ISOpub PUBLIC
"-//W3C//ENTITIES Publishing for MathML 2.0//EN"
"iso8879/isopub.ent"                                                 >


<!--                    ISO STANDARD RUSSIAN CYRILLIC              -->
<!ENTITY % ISOcyr1 PUBLIC
"-//W3C//ENTITIES Russian Cyrillic for MathML 2.0//EN"
"iso8879/isocyr1.ent"                                                >


<!--                    ISO STANDARD NON-RUSSIAN CYRILLIC          -->
<!ENTITY % ISOcyr2 PUBLIC
"-//W3C//ENTITIES Non-Russian Cyrillic for MathML 2.0//EN"
"iso8879/isocyr2.ent"                                                >


<!-- ============================================================= -->
<!--                    ISO 8879 NOT USED BY MATHML                -->
<!-- ============================================================= -->


<!--                    ISO STANDARD GREEK LETTERS                 -->
<!ENTITY % ISOgrk1 PUBLIC
"-//W3C//ENTITIES Greek Letters//EN"
"xmlchars/isogrk1.ent"                                               >


<!--                    ISO STANDARD MONOTONIKO GREEK              -->
<!ENTITY % ISOgrk2 PUBLIC
"-//W3C//ENTITIES Monotoniko Greek//EN"
"xmlchars/isogrk2.ent"                                               >


<!--                    ISO STANDARD ALTERNATIVE GREEK SYMBOLS     -->
<!ENTITY % ISOgrk4 PUBLIC
"-//W3C//ENTITIES Alternative Greek Symbols//EN"
"xmlchars/isogrk4.ent"                                               >


<!-- ============================================================= -->
<!--                    ISO TECHNICAL REPORT 9573-13 ENTITY SETS   -->
<!-- ============================================================= -->


<!--                    ISO STANDARD GENERAL TECHNICAL             -->
<!ENTITY % ISOtech PUBLIC
"-//W3C//ENTITIES General Technical for MathML 2.0//EN"
"iso9573-13/isotech.ent"                                             >


<!--                    ISO STANDARD GREEK SYMBOLS                 -->
<!ENTITY % ISOgrk3 PUBLIC
"-//W3C//ENTITIES Greek Symbols for MathML 2.0//EN"
"iso9573-13/isogrk3.ent"                                             >


<!--                    ISO STANDARD MATH ALPHABETS (SCRIPT)       -->
<!ENTITY % ISOmscr PUBLIC
"-//W3C//ENTITIES Math Alphabets: Script for MathML 2.0//EN"
"iso9573-13/isomscr.ent"                                             >


<!--                    ISO STANDARD ADDED MATH SYMBOLS
                           (ARROW RELATIONS)                       -->
<!ENTITY % ISOamsa PUBLIC
"-//W3C//ENTITIES Added Math Symbols: Arrow Relations for MathML 2.0//EN"
"iso9573-13/isoamsa.ent"                                             >


<!--                    ISO STANDARD ADDED MATH SYMBOLS
                           (BINARY OPERATORS)                      -->
<!ENTITY % ISOamsb PUBLIC
"-//W3C//ENTITIES Added Math Symbols: Binary Operators for MathML 2.0//EN"
"iso9573-13/isoamsb.ent"                                             >


<!--                    ISO STANDARD ADDED MATH SYMBOLS
                           (DELIMITERS)                            -->
<!ENTITY % ISOamsc PUBLIC
"-//W3C//ENTITIES Added Math Symbols: Delimiters for MathML 2.0//EN"
"iso9573-13/isoamsc.ent"                                             >


<!--                    ISO STANDARD ADDED MATH SYMBOLS
                           (NEGATED RELATIONS)                     -->
<!ENTITY % ISOamsn PUBLIC
"-//W3C//ENTITIES Added Math Symbols: Negated Relations for MathML 2.0//EN"
"iso9573-13/isoamsn.ent"                                             >


<!--                    ISO STANDARD ADDED MATH SYMBOLS (ORDINARY) -->
<!ENTITY % ISOamso PUBLIC
"-//W3C//ENTITIES Added Math Symbols: Ordinary for MathML 2.0//EN"
"iso9573-13/isoamso.ent"                                             >


<!--                    ISO STANDARD ADDED MATH SYMBOLS
                           (RELATIONS)                             -->
<!ENTITY % ISOamsr PUBLIC
"-//W3C//ENTITIES Added Math Symbols: Relations for MathML 2.0//EN"
"iso9573-13/isoamsr.ent"                                             >


<!--                    ISO STANDARD MATH ALPHABETS (FRAKTUR)      -->
<!ENTITY % ISOmfrk PUBLIC
"-//W3C//ENTITIES Math Alphabets: Fraktur for MathML 2.0//EN"
"iso9573-13/isomfrk.ent"                                             >


<!--                    ISO STANDARD MATH ALPHABETS (OPEN FACE)    -->
<!ENTITY % ISOmopf PUBLIC
"-//W3C//ENTITIES Math Alphabets: Open Face for MathML 2.0//EN"
"iso9573-13/isomopf.ent"                                             >


<!-- ============================================================= -->
<!--                    ISO SPECIAL CHARACTER SETS INVOKED         -->
<!-- ============================================================= -->


%ISOlat1;
%ISOlat2;
%ISObox;
%ISOdia;
%ISOnum;
%ISOpub;
%ISOcyr1;
%ISOcyr2;

%ISOgrk1;
%ISOgrk2;
%ISOgrk4;

%ISOtech;
%ISOgrk3;
%ISOamsa;
%ISOamsb;
%ISOamsc;
%ISOamsn;
%ISOamso;
%ISOamsr;
%ISOmscr;
%ISOmfrk;
%ISOmopf;


<!--                    MAINTENANCE NOTE:
                        Custom special characters are declared
                        in a separate module %chars.ent;           -->


<!-- ============ End of XML Special Characters Module =========== -->

<!-- ============================================================= -->
<!--  MODULE:    XHTML Table Setup Module                          -->
<!--  VERSION:   ANSI/NISO JATS Version 1.3 (Z39.96-2021)          --> 
<!--  DATE:      June 2021                                         --> 
<!--                                                               -->
<!-- ============================================================= -->

<!-- ============================================================= -->
<!--                    PUBLIC DOCUMENT TYPE DEFINITION            -->
<!--                        TYPICAL INVOCATION                     -->
<!--
"-//NLM//DTD JATS (Z39.96) JATS DTD Suite XHTML Table Setup Module v1.3 20210610//EN"
     Delivered as file "JATS-XHTMLtablesetup1-3.ent"               -->
<!-- ============================================================= -->

<!-- ============================================================= -->
<!-- SYSTEM:     JATS DTD Suite                                    -->
<!--                                                               -->
<!-- PURPOSE:    Provides the organization for using the XHTML 1.1 -->
<!--             table model                                       -->
<!--                                                               -->
<!-- CONTAINS:   1) Invokes the inline style attribute module      -->
<!--                  to pick up the "style" attribute             -->
<!--             2) Overrides to standard parameter entities used  -->
<!--                in the XHTML 1.1 table model                   -->
<!--             3) Invokes the XHTML 1.1 table model              -->
<!--                                                               -->
<!-- MODULES REQUIRED:                                             -->
<!--             1) XHTML inline style module                      -->
<!--                  (-%xhtml-inlstyle-1.mod;)                    -->
<!--             2) XHTML 1.1 table model (-%xhtml-table-1.mod;)   -->
<!--                                                               -->
<!-- TAG SET SPONSOR                                               -->
<!--             National Center for Biotechnology                 -->
<!--                Information (NCBI)                             -->
<!--             National Library of Medicine (NLM)                -->
<!--                                                               -->
<!-- CREATED FOR:                                                  -->
<!--             This module was created for the JATS DTD Suite.   -->
<!--             Digital archives and publishers may use the       -->
<!--             DTD as is for markup of journal literature or     -->
<!--             related material for archiving and transferring   -->
<!--             such material between archives or create a        -->
<!--             custom XML DTD from the Suite for                 -->
<!--             these purposes.                                   -->
<!--                                                               -->
<!--             This DTD is in the public domain. An organization -->
<!--             that wishes to create its own DTD from the suite  -->
<!--             may do so without permission from NLM.            -->
<!--                                                               -->
<!--             The suite has been set up to be extended using a  -->
<!--             new DTD file and a new DTD-specific customization -->
<!--             module to redefine the many Parameter Entities.   -->
<!--             Do not modify the suite directly or redistribute  -->
<!--             modified versions of the suite.                   -->
<!--                                                               -->
<!--             In the interest of maintaining consistency and    -->
<!--             clarity for potential users, NLM requests:        -->
<!--                                                               -->
<!--             1. If you create a DTD from the JATS DTD Suite    -->
<!--                and intend to stay compatible with the suite,  -->
<!--                then please include the following statement    -->
<!--                as a comment in all of your DTD modules:       -->
<!--                   "Created from, and fully compatible with,   -->
<!--                    the ANSI/NISO Z39.96 Journal Article Tag   -->
<!--                    Suite (JATS)."                             -->
<!--                                                               -->
<!--             2. If you alter one or more modules of the suite, -->
<!--                then please rename your version and all its    -->
<!--                modules to avoid any confusion with the        -->
<!--                original suite. Also, please include the       -->
<!--                following statement as a comment in all your   -->
<!--                DTD modules:                                   -->
<!--                   "Based in part on, but not fully compatible -->
<!--                    with, the ANSI/NISO Z39.96 Journal Article -->
<!--                    Tag Suite (JATS)."                         -->
<!--                                                               -->
<!-- ORIGINAL CREATION DATE:                                       -->
<!--             December 2002                                     -->
<!--                                                               -->
<!-- CREATED BY: Mulberry Technologies, Inc. for the NISO Z39.96   -->
<!--             Working Group. Mulberry Technologies was          -->
<!--             supported by National Center for Biotechnology    -->
<!--             Information (NCBI), a center of the US National   -->
<!--             Library of Medicine (NLM).                        -->
<!--                                                               -->
<!--             This module is part of the JATS DTD Suite. The    -->
<!--             Suite is a continuation of work done by NCBI,     -->
<!--             Mulberry Technologies, and Inera Inc. on the NLM  -->
<!--             Journal Archiving and Interchange DTD Suite, which-->
<!--             was originally released in December, 2002.        -->
<!--                                                               -->
<!--             NLM thanks the Harvard University Libraries, both -->
<!--             for proposing that a draft archiving NLM DTD for  -->
<!--             life sciences journals be extended to accommodate -->
<!--             journals in all disciplines and for sponsoring    -->
<!--             Inera Inc.'s collaboration with other DTD         -->
<!--             authors in completing NLM Version 1.0. The        -->
<!--             Andrew W. Mellon Foundation provided support for  -->
<!--             these important contributions.                    -->
<!--                                                               -->
<!--             Suggestions for refinements and enhancements to   -->
<!--             this DTD should be sent in email to:              -->
<!--                 <EMAIL>                         -->
<!-- ============================================================= -->


<!-- ============================================================= -->
<!--                    DTD VERSION/CHANGE HISTORY                 -->
<!-- ============================================================= -->
<!--
Version  Reason/Occasion                   (who) vx.x (yyyy-mm-dd)
   ==============================================================
     JATS Version 1.3 (ANSI/NISO Z39.96-2021)
                                  (DAL/BTU) v1.3 (2021-06-10)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by both the NISO and ANSI memberships 
     to be changed. This version of the NISO JATS was approved 
     by ANSI vote on June 10, 2021, so it supersedes all Committee 
     Drafts as well as the draft version JATS 1.3 2021 (the version
     submitted for vote), becoming NISO JATS v1.3 20210610 
     (ANSI/NISO Z39.96-2019).

     The file names and formal public identifiers have been 
     changed in the modules and the catalogs.

 23. JATS 'v1.3d2 20201130' becomes 'v1.3 20210610' 
     (final ANSI vote date)
     'ANSI/NISO Z39.96-2019' becomes 'ANSI/NISO Z39.96-2021'

   ==============================================================
     JATS Version 1.3d2 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.3d2 (2020-11-30)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by the NISO and ANSI memberships 
     to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     of the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting; it is for Standing Committee review and
     testing. The formal public identifiers have been changed 
     in the modules and the catalogs.
 
 22. JATS "v1.3d2 20201130" becomes 'v1.3 20210610' (second 
     committee draft following the final ANSI/NISO 1.2 vote)

   ==============================================================
     JATS Version 1.3d1 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.3.d1 (2019-08-31)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by the NISO and ANSI memberships 
     to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     of the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
  
 21. JATS "1.2" becomes "v1.3d1 20190831" (first committee
     draft following the final ANSI/NISO vote) 

   ==============================================================
     JATS Version 1.2 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.2     (2019-02-08)
    
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the both the NISO and ANSI membership 
     to be changed. This version of the NISO JATS was approved 
     by ANSI vote on Feb 08, 2019, so it supersedes all Committee 
     Drafts as well as the draft version JATS 1.2 2018 (the version
     submitted for vote), becoming NISO JATS v1.2 20190208 
     (ANSI/NISO Z39.96-2021).
  
 20. JATS "1.2" becomes "v1.2 20190208" (final ANSI vote date)
     "ANSI/NISO Z39.96-2015" becomes "ANSI/NISO Z39.96-2019"
     
   ==============================================================
     JATS Version 1.2 (ANSI/NISO Z39.96-2018)
                                   (DAL/BTU) v1.2     (2018-11-30)
    
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the both the NISO and ANSI membership 
     to be changed. This version of the NISO JATS was approved 
     by ANSI and NISO vote, so it supersedes all Committee Drafts
     and becoming NISO JATS 1.2 (ANSI/NISO Z39.96-2018).
  
 19. JATS "1.2d2" and "v1.2d2 20180401" became
     JATS "1.2" and "v1.2 20181130" 

    =============================================================
     JATS Version 1.2d2              (DAL/BTU) v1.2d2  (2018-04-01)

     JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
     BITS is under continuous maintenance, and is modified at the
     discretion of the working group.
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.

 18. BITS remains "2.0" and "v2.0 20151225"
   
     JATS "1.2d1" and "v1.2d1 20171231" became
     JATS "1.2d2" and "v1.2d2 20180401". 

     No module names were changed.
 
    =============================================================
     JATS Version 1.2d1 (ANSI/NISO Z39.96-2015)
                                   (DAL/BTU) v1.2d1  (2017-12-31)
 
     NISO JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
  
 17. JATS "1.2d1" and "v1.2d1 20170631" became
     JATS "1.2d1" and "v1.2d1 20171231". 

    =============================================================
     JATS Version 1.2d1              (DAL/BTU) v1.2d1  (2017-06-31)

     NISO JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
    
 16. JATS became version "1.2d1" and "v1.2d1 20170631"
 
     =============================================================
     JATS Version 1.1                (DAL/BTU) v1.1  (2015-12-15)

     JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
     JATS 1.1 was approved in late 2015, so the formal public 
     identifiers were changed in the modules and the catalogs.
     No model or attribute changes were made at this time. 
 
 15. JATS became version "1.1" and "v1.1 20151215"
     BITS remained version "2.0" but becomes "v2.0 20151225"
     
     =============================================================
     JATS Version 1.1d3             (DAL/BTU) v1.1d3  (2015-03-01)
   
     The changes in this release are in response to NISO 
     License and Indicators Recommended Practice.

     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the NISO membership to be changed. This
     Committee Draft 1.1d3 will be sent to the NISO voting
     membership, to become (if approved) NISO JATS 1.1.
     
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee.

 14. JATS became version "1.1d3" and "v1.1d3 20150301"

     =============================================================
     JATS Version 1.1d2              (DAL/BTU) v1.1d2 (2014-09-30)
   
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the NISO membership to be changed. This
     Committee Draft 1.1d2 will be sent to the NISO voting
     membership, to become (if approved) NISO JATS 1.1.
     
     This catalog represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee.

 13. TABLE ATTRIBUTES
     - Added @specific-use to the attributes for <table>,
       primarily for use when <table> is used inside <alternatives>.
     - This entailed creating a new PE "additional-table-atts",
       which is defined and called AFTER the full table setup,
       so that XML tools that do not recognize a 2nd ATTLIST for
       an element will lose only the @specific-use. This new PE
       could also be sued to add other attributes to an XHTML-
       inspired <table>.

 12. JATS became version "1.1d2" and "v1.1d2 20140930//EN"
   
     =============================================================
     NISO JATS Version 1.1d1           (DAL/BTU) v1.1 (2013-11-15)
   
     ANSI/NISO Z39.96-2012 (pre-release for V1.0 BITS; Version 1.1d1) 

     NISO JATS Standing Committee met and answered the requests and
     suggestions from the NISO request forms.

     Details concerning ANSI/NISO Z39.96-2012 JATS-based DTDs,  
     XSDs, RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.1d2/

 11. CAPTION ATTRIBUTES - This module has been changed to alter
      the mechanism by which the @style attribute is associated 
      with the element <caption>. 
          Previously, the <caption> element's base attributes  
      were declared in the JATS-display.ent module and additional 
      attributes (in the published XHTML, only @style) for it 
      were declared in the XHTML Table Module. The @style 
      attribute is now declared with all the other <caption>
      attributes in the  JATS-display.ent module, and the caption 
      attributes in the XHTML table model have been set to IGNORE. 
      This was done to prevent duplicate (harmless but annoying)
      attribute warning messages. 
                        
           CAUTION: If your XHTML customization has added attributes
                    by redefining the parameter entity 
                            "Core.extra.attrib"
                    you will need to uncomment the lines in this
                    module that add that parameter entity to the
                    contents of the <caption> attribute list.
                    Otherwise, your new attributes will not be
                    added to <caption>. When you uncomment this
                    line, you will get a duplicative attribute
                    warning message on the @style attribute,
                    which you may ignore.  
                    
 10. GLOBAL ATTRIBUTES - Added the new parameter entity 
         %jats-common-atts;
     to every element in this module. This PE adds (for now) the
     @id attribute and the @xml:base attribute to every element,
     whether metadata or narrative.
     Since the @id in this parameter entity is optional, a second
     parameter entity jats-common-atts-id-required was also added.
     The two are kept in sync with the jats-base-atts parameter 
     entity.
     This added an attribute list to the parameter entity 
     "Common.attrib".
   
     =============================================================
     NISO JATS Version 1.0             (DAL/BTU) v1.0 (2012-xx-xx)
   
     ANSI/NISO Z39.96-2012 (Version 1.0) 

     Details concerning ANSI/NISO Z39.96-2012 JATS-based DTDs,  
     XSDs, RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.3d2/

  5. OASIS NAMESPACE - Added the OASIS table namespace handling,
     which, by default, places a namespace prefix of "oasis" on
     the OASIS CALS Exchange Table Model.
      - Added call to JATS-oasis-namespace.ent (which sets up
          the OASIS namespace URI and the prefix "oasis")
      - Added  %oasis.xmlns.attrib; attribute to <article>
      - Altered the custom classes module to use the new
         namespaced OASIS element names for <table> and <tbody>
         (%otable.qname; and %otbody.qname;)
     NOTE: The XHTML table material was not removed, so both table
     models still work (using <table> and <oasis:table> respectively.
   
  4. Updated the DTD-version attribute to "1.0" and the formal
     public identifier to the date: "v1.0 20120330//EN".

     =============================================================
     Version 0.4                       (DAL/BTU) v0.4 (2011-01-31)
   
     This Tag Set is in the process of becoming a NISO standard.
     The version numbers are starting over from 0.4", as a Trial
     Use Draft, to be made into "Version 1.0" when the Tag Suite 
     becomes a NISO standard. Thus, the version number that would
     have been "NLM Version 3.1 Draft" has become NISO JATS
     "Version 0.4".

     Details on NISO Trial Use Draft Version 0.4 are available at
           http://jats.nlm.nih.gov/JATS-0.4.
   
  3. Updated the DTD-version attribute to "0.4" 
   
  2. Updated the public identifier to "v0.4 20110131", 
     modified the formal public identifier to include "JATS (Z39.96)",
     and the filename as delivered to include "JATS" and the
     new version number "0".

     =============================================================
     Version 3.0                       (DAL/BTU) v3.0 (2007-10-31)

     Version 3.0 is the first non-backward-compatible release.
     In addition to the usual incremental changes, some
     elements and attributes have been renamed and/or remodeled
     to better meet user needs and to increase tag set consistency.
     All module change histories are available through the Tag Suite
     web site at http://dtd.nlm.nih.gov.

     Details on version 3.0 are available at
           http://dtd.nlm.nih.gov/3.0.

  1. Updated public identifier to "v3.0 20071031//EN"              -->

<!-- ============================================================= -->

<!-- Details concerning 
     ANSI/NISO Z39.96-2015 JATS-based DTDs, XSDs, 
     RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.1/                            --> 

<!-- ============================================================= -->
<!--                    SET UP FOR THE XHTML 1.1 TABLE MODULE      -->
<!-- ============================================================= -->


<!-- ============================================================= -->
<!--                    THE XHTML TABLE STYLE ATTRIBUTE MODULE     -->
<!-- ============================================================= -->


<!--                    XHTML TABLE INLINE STYLE MODULE            -->
<!--                    This module declares the 'style' attribute,
                        used to support inline style markup for the
                        <td> and <tr> elements. Copyright 1998-2005
                        W3C (MIT, ERCIM, Keio), All Rights Reserved.
                        Revision: $Id: xhtml-inlstyle-1.mod,v 4.0
                        2001/04/02 22:42:49 altheim Exp $
                        PUBLIC identifier
                        "-//W3C//ENTITIES XHTML Inline Style 1.0//EN"
                        SYSTEM identifier
                "http://www.w3.org/MarkUp/DTD/xhtml-inlstyle-1.mod"-->
%xhtml-inlstyle-1.mod;


<!-- ============================================================= -->
<!--                    DEFAULTS FOR TABLE ELEMENT NAMES           -->
<!-- ============================================================= -->


<!ENTITY % table.qname  "table"                                      >
<!ENTITY % caption.qname
                        "caption"                                    >
<!ENTITY % thead.qname  "thead"                                      >
<!ENTITY % tfoot.qname  "tfoot"                                      >
<!ENTITY % tbody.qname  "tbody"                                      >
<!ENTITY % colgroup.qname
                        "colgroup"                                   >
<!ENTITY % col.qname    "col"                                        >
<!ENTITY % tr.qname     "tr"                                         >
<!ENTITY % th.qname     "th"                                         >
<!ENTITY % td.qname     "td"                                         >


<!-- ============================================================= -->
<!--                    DEFAULTS FOR DATATYPE PARAMETER ENTITIES   -->
<!-- ============================================================= -->


<!ENTITY % Text.datatype
                        "CDATA"                                      >


<!ENTITY % Number.datatype
                        "CDATA"                                      >


<!ENTITY % MultiLength.datatype
                        "CDATA"                                      >


<!ENTITY % Length.datatype
                        "CDATA"                                      >


<!ENTITY % Pixels.datatype
                        "CDATA"                                      >


<!ENTITY % Character.datatype
                        "CDATA"                                      >


<!-- ============================================================= -->
<!--                    DEFAULTS FOR ATTRIBUTE PARAMETER ENTITIES  -->
<!-- ============================================================= -->


<!--                    COMMON ATTRIBUTES (used all over)          -->
<!--         style      Defined in the PE -%Core.extra.attrib; and
                        used to support inline style markup. This
                        attribute is defined in the XHTML Table
                        Inline Style Module called in with the PE:
                           -%xhtml-inlstyle-1.mod
                        which must be invoked before the attribute
                        PE is used. 
                        Warning: The definitions below will lead to
                        the element <caption> having the attributes
                        @id and @content-type defined twice. This is
                        just a validity warning, not an error and 
                        should be ignored.                         -->
<!ENTITY % Common.attrib
            "%jats-common-atts;
             content-type
                        CDATA                             #IMPLIED
             %Core.extra.attrib;"                                    >


<!-- ============================================================= -->
<!--                    OVER-RIDES TO REMOVE CAPTION               -->
<!-- ============================================================= -->


<!--                   CAPTION FOR A TABLE                         -->
<!--                   Modification of the standard XHMTL model
                       Removed the definition of caption, so the
                       element would not be multiply defined       -->
<!ENTITY % caption.element
                       "IGNORE"                                      >


<!ENTITY % caption.attlist  
                       "IGNORE"                                      >


<!--                    CAPTION ATTRIBUTES                         -->
<!--                    Attributes for <caption> element           -->
<!--                    In the lines directly above this declaration,
                        the attribute list for caption, as defined
                        in the XHTML Table Model, has been removed
                        using the IGNORE mechanism. This was done 
                        to prevent duplicate (harmless but annoying)
                        attribute warning messages. 
                        
                        CAUTION: If your XHTML customization has
                        redefined the parameter entity 
                                   "Core.extra.attrib"
                        you will need to uncomment the lines
                        below. You will get a duplicative attribute
                        warning message on the @style attribute,
                        which you may ignore.                      -->
<!--
<!ENTITY % caption-atts
                        "%Core.extra.attrib;                         >
-->
            
            
<!-- ============================================================= -->
<!--                    OVER-RIDES FOR CONTENT PARAMETER ENTITIES  -->
<!-- ============================================================= -->


<!--                   INLINE ELEMENTS                             -->
<!--                   Modification of the standard XHMTL model
                       for inline elements used in the <caption>
                       Set to the null because the <caption>
                       element is now inside the table wrapper,
                       not inside the table, as the original XHTML
                       table intended                              -->
<!ENTITY % Inline.mix  ""                                            >


<!--                   CONTENTS OF A TABLE CELL                    -->
<!--                   Modification of the standard XHMTL model
                       used for the content of tables cells <th>
                       and <td>                                    -->
<!ENTITY % Flow.mix    "%inside-cell;"                               >


<!--                   CONTENTS OF A TABLE                         -->
<!--                   Modification of the standard XHMTL model
                       This has been modified from the XHTML model
                       to remove the <caption> element from the
                       <table> model, since in the JATS DTD Suite 
                       modular library, the <caption> element is part  
                       of the Table Wrapper <table-wrap> element. 
                       No other changes were made to the XHTML table 
                       content model.                              -->
<!ENTITY % table.content
     "( ( %col.qname;* | %colgroup.qname;* ),
        ( ( %thead.qname;?, %tfoot.qname;?, %tbody.qname;+ ) |
          ( %tr.qname;+ )
        )
      )"                                                             >


<!-- ============================================================= -->
<!--                    THE XHTML V1.1 TABLE INVOCATION            -->
<!-- ============================================================= -->


<!--                    XHTML TABLE MODEL                          -->
<!--                    This module declares element types and
                        attributes used to provide table markup
                        similar to HTML 4, including features that
                        enable better accessibility for non-visual
                        user agents. This is the XHTML reformulation
                        of HTML as a modular XML application.
                        Copyright 1998-2005 W3C (MIT, ERCIM, Keio),
                        All Rights Reserved.
                        Revision: $Id: xhtml-table-1.mod,v 4.1
                        2001/04/10 09:42:30 altheim Exp $ SMI
                        PUBLIC identifier
                        "-//W3C//ELEMENTS XHTML Tables 1.0//EN"
                        SYSTEM identifier:
                  "http://www.w3.org/MarkUp/DTD/xhtml-table-1.mod" -->
%xhtml-table-1.mod;

<!-- ============================================================= -->
<!--                    ADDITIONAL ATTRIBUTE(S) FOR TABLE          -->
<!-- ============================================================= -->

<!ENTITY % additional-table-atts
            "specific-use 
                        CDATA                              #IMPLIED" >

<!ATTLIST %table.qname;
            %additional-table-atts;                                  >

<!-- ================== End XHTML Table Setup Module ============= -->

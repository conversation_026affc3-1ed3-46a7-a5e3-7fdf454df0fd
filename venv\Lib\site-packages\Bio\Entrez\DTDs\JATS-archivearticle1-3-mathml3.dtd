<!-- ============================================================= -->
<!--  MODULE:    Journal Archiving and Interchange DTD (MathML 3.0)-->
<!--  VERSION:   ANSI/NISO JATS Version 1.3 (Z39.96-2021)          -->
<!--  DATE:      June 2021                                         -->
<!--                                                               -->
<!-- ============================================================= -->

<!-- ============================================================= -->
<!--                    PUBLIC DOCUMENT TYPE DEFINITION            -->
<!--                        TYPICAL INVOCATION                     -->
<!--
"-//NLM//DTD JATS (Z39.96) Journal Archiving and Interchange DTD with MathML3 v1.3 20210610//EN"
     Delivered as file "JATS-archivearticle1-3-mathml3.dtd"
     Available at:
     http://jats.nlm.nih.gov/archiving/1.3/JATS-archivearticle1-3.dtd
                                                                   -->
<!-- ============================================================= -->


<!-- ============================================================= -->
<!-- SYSTEM:     Journal Archiving and Interchange DTD of the      --> 
<!--             JATS DTD Suite                                    -->         
<!--                                                               -->
<!-- PURPOSE:    DTD to describe a full-text journal article       -->
<!--                                                               -->
<!--             The Journal Archiving and Interchange DTD is an   -->
<!--             application of the ANSI/NISO Z39.96 JATS Journal  -->
<!--             Article Tag Set. It describes journal articles    -->
<!--             and some non-article material such as product     -->
<!--             and book reviews for repository or interchange    -->
<!--             purposes. It describes both the metadata for a    -->
<!--             journal article and the  full content of          -->
<!--             the article.                                      -->
<!--                                                               -->
<!--             This DTD was constructed using the modules in the -->
<!--             JATS DTD Suite, using almost no customization.    -->
<!--                                                               -->
<!-- TAG SET SPONSOR                                               -->
<!--             National Center for Biotechnology                 -->
<!--                Information (NCBI)                             -->
<!--             National Library of Medicine (NLM)                -->
<!--                                                               -->
<!-- CREATED FOR:                                                  -->
<!--             This DTD was created from the JATS DTD Suite.     -->
<!--             Digital archives and publishers may use the       -->
<!--             DTD as is for markup of journal literature or     -->
<!--             related material for archiving and transferring   -->
<!--             such material between archives or create a        -->
<!--             custom XML DTD from the Suite for                 -->
<!--             these purposes.                                   -->
<!--                                                               -->
<!--             This DTD is in the public domain. An organization -->
<!--             that wishes to create its own DTD from the suite  -->
<!--             may do so without permission from NLM.            -->
<!--                                                               -->
<!--             The suite has been set up to be extended using a  -->
<!--             new DTD file and a new DTD-specific customization -->
<!--             module to redefine the many Parameter Entities.   -->
<!--             Do not modify the suite directly or redistribute  -->
<!--             modified versions of the suite.                   -->
<!--                                                               -->
<!--             In the interest of maintaining consistency and    -->
<!--             clarity for potential users, NLM requests:        -->
<!--                                                               -->
<!--             1. If you create a DTD from the JATS DTD Suite    -->
<!--                and intend to stay compatible with the suite,  -->
<!--                then please include the following statement    -->
<!--                as a comment in all of your DTD modules:       -->
<!--                   "Created from, and fully compatible with,   -->
<!--                    the ANSI/NISO Z39.96 Journal Article Tag   -->
<!--                    Suite (JATS)."                             -->
<!--                                                               -->
<!--             2. If you alter one or more modules of the suite, -->
<!--                then please rename your version and all its    -->
<!--                modules to avoid any confusion with the        -->
<!--                original suite. Also, please include the       -->
<!--                following statement as a comment in all your   -->
<!--                DTD modules:                                   -->
<!--                   "Based in part on, but not fully compatible -->
<!--                    with, the ANSI/NISO Z39.96 Journal Article -->
<!--                    Tag Suite (JATS)."                         -->
<!--                                                               -->
<!-- ORIGINAL CREATION DATE:                                       -->
<!--             December 2002                                     -->
<!--                                                               -->
<!-- CREATED BY: Mulberry Technologies, Inc. for the NISO Z39.96   -->
<!--             Working Group. Mulberry Technologies was          -->
<!--             supported by National Center for Biotechnology    -->
<!--             Information (NCBI), a center of the US National   -->
<!--             Library of Medicine (NLM).                        -->
<!--                                                               -->
<!--             The Journal Archiving and Interchange DTD is      -->
<!--             built from the JATS DTD Suite.                    -->
<!--                                                               -->
<!--             This DTD and the Suite are a continuation of      -->
<!--             the work done by NCBI, Mulberry Technologies,     -->
<!--             and Inera Inc. on the NLM Journal Archiving       -->
<!--             and Interchange DTD Suite, which was originally   -->
<!--             released in December, 2002.                       -->
<!--                                                               -->
<!--             NLM thanks the Harvard University Libraries, both -->
<!--             for proposing that a draft archiving NLM DTD for  -->
<!--             life sciences journals be extended to accommodate -->
<!--             journals in all disciplines and for sponsoring    -->
<!--             Inera Inc.'s collaboration with other DTD         -->
<!--             authors in completing NLM Version 1.0. The        -->
<!--             Andrew W. Mellon Foundation provided support for  -->
<!--             these important contributions.                    -->
<!--                                                               -->
<!--             Suggestions for refinements and enhancements to   -->
<!--             this DTD should be sent in email to:              -->
<!--                 <EMAIL>                         -->
<!-- ============================================================= -->


<!-- ============================================================= -->
<!--                    DTD VERSION/CHANGE HISTORY                 -->
<!-- ============================================================= -->
<!--

     =============================================================

Version  Reason/Occasion                   (who) vx.x (yyyy-mm-dd)
   ==============================================================
     JATS Version 1.3 (ANSI/NISO Z39.96-2021)
                                  (DAL/BTU) v1.3 (2021-06-10)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by both the NISO and ANSI memberships 
     to be changed. This version of the NISO JATS was approved 
     by ANSI vote on June 10, 2021, so it supersedes all Committee 
     Drafts as well as the draft version JATS 1.3 2021 (the version
     submitted for vote), becoming NISO JATS v1.3 20210610 
     (ANSI/NISO Z39.96-2019).

     The file names and formal public identifiers have been 
     changed in the modules and the catalogs.

 41. JATS 'v1.3d2 20201130' becomes 'v1.3 20210610' 
     (final ANSI vote date)
     'ANSI/NISO Z39.96-2019' becomes 'ANSI/NISO Z39.96-2021'

  ==============================================================
     JATS Version 1.3d2 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.3d2 (2020-11-30)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by the NISO and ANSI memberships 
     to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     of the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting; it is for Standing Committee review and
     testing. The formal public identifiers have been changed 
     in the modules and the catalogs.
         
 40. PROCESSING METADATA - A new container element was added as
     a child of <article>, <sub-article>, and <response> to 
     describe processing information to help determine the 
     exact tagset flavor used by this document instance. 
     (<processing-meta>)
     Current contents of <processing-meta>:
      - <restricted-by> - Names tighter schemas, subsets, or
           Guidelines followed by this document.
      - <extended-by> - Names extension or superset schemas
           followed by this document, such as TaxPub.
      - <custom-meta-group>
     Several new attributes were added to describe more 
     completely the flavor/version of JATS in which the 
     document was composed:
       - @tagset-family	(JATS | BITS | STS)
       - @base-tagset   (Archiving | Publishing | Authoring)
       - @table-model	(XHTML |OASIS-CALS | both | none)
       - @mathml		(MathML 2.0 | MathML 3.0)
       - @dtd-version was also revised (see below)

 39. DTD VERSION - The <article> attribute @dtd-version has been
     a #FIXED attribute for all of JATS history. This is very
     convenient when creating documents, but problematic for
     archives who receive many versions over time. If a document
     is valid to "v1.3d1"; it is also valid to the latest version
     and to all older versions. @dtd-version will stay a CDATA
     attribute, but will be optional, with a list of values, and
     with no default.
     
 38. ADDED <issue-title-group> added to <front-stub>
     (optional, repeatable)
 
 37. JATS "v1.3d2 20201130" becomes 'v1.3 20210610' (second 
     committee draft following the final ANSI/NISO 1.2 vote)

   ==============================================================
     JATS Version 1.3d1 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.3.d1 (2019-08-31)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by the NISO and ANSI memberships 
     to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     of the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
  
 36. QUESTION AND ANSWER MODULES - Added call to BITS question
     and answer module names question-answer ent
  
 35. JATS "1.2" becomes "v1.3d1 20190831" (first committee
     draft following the final ANSI/NISO vote) 

   ==============================================================
     JATS Version 1.2 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.2     (2019-02-08)
    
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the both the NISO and ANSI membership 
     to be changed. This version of the NISO JATS was approved 
     by ANSI vote on Feb 08, 2019, so it supersedes all Committee 
     Drafts as well as the draft version JATS 1.2 2018 (the version
     submitted for vote), becoming NISO JATS v1.2 20190208 
     (ANSI/NISO Z39.96-2021).
  
 34. JATS "1.2" becomes "v1.2 20190208" (final ANSI vote date)
     "ANSI/NISO Z39.96-2015" becomes "ANSI/NISO Z39.96-2019"

    ==============================================================
     JATS Version 1.2 (ANSI/NISO Z39.96-2018)
                                   (DAL/BTU) v1.2     (2018-11-30)
    
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the both the NISO and ANSI membership 
     to be changed. This version of the NISO JATS was approved 
     by ANSI and NISO vote, so it supersedes all Committee Drafts
     and becoming NISO JATS 1.2 (ANSI/NISO Z39.96-2018).
 
 33. @dtd-version becomes "1.2" from "1.2d2"
  
 32. JATS "1.2d2" and "v1.2d2 20180401" became
     JATS "1.2" and "v1.2 20181130" 

   =============================================================
     JATS Version 1.2d2              (DAL/BTU) v1.2d2  (2018-04-01)

     JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
     BITS is under continuous maintenance, and is modified at the
     discretion of the working group.
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
 
 31. DATE-NOT-AVAILABLE - Inside <article-meta> and <front-stub>,
     a new element <pub-date-not-available> was added as an
     alternatives to <pub-date>. The meaning is that a 
     publication date was (for whatever reason) not available. 
     Presence of the element says nothing about publication 
     status.
 
 30. INLINE INDEX TERMS  - Added invocation to BITS inline
     (embedded) index term model
 
 29. @dtd-version becomes "1.2d2" from "1.2d1"

 28. JATS "1.2d1" and "v1.2d1 20171231" became
     JATS "1.2d2" and "v1.2d2 20180401". 

     No module names were changed. 
     
    =============================================================
     JATS Version 1.2d1 (ANSI/NISO Z39.96-2015)
                                   (DAL/BTU) v1.2d2  (2017-12-31)
 
     NISO JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
  
 27. JATS "1.2d1" and "v1.2d1 20170631" became
     JATS "1.2d1" and "v1.2d1 20171231". 

    =============================================================
     JATS Version 1.2d1              (DAL/BTU) v1.2d1  (2017-06-31)

     NISO JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.

 26. xsi:noNamespaceSchemaLocation added to attribute list of
     <article>. Most modern processors do not need this, but
     a user with an older processor requested it, so the JATS
     Standing Committee acquiesced. This pseudo-attribute
     cannot be used with a DOCTYPE declaration.

 25. FRONT STUB MODEL
     - ARTICLE VERSION - Added new element <article-version>
       inside <front-stub> to hold one version number for the 
       article. <article-version> element may repeat inside
       <article-version-alternatives> to hold version numbers 
       from different systems.
 
     - NO DATE - <pub-date> (optional repeatable) is now
       followed by the optional new element 
       <pub-date-not-available>. 
 
     - PUB-HISTORY - <pub-history> (optional) added to hold
       <event>s.
   
 24. DTD version "1.2d1" from 1.1 

 23. JATS became version "1.2d1" and "v1.2d1 20170631" 

    =============================================================
     JATS Version 1.1                (DAL/BTU) v1.1  (2015-12-15)

     JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
     JATS 1.1 was approved in late 2015, so the formal public 
     identifiers were changed in the modules and the catalogs.
     No model or attribute changes were made at this time. 
 
 22. JATS became version "1.1" and "v1.1 20151215"
     BITS remained version "2.0" but becomes "v2.0 20151225"

     =============================================================
     JATS Version 1.1d3             (DAL/BTU) v1.1d3  (2015-03-01)
   
     The changes in this release are in response to NISO 
     License and Indicators Recommended Practice.

     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the NISO membership to be changed. This
     Committee Draft 1.1d3 will be sent to the NISO voting
     membership, to become (if approved) NISO JATS 1.1.
     
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee.

 21. ALI - Added ALI namespace for NISO Access and Indicator 
     license reference and free to read elements.

 20. JATS became version "1.1d3" and "v1.1d3 20150301"

     =============================================================
     JATS Version 1.1d2              (DAL/BTU) v1.1d2 (2014-09-30)
   
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the NISO membership to be changed. This
     Committee Draft 1.1d2 will be sent to the NISO voting
     membership, to become (if approved) NISO JATS 1.1.
     
     This catalog represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee.

19. <VOLUME> INSIDE <ARTICLE-META> and <FRONT-STUB>
   - Allowed <volume> to repeat inside <article-meta>, the types to 
     be distinguished using @content-type
   - Added new optional element <volume-issue-group> inside 
     <article-meta) following all volume and issue elements
     to hold volume-issue pairs (or n-tuples) when a second
     and subsequent <volume> has its own related issue
     information.

 18. JATS became version "1.1d2" and "v1.1d2 20140930//EN"
   
     =============================================================
     NISO JATS Version 1.1d1           (DAL/BTU) v1.1 (2013-11-15)
   
     ANSI/NISO Z39.96-2012 (pre-release for V1.0 BITS; Version 1.1d1) 

     NISO JATS Standing Committee met and answered the requests and
     suggestions from the NISO request forms.

     Details concerning ANSI/NISO Z39.96-2012 JATS-based DTDs,  
     XSDs, RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.1d2/
    
 17. MATHML 3.0 - Created a new version of the Archiving DTD to
     take MathML 3.0 instead of MathML 2.0.
     Nothing else changed from regular Archiving DTD.
     Call to MathML setup module changed and added call to the
     specific MathML 3.0 modules.
  
 16. DTD Version - Updated the DTD-version attribute to "1.1d1" and 
     the formal public identifier to the date: "v1.1d1 20130915//EN".

 15. GLOBAL ATTRIBUTES - Added the new module for Common (global)
     attributes: %JATS-common-atts.ent;, called in before the
     customizations models module.

 14. GLOBAL ATTRIBUTES - Added the new parameter entity 
         %jats-common-atts;
     to every element in this module. This PE adds (for now) the
     @id attribute and the @xml:base attribute to every element,
     whether metadata or narrative.
     Since the @id in this parameter entity is optional, a second
     parameter entity jats-common-atts-id-required was also added.
     The two are kept in sync with the jats-base-atts parameter 
     entity.
     This added a new attribute list to:
      - back
      - front
      - front-stub
                          
 13. ABSTRACTS AND KEYWORDS
       Changed "abstract*" to "(%abstract.class;)*"
       and "kwd-group*" to "(%kwd-group.class;)*" since those
       classes now exist. Elements should have a limited number of
       ways to be invoked.
        - article-meta (through %article-meta-model;)
        - front-stub (through %front-stub-model;)

     =============================================================
     NISO JATS Version 1.0             (DAL/BTU) v1.0 (2012-xx-xx)
   
     ANSI/NISO Z39.96-2012 (Version 1.0) 

     Details concerning ANSI/NISO Z39.96-2012 JATS-based DTDs,  
     XSDs, RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.3d2/
   
 12. RELATED OBJECT - Added <related-object> everywhere
     <related-article> was used, including inside 
     <front-stub> using front-stub-model.
   
 11. Updated the DTD-version attribute to "1.0" and the formal
     public identifier to the date: "v1.0 20120330//EN".

     =============================================================
     Version 0.4                       (DAL/BTU) v0.4 (2011-01-31)
   
     This Tag Set is in the process of becoming a NISO standard.
     The version numbers are starting over from 0.4", as a Trial
     Use Draft, to be made into "Version 1.0" when the Tag Suite 
     becomes a NISO standard. Thus, the version number that would
     have been "NLM Version 3.1 Draft" has become NISO JATS
     "Version 0.4".

     Details on NISO Trial Use Draft Version 0.4 are available at
           http://jats.nlm.nih.gov/JATS-0.4.
   
 10. Updated the DTD-version attribute to "0.4" 
   
  9. Updated the public identifier to "v0.4 20110131", 
     modified the formal public identifier to include "JATS (Z39.96)",
     and the filename as delivered to include "JATS" and the
     new version number "0".

  8. BODY ATTRIBUTES - Added attribute list and new PE %body-atts;
     to the model of <body>. The only current attribute is
     @specific-use, which indicates, for example, not a real
     tagged XML body, but a 'bag of words" for indexing purposes.

  7. KEYWORD GROUP - Made <kwd-group> repeatable inside
     <front-stub> by modifying %front-stub-model;

  6. I18N - Changed the default for @xml:lang from "en" to
     #IMPLIED, so that the @xml:lang would inherit properly and
     not need to be over-ridden in
      - <sub-article> through %sub-article-atts;
      - <response> through %response-atts;

  5. AFFILIATION ALTERNATIVES - Added the element <aff-alternatives>
     to <front-stub> through %front-stub-model;. This element
     will hold multiple <aff>s that are a representation of a
     single affiliation, for example, the name of an institution
     in two languages or two scripts.

  4. FRONT MATTER - Modified %front-model; to add the elements:
        ack | bio | fn-group | glossary (via %front.class;)
     to front matter content model.

  3. LANGUAGE CODES - Codes for languages as well as variants,
     transliterations, regions, scripts, and combinations
     such as "Jpan"(Han + Hiragana + Katakana). These values should be
     taken from RFC 5646/W3C/IANA Subtag Registry recommendations
     and can be found online at:
       http://www.iana.org/assignments/language-subtag-registry

     =============================================================
     Version 3.0                       (DAL/BTU) v3.0 (2007-10-31)

     Version 3.0 is the first non-backward-compatible release.
     In addition to the usual incremental changes, some
     elements and attributes have been renamed and/or remodeled
     to better meet user needs and to increase tag set consistency.
     All module change histories are available through the Tag Suite
     web site at http://dtd.nlm.nih.gov.

     Details on version 3.0 are available at
           http://dtd.nlm.nih.gov/3.0.

  2. Changed default @xml:lang from "EN" from "en" to match latest
     RFC 4646/W3C/IANA Subtag Registry recommendations

  1. Updated public identifier to "v3.0 20071031//EN"              -->

<!-- ============================================================= -->
<!-- Details concerning 
     ANSI/NISO Z39.96-2015 JATS-based DTDs, XSDs, 
     RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.1/
-->
<!-- ============================================================= -->
<!--                    DESIGN CONSIDERATIONS                      -->
<!-- ============================================================= -->


<!-- MODULAR DTD LIBRARY
                        A set of journal archiving and interchange
                        DTD modules was written as the basis for
                        publishing, interchange, and repository
                        DTDs, with the intention that DTDs for
                        specific purposes, such as this Journal
                        Archiving and Interchange DTD, would be
                        developed based on them.

                        This archiving DTD has been designed to
                        be descriptive, open, and inclusive to
                        allow journal articles to be translated into
                        this format as conveniently as possible
                        from a wide variety of proprietary journal
                        article DTDs (such as Blackwell, Keton, and
                        Ovid).

                        This Archiving DTD has been developed from
                        the JATS DTD Suite modules, in the approved 
                        manner, making changes to the declarations 
                        in those modules by over-riding Parameter 
                        Entity contents by redefining the entities 
                        in the:
                            %archivecustom-classes.ent;
                            %archivecustom-mixes.ent;
                            %archivecustom-models.ent;
                        modules, which are called from this DTD file.
                        No changes to any of the original JATS DTD
                        Suite modules are required in order to 
                        use this DTD.                              -->


<!-- ============================================================= -->
<!--                    MODULES OF MODULES INVOKED                 -->
<!-- ============================================================= -->


<!--                    MODULE TO NAME DTD-SPECIFIC MODULES        -->
<!--                    Names all DTD-specific external modules    -->
<!ENTITY % archivecustom-modules.ent 
                        PUBLIC
"-//NLM//DTD JATS (Z39.96) Journal Archiving DTD-Specific Modules v1.3 20210610//EN" 
"JATS-archivecustom-modules1-3.ent"                                >
%archivecustom-modules.ent;


<!--                    MODULE TO NAME THE MATHML 3.0 MODULES      -->
<!--                    Names all DTD-specific external modules    -->
<!ENTITY % mathml3-modules.ent
                        PUBLIC
"-//NLM//DTD JATS (Z39.96) JATS MathML 3.0 Modules v1.3 20210610//EN"
"JATS-mathml3-modules1-3.ent"                                      >
%mathml3-modules.ent;



<!--                    MODULE TO NAME THE MODULES                 -->
<!--                    Declares all the external modules that are
                        part of the modular JATS DTD Suite library. 
                        Thus it does NOT declare itself; 
                        the DTD-specific module-of-modules; 
                        any of the DTD-specific class,
                        mix, or model over-ride modules; or any 
                        new content modules specific to this DTD.
                        Those are declared in the DTD-specific
                        module of modules.
                           Since this module declares but does not
                        invoke modules, this DTD then invokes any
                        modules it uses by referencing the external
                        Parameter Entities defined in the Module of
                        Modules. To include a set of elements (such
                        as all the lists or the table elements) this
                        module defines the external Parameter Entity
                        for the module(s) that contains the table
                        declarations and the DTD references that
                        entity.                                    -->
<!ENTITY % modules.ent  PUBLIC
"-//NLM//DTD JATS (Z39.96) JATS DTD Suite Module of Modules v1.3 20210610//EN"
"JATS-modules1-3.ent"                                              >
%modules.ent;

<!-- ============================================================= -->
<!--                    NISO ALI NAMESPACE SETUP                   -->
<!-- ============================================================= -->


<!--                    DEFINE ALI NAMESPACE ATTRIBUTE AND PREFIX  -->
<!--                    Names the module defines the NISO Access and 
                        Indicators Exchange Model namespace, prefix, 
                        and pseudo-attribute @xmlns.               -->
%JATS-ali-namespace.ent;
                                                            

<!-- ============================================================= -->
<!--                    SET UP COMMON (ALL ELEMENT) ATTRIBUTES     -->
<!-- ============================================================= -->


<!--                    COMMON ATTRIBUTES MODULE                   -->
<!--                    Set up the common attributes, those used on
                        nearly all elements.
                        Must be called before the custom models
                        module.                                    -->
%JATS-common-atts.ent;


<!-- ============================================================= -->
<!--                    CUSTOMIZATION MODULES INVOKED              -->
<!--                    Note: These modules must be called after   -->
<!--                    all Module of Modules but before any other -->
<!--                    modules. Unlike any other grouping, order  -->
<!--                    of these modules matters.                  -->
<!-- ============================================================= -->


<!--                    DTD-SPECIFIC CLASS CUSTOMIZATIONS MODULE   -->
<!--                    Names the module that holds the DTD-specific
                        class definitions for the Journal Archiving
                        and Interchange DTD that over-rides the Suite
                        defaults.
                        (Defined in %archivecustom-modules.ent; )  -->
%archivecustom-classes.ent;


<!--                    DEFAULT ELEMENT CLASSES MODULE             -->
<!--                    Names the module that holds the standard
                        class definitions for the JATS DTD Suite.  -->
%default-classes.ent;


<!--                    DTD-SPECIFIC MIX CUSTOMIZATIONS MODULE     -->
<!--                    Set up the Parameter Entities and element
                        class definitions that will be used to
                        over-ride some element mixes in this DTD.
                        (Defined in %archivecustom-modules.ent; )  -->
%archivecustom-mixes.ent;


<!--                    DEFAULT MIX CUSTOMIZATIONS MODULE          -->
<!--                    Names the module that holds the standard
                        mix definitions for the JATS DTD Suite.    -->
%default-mixes.ent;


<!--                    DTD-SPECIFIC MODELS/ATTRIBUTES CUSTOMIZATIONS
                        MODULE                                     -->
<!--                    Names the module that holds the over-rides
                        of content models, attribute lists, elements
                        lists to be used in content models, and
                        attribute values. These are DTD-specific.
                        (Defined in %archivecustom-modules.ent; )  -->
%archivecustom-models.ent;


<!-- ============================================================= -->
<!--                    COMMON (SHARED) ELEMENTS MODULE INVOKED    -->
<!-- ============================================================= -->


<!--                    COMMON (SHARED) DECLARATIONS               -->
<!--                    Declarations for elements, attributes,
                        entities, and Notations that are shared by
                        more than one class module. Note: Must be
                        called before any of the class modules.    -->

%common.ent;


<!-- ============================================================= -->
<!--                    JOURNAL ARTICLE CLASS ELEMENTS (alpha)     -->
<!-- ============================================================= -->

<!--                    ARTICLE METADATA ELEMENTS                  -->
%articlemeta.ent;


<!--                    BACK MATTER ELEMENTS                       -->
%backmatter.ent;


<!--                    DISPLAY (GRAPHICAL) ELEMENTS               -->
%display.ent;


<!--                    FORMATTING ELEMENT CLASSES                 -->
<!--                    Elements that change rendition/display.    -->
%format.ent;


<!--                    FUNDING ELEMENTS                           -->
<!--                    Elements that describe the sponsorship or
                        open access                                -->
%funding.ent;


<!--                    JOURNAL METADATA ELEMENTS                  -->
%journalmeta.ent;


<!--                    LINK CLASS ELEMENTS                        -->
%link.ent;


<!--                    LIST CLASS ELEMENTS                        -->
%list.ent;


<!--                    MATH ELEMENTS                              -->
%math.ent;


<!--                    NLM CITATION ELEMENT                       -->
%nlmcitation.ent;


<!--                    PARAGRAPH-LEVEL ELEMENTS                   -->
%para.ent;


<!--                    PHRASE-LEVEL ELEMENTS                      -->
%phrase.ent;


<!--                    BIBLIOGRAPHIC REFERENCE (CITATION)
                        CLASS ELEMENTS                             -->
%references.ent;


<!--                    RELATED OBJECT ELEMENT                     -->
<!--                    Defines the <related-object> element to
                        describe a related object such as a
                        related book or a dataset.                 -->
%related-object.ent;


<!--                    SECTION ELEMENTS                           -->
%section.ent;


<!-- ============================================================= -->
<!--                    THE REST OF THE EXTERNAL MODULES INVOKED   -->
<!-- ============================================================= -->


<!--                    BITS EMBEDDED INDEX ELEMENTS MODULE        -->
<!--                    Element declarations the index elements
                        which are embedded in the article 
                        narrative.                                 -->
%index-term.ent;


<!--                    BITS QUESTION AND ANSWER MODULE            -->
<!--                    Element declarations for BITS questions
                        and answers (not complete tests, but used
                        to put build quizzes and tests.            -->
%question-answer.ent;


<!-- ============================================================= -->
<!--                    THE REST OF THE EXTERNAL MODULES INVOKED   -->
<!-- ============================================================= -->


<!--                    MATHML SETUP MODULE                        -->
<!--                    Invoke the MathML modules                  -->
%mathml3-mathmlsetup.ent;



<!--                    XHTML TABLE SETUP MODULE                   -->
<!--                    Set up the necessary Parameter Entity values
                        and then invoke XHTML (HTML 4.0) table
                        module                                     -->
%XHTMLtablesetup.ent;


<!--                    SPECIAL CHARACTERS DECLARATIONS            -->
<!--                    Standard XML special character entities
                        used in this DTD                           -->
%xmlspecchars.ent;


<!--                    CUSTOM SPECIAL CHARACTERS DECLARATIONS     -->
<!--                    Custom special character entities created
                        specifically for use in this DTD Suite     -->
%chars.ent;


<!--                    NOTATION DECLARATIONS MODULE               -->
%notat.ent;


<!-- ============================================================= -->
<!--                    PARAMETER ENTITIES FOR ATTRIBUTE LISTS     -->
<!-- ============================================================= -->


<!--                    DTD VERSION                                -->
<!--                    What version of this DTD was used to make
                        the document instance under consideration. -->
<!ENTITY % dtd-version
            "dtd-version
                        (3.0 | 0.4 | 1.0 | 1.1d1 | 1.1d2 | 1.1d3 | 
                         1.1 | 1.2d1 | 1.2d2 | 1.2 | 1.3d1 | 1.3d2 |
                         1.3)
                                                          #IMPLIED"  >


<!--                    ARTICLE ATTRIBUTES                         -->
<!--                    Attributes for the top-level element
                        <article>                                  -->
<!ENTITY % article-atts
            "%jats-common-atts;                                       
             article-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           'en'
             %dtd-version;
             %XLINK.xmlns.attrib;
             %MATHML.xmlns.attrib;
             %ali.xmlns.attrib;
             %Schema.xmlns.attrib; 
             %Schema.prefix;:noNamespaceSchemaLocation   
                        CDATA          #IMPLIED"                    >


<!--                    BACK ATTRIBUTES                            -->
<!--                    Attributes for the <back> element          -->
<!ENTITY % back-atts
            "%jats-common-atts;"                                     >


<!--                    BODY ATTRIBUTES                            -->
<!--                    Attributes for the <body> element          -->
<!ENTITY % body-atts
            "%jats-common-atts;                                       
             specific-use
                        CDATA                             #IMPLIED"  >


<!--                    FRONT ATTRIBUTES                           -->
<!--                    Attributes for the <front> element         -->
<!ENTITY % front-atts
            "%jats-common-atts;"                                     >


<!--                    FRONT STUB ATTRIBUTES                      -->
<!--                    Attributes for the <front-stub> element    -->
<!ENTITY % front-stub-atts
            "%jats-common-atts;"                                     >


<!--                    SUB-ARTICLE ATTRIBUTES                     -->
<!--                    Attributes for the <sub-article> element   -->
<!ENTITY % sub-article-atts
            "%jats-common-atts;                                       
             article-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    RESPONSE ATTRIBUTES                        -->
<!--                    Attributes for the <response> element      -->
<!ENTITY % response-atts
            "%jats-common-atts;                                       
             response-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!-- ============================================================= -->
<!--                    JOURNAL ARTICLE ELEMENTS                   -->
<!-- ============================================================= -->


<!--                    ARTICLE MODEL (LIMITED)                    -->
<!--                    Article-like model used for, for example,
                        a short sub-article such as a news brief that
                        is contained in side a journal article.    -->
<!ENTITY % article-short-model
                        "(processing-meta?, (front | front-stub),
                          body?, back?, floats-group?)"              >


<!--                    ARTICLE MODEL WITH SUB-ARTICLE OR RESPONSE -->
<!--                    Typical journal article model, which may
                        contain extended components, such as
                        sub-articles or responses,                 -->
<!ENTITY % article-full-model
                        "(processing-meta?, front, 
                          body?, back?, floats-group?,
                          (sub-article* | response*) )"              >


<!--                    ARTICLE                                    -->
<!--                    The complete content of a journal article.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=article
                                                                   -->
<!ELEMENT  article      %article-full-model;                         >
<!ATTLIST  article
             %article-atts;                                          >


<!-- ============================================================= -->
<!--                    FRONT MATTER ELEMENTS                      -->
<!-- ============================================================= -->


<!--                    FRONT MATTER MODEL                         -->
<!--                    Model for the <front> matter (the header
                        metadata) of a journal article)            -->
<!ENTITY % front-model  "(journal-meta?, article-meta,
                          (%list.class; | %front.class; |
                           %front-back.class;)* )"                   >


<!--                    FRONT MATTER                               -->
<!--                    The metadata concerning an article, such as
                        the name and issue of the journal in which it
                        appears and the name and author(s) of the
                        article.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=front
                                                                   -->
<!ELEMENT  front        %front-model;                                >
<!ATTLIST  front
             %front-atts;                                            >


<!-- ============================================================= -->
<!--                    BODY ELEMENTS                              -->
<!-- ============================================================= -->


<!--                    BODY MODEL                                 -->
<!--                    Content model for the Body (main textual
                        content) of a journal article.             -->
<!ENTITY % body-model   "((%para-level;)*, (%sec-level;)*,
                          sig-block?)"                               >


<!--                    BODY OF THE ARTICLE                        -->
<!--                    The main textual portion of the article that
                        conveys the content.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=body
                                                                   -->
<!ELEMENT  body         %body-model;                                 >
<!ATTLIST  body
             %body-atts;                                             >


<!-- ============================================================= -->
<!--                    BACK MATTER ELEMENTS                       -->
<!-- ============================================================= -->


<!--                    BACK MATTER MODEL                          -->
<!--                    Content model for the Back Matter (ancillary
                        material such as appendices) of a journal
                        article.                                   -->
<!ENTITY % back-model   "(label?, title*,
                          (%doc-back-matter-mix;)* )"                >


<!--                    BACK MATTER                                -->
<!--                    Ancillary or supporting material not included
                        as part of the main textual content of a
                        journal article, for example appendices and
                        acknowledgments.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=back
                                                                   -->
<!ELEMENT  back         %back-model;                                 >
<!ATTLIST  back
             %back-atts;                                             >


<!-- ============================================================= -->
<!--                    SUB-ARTICLE                                -->
<!-- ============================================================= -->


<!--                    SUB-ARTICLE MODEL                          -->
<!--                    Content model for the <sub-article> element
                                                                   -->
<!ENTITY % sub-article-model
                        "(processing-meta?, (front | front-stub), 
                          body?, back?, floats-group?,
                          (sub-article* | response*) )"              >


<!--                    SUB-ARTICLE                                -->
<!--                    An article that is completely contained
                        inside another article. Both the article and
                        the sub-article have their own metadata.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=sub-article
                                                                   -->
<!ELEMENT  sub-article  %sub-article-model;                          >
<!ATTLIST  sub-article
             %sub-article-atts;                                      >


<!--                    FRONT MODEL STUB                           -->
<!--                    Content model for the <front-stub> element,
                        a reduced metadata set for use in
                        sub-articles and responses.                -->
<!ENTITY % front-stub-model
                       "(article-id*, (%article-version.class;)?, 
                         article-categories?,
                         title-group?,
                         (%contrib-group.class; |
                          %aff-alternatives.class; | %x.class;)*,
                         author-notes?, ( (%pub-date.class;)* | 
                           pub-date-not-available?), 
                         volume*, volume-id*, volume-series?,
                         issue*, issue-id*,
                         issue-title*, issue-title-group*, 
                         issue-sponsor*, issue-part?, 
                         volume-issue-group*, isbn*,
                         supplement?,
                         ( ( (fpage, lpage?)?, page-range?) |
                           elocation-id )?,
                         (%address-link.class; | product |
                          supplementary-material)*,
                         history?, pub-history?, permissions?, 
                         self-uri*, (%related-article.class;)*, 
                         (%abstract.class;)*,
                         trans-abstract*, (%kwd-group.class;)*,
                         funding-group*, support-group*,
                         conference*, counts?,
                         custom-meta-group?)"                        >
                         

<!--                    STUB FRONT METADATA                        -->
<!--                    A reduced metadata set for use in
                        sub-articles and responses, which will
                        inherit metadata not defined in the stub
                        from the enclosing article
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=front-stub
                                                                   -->
<!ELEMENT  front-stub   %front-stub-model;                           >
<!ATTLIST  front-stub
             %front-stub-atts;                                       >


<!-- ============================================================= -->
<!--                    RESPONSE ELEMENTS                          -->
<!-- ============================================================= -->


<!--                    RESPONSE                                   -->
<!--                    Reply, response, or commentary concerning the
                        journal article.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=response
                                                                   -->
<!ELEMENT  response     %article-short-model;                        >
<!ATTLIST  response
             %response-atts;                                         >


<!-- ================== End Journal Archiving and Interchange DTD  -->

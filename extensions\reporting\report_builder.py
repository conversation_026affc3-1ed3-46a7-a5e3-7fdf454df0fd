"""
报告构建器
整合HTML生成和PDF导出功能，提供简单的API
"""
import os
import logging
from pathlib import Path
from typing import Dict, Any, Optional, Union, List
from datetime import datetime

from .html_generator import HTMLReportGenerator
from .pdf_exporter import PDFExporter

logger = logging.getLogger(__name__)

class ReportBuilder:
    """
    报告构建器类，整合HTML生成和PDF导出功能
    """
    
    def __init__(
        self,
        output_dir: str = 'reports',
        template_dir: Optional[str] = None,
        static_dir: Optional[str] = None,
        base_url: Optional[str] = None
    ):
        """
        初始化报告构建器
        
        Args:
            output_dir: 输出目录
            template_dir: 模板目录
            static_dir: 静态文件目录
            base_url: 基础URL，用于解析HTML中的相对路径
        """
        self.output_dir = output_dir
        self.template_dir = template_dir or str(Path(__file__).parent.parent.parent / 'templates')
        self.static_dir = static_dir or str(Path(__file__).parent.parent.parent / 'static')
        
        # 确保目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(self.template_dir, exist_ok=True)
        os.makedirs(self.static_dir, exist_ok=True)
        
        # 初始化生成器和导出器
        self.html_generator = HTMLReportGenerator(template_dir=self.template_dir, static_dir=self.static_dir)
        self.pdf_exporter = PDFExporter(base_url=base_url or f'file://{Path.cwd()}/')
        
    def build_report(
        self,
        title: str,
        content_md: str,
        plots: Optional[Dict[str, str]] = None,
        output_filename: Optional[str] = None,
        export_pdf: bool = True,
        **context
    ) -> Dict[str, str]:
        """
        构建报告
        
        Args:
            title: 报告标题
            content_md: Markdown格式的报告内容
            plots: 图表字典，键为图表ID，值为HTML字符串
            output_filename: 输出文件名（不含扩展名）
            export_pdf: 是否导出PDF
            **context: 其他模板变量
            
        Returns:
            Dict[str, str]: 包含生成的文件路径的字典
        """
        try:
            # 生成输出文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            if not output_filename:
                output_filename = f'report_{timestamp}'
            
            # 准备输出路径
            report_dir = Path(self.output_dir) / output_filename
            os.makedirs(report_dir, exist_ok=True)
            
            # 准备上下文
            context.update({
                'title': title,
                'generated_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'output_dir': str(report_dir)
            })
            
            # 生成HTML报告
            html_path = report_dir / f'{output_filename}.html'
            html_content = self.html_generator.generate_report(
                title=title,
                content_md=content_md,
                plots=plots or {},
                output_file=str(html_path),
                **context
            )
            
            result = {'html': str(html_path)}
            
            # 导出PDF
            if export_pdf:
                pdf_path = report_dir / f'{output_filename}.pdf'
                try:
                    self.pdf_exporter.export_from_html(
                        html_content=html_content,
                        output_path=str(pdf_path),
                        stylesheets=[
                            str(Path(self.static_dir) / 'css' / 'pdf.css')
                        ] if os.path.exists(Path(self.static_dir) / 'css' / 'pdf.css') else None
                    )
                    result['pdf'] = str(pdf_path)
                except Exception as e:
                    logger.error(f"导出PDF时出错: {str(e)}", exc_info=True)
                    result['pdf_error'] = str(e)
            
            # 保存图表图片
            if plots:
                result['plots'] = self._save_plot_images(plots, report_dir / 'plots')
            
            return result
            
        except Exception as e:
            logger.error(f"构建报告时出错: {str(e)}", exc_info=True)
            raise
    
    def _save_plot_images(
        self,
        plots: Dict[str, str],
        output_dir: Union[str, Path]
    ) -> Dict[str, str]:
        """
        保存图表图片
        
        Args:
            plots: 图表字典，键为图表ID，值为HTML字符串
            output_dir: 输出目录
            
        Returns:
            Dict[str, str]: 包含保存的图片路径的字典
        """
        try:
            from bs4 import BeautifulSoup
            import base64
            import re
            
            os.makedirs(output_dir, exist_ok=True)
            saved_images = {}
            
            for plot_id, plot_html in plots.items():
                try:
                    # 使用BeautifulSoup解析HTML
                    soup = BeautifulSoup(plot_html, 'html.parser')
                    
                    # 查找图片标签
                    img_tag = soup.find('img')
                    if img_tag and 'src' in img_tag.attrs:
                        # 提取Base64编码的图片数据
                        src = img_tag['src']
                        match = re.match(r'data:image/(\w+);base64,(.+)', src)
                        
                        if match:
                            img_format = match.group(1)
                            img_data = base64.b64decode(match.group(2))
                            
                            # 保存图片
                            img_path = Path(output_dir) / f'{plot_id}.{img_format}'
                            with open(img_path, 'wb') as f:
                                f.write(img_data)
                            
                            saved_images[plot_id] = str(img_path)
                    
                except Exception as e:
                    logger.warning(f"保存图表 {plot_id} 时出错: {str(e)}")
            
            return saved_images
            
        except Exception as e:
            logger.error(f"保存图表图片时出错: {str(e)}", exc_info=True)
            return {}
    
    def create_ebm_report(
        self,
        title: str,
        content_md: str,
        forest_plot_data: Optional[Dict] = None,
        funnel_plot_data: Optional[Dict] = None,
        rob_plot_data: Optional[Dict] = None,
        output_filename: Optional[str] = None,
        **context
    ) -> Dict[str, str]:
        """
        创建EBM报告（包含森林图、漏斗图等）
        
        Args:
            title: 报告标题
            content_md: Markdown格式的报告内容
            forest_plot_data: 森林图数据
            funnel_plot_data: 漏斗图数据
            rob_plot_data: 偏倚风险图数据
            output_filename: 输出文件名（不含扩展名）
            **context: 其他模板变量
            
        Returns:
            Dict[str, str]: 包含生成的文件路径的字典
        """
        from ..visualization import ForestPlot, FunnelPlot, RobPlot
        
        plots = {}
        
        try:
            # 生成森林图
            if forest_plot_data:
                forest_plot = ForestPlot(forest_plot_data)
                plots['forest'] = forest_plot.to_html()
            
            # 生成漏斗图
            if funnel_plot_data:
                funnel_plot = FunnelPlot(funnel_plot_data)
                plots['funnel'] = funnel_plot.to_html()
            
            # 生成偏倚风险图
            if rob_plot_data:
                rob_plot = RobPlot(rob_plot_data)
                plots['rob'] = rob_plot.to_html()
            
            # 构建报告
            return self.build_report(
                title=title,
                content_md=content_md,
                plots=plots,
                output_filename=output_filename,
                **context
            )
            
        except Exception as e:
            logger.error(f"创建EBM报告时出错: {str(e)}", exc_info=True)
            raise

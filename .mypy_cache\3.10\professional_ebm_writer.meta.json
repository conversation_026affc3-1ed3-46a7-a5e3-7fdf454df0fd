{"data_mtime": 1751348968, "dep_lines": [8, 9, 10, 11, 1, 1, 1], "dep_prios": [10, 10, 5, 5, 5, 30, 30], "dependencies": ["logging", "re", "typing", "dataclasses", "builtins", "_frozen_importlib", "abc"], "hash": "a1f994f7ff42d24e77778acd844a332ef304347e", "id": "professional_ebm_writer", "ignore_all": true, "interface_hash": "3780a0ee0febc30d6cf7fb3b8c1e91134513d403", "mtime": 1751348931, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "E:\\xzyxgg\\professional_ebm_writer.py", "plugin_data": null, "size": 26361, "suppressed": [], "version_id": "1.15.0"}
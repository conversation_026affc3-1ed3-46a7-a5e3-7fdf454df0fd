"""
模拟的 LLMManager 类，用于测试
"""
from typing import Optional, Dict, Any, List, Generator, Union
import json
import time

class MockLLMManager:
    def __init__(self):
        self.responses = {}
        self.last_prompt = ""
        self.fail_next_call = False
    
    def generate(self, provider: str, model: str, system_prompt: str, 
                user_prompt: str, temperature: float = 0.3, **kwargs) -> str:
        """模拟 generate 方法"""
        return self.generate_response(provider, model, system_prompt, user_prompt, **kwargs)
    
    def generate_completion(self, provider: str, model: str, prompt: str, **kwargs) -> str:
        """模拟 generate_completion 方法"""
        return self.generate_response(provider, model, "", prompt, **kwargs)
    
    def generate_response(self, provider: str, model: str, system_prompt: str, 
                        user_prompt: str, **kwargs) -> str:
        """
        模拟生成回复
        """
        if self.fail_next_call:
            self.fail_next_call = False
            raise Exception("Simulated API failure")
            
        self.last_prompt = f"{system_prompt}\n\n{user_prompt}"
        
        # 根据提示返回模拟响应
        if "title" in user_prompt.lower() or "标题" in user_prompt:
            return "测试报告标题"
        elif "theme" in user_prompt.lower() or "主题" in user_prompt:
            return json.dumps({"themes": ["主题1", "主题2"]})
        elif "narrative" in user_prompt.lower() or "叙述" in user_prompt:
            return "这是测试的叙述性综合结果。"
        elif "chart" in user_prompt.lower() or "图表" in user_prompt:
            return "```mermaid\npie\n    title 测试图表\n    " + \
                   "\"系列1\" : 42\n    \"系列2\" : 58\n```"
        elif "clinical" in user_prompt.lower() or "临床" in user_prompt:
            return "{\"is_clinical\": true, \"study_type\": \"RCT\"}"
        elif "extract" in user_prompt.lower() or "提取" in user_prompt:
            return "{\"pico\": {\"population\": \"测试人群\", \"intervention\": \"测试干预\", \"comparison\": \"测试对照\", \"outcome\": \"测试结果\"}}"
        else:
            return "这是测试生成的文本响应。"
    
    def generate_streaming_response(self, provider: str, model: str, system_prompt: str, 
                                 user_prompt: str, **kwargs) -> Generator[str, None, None]:
        """模拟流式响应"""
        response = self.generate_response(provider, model, system_prompt, user_prompt, **kwargs)
        yield response
    
    def chat_completion(self, provider: str, model: str, messages: List[Dict[str, str]], **kwargs) -> Dict:
        """模拟 chat_completion 方法"""
        content = self.generate_response(provider, model, 
                                      messages[0]["content"] if messages else "", 
                                      messages[-1]["content"] if len(messages) > 1 else "",
                                      **kwargs)
        return {"choices": [{"message": {"content": content}}]}
    
    def set_fail_next_call(self, fail: bool = True):
        """设置下一次调用是否失败"""
        self.fail_next_call = fail

# 导出模拟类
LLMManager = MockLLMManager

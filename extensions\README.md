# EBM 可视化与报告扩展

本扩展为EBM文献综述系统添加了专业的可视化图表和报告生成功能，包括森林图、漏斗图、偏倚风险图以及交互式HTML/PDF报告导出。

## 功能特性

- **专业图表生成**：
  - 森林图（Forest Plot）
  - 漏斗图（Funnel Plot）
  - 偏倚风险图（Risk of Bias Plot）
  
- **报告生成**：
  - 支持Markdown格式的内容
  - 可嵌入交互式图表
  - 响应式设计，适配不同设备
  
- **导出格式**：
  - HTML（交互式）
  - PDF（可打印）
  - 图片（PNG/JPEG/SVG）

## 快速开始

### 安装依赖

```bash
pip install -r requirements-extras.txt
```

### 使用示例

```python
from extensions.visualization import ForestPlot, FunnelPlot, RobPlot
from extensions.reporting import ReportBuilder

# 准备数据
forest_data = [
    {'study': 'Study A', 'effect': 0.5, 'ci_lower': 0.3, 'ci_upper': 0.7, 'weight': 15.5},
    # 更多研究数据...
]

# 创建森林图
forest_plot = ForestPlot({
    'studies': forest_data,
    'effect_measure': 'SMD',
    'title': '森林图示例'
})

# 生成HTML
html = forest_plot.to_html()

# 或者保存为图片
forest_plot.to_image('forest_plot.png')

# 使用报告构建器创建完整报告
report_builder = ReportBuilder()

report = report_builder.create_ebm_report(
    title="系统评价报告",
    content_md="# 报告内容...",
    forest_plot_data={
        'studies': forest_data,
        'effect_measure': 'SMD',
        'title': '干预效果森林图'
    },
    # 其他图表数据...
)

print(f"报告已生成: {report['html']}")
```

## 模块说明

### visualization 模块

- `base_plot.py`: 基础绘图类，封装了Plotly图表的通用功能
- `forest_plot.py`: 森林图实现
- `funnel_plot.py`: 漏斗图实现
- `rob_plot.py`: 偏倚风险图实现

### reporting 模块

- `html_generator.py`: HTML报告生成器
- `pdf_exporter.py`: PDF导出功能
- `report_builder.py`: 报告构建器，整合HTML生成和PDF导出

### utils 模块

- 提供实用函数，如数据处理、统计计算等

## 示例

运行示例脚本生成包含图表的报告：

```bash
python examples/generate_ebm_report.py
```

## 自定义

### 自定义模板

1. 在`templates/`目录下创建或修改模板文件
2. 使用`HTMLReportGenerator`时指定自定义模板

### 自定义样式

1. 在`static/css/`目录下添加自定义CSS文件
2. 在模板中引用自定义CSS

## 注意事项

- 导出PDF需要安装WeasyPrint和相关系统依赖
- 中文字体支持需要系统安装相应字体
- 图表交互功能在PDF中不可用，建议同时提供HTML版本

## 许可证

MIT

#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试修复后的质量评估功能
"""

import json
import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.getcwd())

def test_quality_assessment():
    """测试质量评估功能"""
    try:
        from ebm_professional_enhancements import ProfessionalEBMProcessor
        
        # 加载测试数据
        with open('output/脓毒血症和血糖_search_results.json', 'r', encoding='utf-8') as f:
            studies = json.load(f)
        
        print(f"加载了 {len(studies)} 项研究")
        
        # 创建处理器并测试质量评估
        processor = ProfessionalEBMProcessor(None)
        quality_summary = processor.generate_quality_summary(studies[:20])  # 测试前20项研究
        
        print("\n=== 质量评估结果 ===")
        print(f"总研究数: {quality_summary.get('total_studies', 0)}")
        print(f"质量分布: {quality_summary.get('quality_distribution', {})}")
        print(f"证据等级分布: {quality_summary.get('evidence_levels', {})}")
        print(f"整体推荐: {quality_summary.get('recommendation', '未知')}")
        print(f"研究类型分布: {quality_summary.get('study_types', {})}")
        
        # 测试经济学评价
        from ebm_professional_enhancements import EconomicEvaluationProcessor
        economic_evaluator = EconomicEvaluationProcessor(None)
        economic_analysis = economic_evaluator.analyze_cost_effectiveness(studies[:10], "openai", "gpt-4")

        print(f"\n=== 经济学评价结果 ===")
        print(f"包含经济学数据的研究数: {economic_analysis.get('economic_studies_count', 0)}")
        if economic_analysis.get('analysis'):
            print("经济学分析已生成")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_quality_assessment()
    if success:
        print("\n✅ 质量评估修复测试通过")
    else:
        print("\n❌ 质量评估修复测试失败")
        sys.exit(1)

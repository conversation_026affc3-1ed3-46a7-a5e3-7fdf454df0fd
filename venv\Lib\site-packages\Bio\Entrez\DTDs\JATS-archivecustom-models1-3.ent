<!-- ============================================================= -->
<!--  MODULE:    Journal Archiving DTD Customize Content and       -->
<!--             Attributes Module                                 -->
<!--  VERSION:   ANSI/NISO JATS Version 1.3 (Z39.96-2021)          -->
<!--  DATE:      June 2021                                         -->
<!--                                                               -->
<!-- ============================================================= -->

<!-- ============================================================= -->
<!--                    PUBLIC DOCUMENT TYPE DEFINITION            -->
<!--                        TYPICAL INVOCATION                     -->
<!--
"-//NLM//DTD JATS (Z39.96) Journal Archiving DTD Customize Content and Attributes Module v1.3 20210610//EN"
Delivered as file "JATS-archivecustom-models1-3.ent"               -->
<!-- ============================================================= -->


<!-- ============================================================= -->
<!-- SYSTEM:     Journal Archiving and Interchange DTD of the      --> 
<!--             JATS DTD Suite                                    -->         
<!--                                                               -->
<!-- PURPOSE:    To declare the Parameter Entities (PEs) used to   -->
<!--             over-ride the content models, parts of content    -->
<!--             models, or attribute lists of the JATS DTD Suite  -->
<!--                                                               -->
<!--             Or-groups within models should use mixes or       -->
<!--             classes rather than name elements directly.       -->
<!--                                                               -->
<!--             Note: Since PEs must be declared before they      -->
<!--             are used, this module must be called before the   -->
<!--             content modules that declare elements.            -->
<!--                                                               -->
<!-- TAG SET SPONSOR                                               -->
<!--             National Center for Biotechnology                 -->
<!--                Information (NCBI)                             -->
<!--             National Library of Medicine (NLM)                -->
<!--                                                               -->
<!-- CREATED FOR:                                                  -->
<!--             This DTD was created from the JATS DTD Suite.     -->
<!--             Digital archives and publishers may use the       -->
<!--             DTD as is for markup of journal literature or     -->
<!--             related material for archiving and transferring   -->
<!--             such material between archives or create a        -->
<!--             custom XML DTD from the Suite for                 -->
<!--             these purposes.                                   -->
<!--                                                               -->
<!--             This DTD is in the public domain. An organization -->
<!--             that wishes to create its own DTD from the suite  -->
<!--             may do so without permission from NLM.            -->
<!--                                                               -->
<!--             The suite has been set up to be extended using a  -->
<!--             new DTD file and a new DTD-specific customization -->
<!--             module to redefine the many Parameter Entities.   -->
<!--             Do not modify the suite directly or redistribute  -->
<!--             modified versions of the suite.                   -->
<!--                                                               -->
<!--             In the interest of maintaining consistency and    -->
<!--             clarity for potential users, NLM requests:        -->
<!--                                                               -->
<!--             1. If you create a DTD from the JATS DTD Suite    -->
<!--                and intend to stay compatible with the suite,  -->
<!--                then please include the following statement    -->
<!--                as a comment in all of your DTD modules:       -->
<!--                   "Created from, and fully compatible with,   -->
<!--                    the ANSI/NISO Z39.96 Journal Article Tag   -->
<!--                    Suite (JATS)."                             -->
<!--                                                               -->
<!--             2. If you alter one or more modules of the suite, -->
<!--                then please rename your version and all its    -->
<!--                modules to avoid any confusion with the        -->
<!--                original suite. Also, please include the       -->
<!--                following statement as a comment in all your   -->
<!--                DTD modules:                                   -->
<!--                   "Based in part on, but not fully compatible -->
<!--                    with, the ANSI/NISO Z39.96 Journal Article -->
<!--                    Tag Suite (JATS)."                         -->
<!--                                                               -->
<!-- ORIGINAL CREATION DATE:                                       -->
<!--             August 2004                                       -->
<!--                                                               -->
<!-- CREATED BY: Mulberry Technologies, Inc. for the NISO Z39.96   -->
<!--             Working Group. Mulberry Technologies was          -->
<!--             supported by National Center for Biotechnology    -->
<!--             Information (NCBI), a center of the US National   -->
<!--             Library of Medicine (NLM).                        -->
<!--                                                               -->
<!--             The Journal Archiving and Interchange DTD is      -->
<!--             built from the JATS DTD Suite.                    -->
<!--                                                               -->
<!--             This DTD and the Suite are a continuation of      -->
<!--             the work done by NCBI, Mulberry Technologies,     -->
<!--             and Inera Inc. on the NLM Journal Archiving       -->
<!--             and Interchange DTD Suite, which was originally   -->
<!--             released in December, 2002.                       -->
<!--                                                               -->
<!--             NLM thanks the Harvard University Libraries, both -->
<!--             for proposing that a draft archiving NLM DTD for  -->
<!--             life sciences journals be extended to accommodate -->
<!--             journals in all disciplines and for sponsoring    -->
<!--             Inera Inc.'s collaboration with other DTD         -->
<!--             authors in completing NLM Version 1.0. The        -->
<!--             Andrew W. Mellon Foundation provided support for  -->
<!--             these important contributions.                    -->
<!--                                                               -->
<!--             Suggestions for refinements and enhancements to   -->
<!--             this DTD should be sent in email to:              -->
<!--                 <EMAIL>                         -->
<!-- ============================================================= -->


<!-- ============================================================= -->
<!--                    DTD VERSION/CHANGE HISTORY                 -->
<!-- ============================================================= -->
<!--

Version  Reason/Occasion                   (who) vx.x (yyyy-mm-dd)
   ==============================================================
     JATS Version 1.3 (ANSI/NISO Z39.96-2021)
                                  (DAL/BTU) v1.3 (2021-06-10)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by both the NISO and ANSI memberships 
     to be changed. This version of the NISO JATS was approved 
     by ANSI vote on June 10, 2021, so it supersedes all Committee 
     Drafts as well as the draft version JATS 1.3 2021 (the version
     submitted for vote), becoming NISO JATS v1.3 20210610 
     (ANSI/NISO Z39.96-2019).

     The file names and formal public identifiers have been 
     changed in the modules and the catalogs.

 53. JATS 'v1.3d2 20201130' becomes 'v1.3 20210610' 
     (final ANSI vote date)
     'ANSI/NISO Z39.96-2019' becomes 'ANSI/NISO Z39.96-2021'

  ==============================================================
     JATS Version 1.3d2 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.3d2 (2020-11-30)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by the NISO and ANSI memberships 
     to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     of the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting; it is for Standing Committee review and
     testing. The formal public identifiers have been changed 
     in the modules and the catalogs.
     
 52. EXPANSION OF FIXED ATTRIBUTES LIST: There are four  
     content-defined attributes which are CDATA values 
     (undetermined) in Archiving, but which are named value 
     lists in Publishing/Authoring:
       - @fn-type 
       - @person-group-type (override is in this module)
       - @pub-id-type
       - @ref-type 
     In order to give users of Publishing and Authoing
     more flexibility to name new values without 
     destroying the benefit of named value lists:
       - A new value “custom” has been added to the named
           value list of each of these four attributes.
       - A new attribute @custom-type has been added to
           each element that takes one of these attributes.
     The new attribute @custom-type was also added to
     Green, so that documents valid to Blue or Pumpkin 
     would also be also valid to Green.
     In this module:
      - The element <article-id> was given the new attribute 
        @custom-type. The value "custom" will be
        documented as a valid article-id type.
       - The element <ref> was given the new attribute 
        @custom-type. The value "custom" will be
        documented as a valid reference type.
       - The element <pub-id> was given the new attribute 
        @custom-type. The value "custom" will be
        documented as a valid publication ID type.
    The Best Practice rule (unenforceable in DTDs, but  
     enforceable in Schematron) is, that if you use the 
     value “custom” from one of these lists, you should 
     also record what type of 'custom' in the 
     @custom-type attribute.    
     
 51. OBJECT ID Added <object-id> to: 
        <disp-formula>, <list>, <ref-list>

 50. SEE ALSO - The four vocabulary attributes were added to
     <see-also>, which is defined in the BITS question-answer
     model.

 49. STRING DATE - Used to be added to models using
     %citation-additions.class, now added through
     %references.class; and the same as Blue. Changed:
       <element-citation>, <mixed-citation>
       <product>, <related-article>, <related-object>

 48. ADDED <issue-title-group> added to <article-meta>
     (optional, repeatable)
 
 47. DISPLAY FORMULA - Held our noses and added <caption> to 
     <display-formula>.

 46. JATS "v1.3d2 20201130" becomes 'v1.3 20210610' (second 
     committee draft following the final ANSI/NISO 1.2 vote)

   ==============================================================
     JATS Version 1.3d1 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.3.d1 (2019-08-31)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by the NISO and ANSI memberships 
     to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     of the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
 
 45. SIZE - All face markup (by face-markup.class) was added to the 
     content model of <size>.
 
 44. JATS "1.2" becomes "v1.3d1 20190831" (first committee
     draft following the final ANSI/NISO vote)

   ==============================================================
     JATS Version 1.2 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.2     (2019-02-08)
    
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the both the NISO and ANSI membership 
     to be changed. This version of the NISO JATS was approved 
     by ANSI vote on Feb 08, 2019, so it supersedes all Committee 
     Drafts as well as the draft version JATS 1.2 2018 (the version
     submitted for vote), becoming NISO JATS v1.2 20190208 
     (ANSI/NISO Z39.96-2021).
  
 43. JATS "1.2" becomes "v1.2 20190208" (final ANSI vote date)
     "ANSI/NISO Z39.96-2015" becomes "ANSI/NISO Z39.96-2019"

   ==============================================================
     JATS Version 1.2 (ANSI/NISO Z39.96-2018)
                                   (DAL/BTU) v1.2     (2018-11-30)
    
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the both the NISO and ANSI membership 
     to be changed. This version of the NISO JATS was approved 
     by ANSI and NISO vote, so it supersedes all Committee Drafts
     and becoming NISO JATS 1.2 (ANSI/NISO Z39.96-2018).
  
 42. JATS "1.2d2" and "v1.2d2 20180401" became
     JATS "1.2" and "v1.2 20181130" 

    =============================================================
     JATS Version 1.2d2              (DAL/BTU) v1.2d2  (2018-04-01)

     JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
     BITS is under continuous maintenance, and is modified at the
     discretion of the working group.
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.

 41. ASSIGNING AUTHORITY - Added @assigning-authority to the
     following elements, so that the type-of-identifier and
     the party-responsible-for-identifier could both be
     recorded. Previously, these values were both assigned to
     the attribute @pub-id-type.
        - <article-id>
        - Note: <pub-id> already had @assigning-authority

 40. NON-MONETARY SUPPORT - Inside <article-meta> added new
     <support-group> to hold both funding and
     non-monetary support descriptions. <support-group> is 
     both a peer to <funding-group> (backward compatibility)
     and contains <funding-group>.
 
 39. PUBLICATION DATE-NOT-AVAILABLE - Inside <article-meta>, 
     <front-stub>, <event>, and <event-desc>
     added new element <pub-date-not-available>, as an 
     alternative to <pub-date>. The meaning is that a 
     publication date was (for whatever reason) not available. 
     Presence of the element says nothing about publication 
     status.
 
 38. DATE IN CITATION - Superscript and subscript added to
     <date-in-citation>.
  
 37. CONFERENCE DATE - Face markup (face-markup.class) 
     added to <conf-date>.

 36. BITS "2.0" and "v2.0 20151225" remains unchanged
   
     JATS "1.2d1" and "v1.2d1 20171231" became
     JATS "1.2d2" and "v1.2d2 20180401". 

     No module names were changed. 
     
    =============================================================
     JATS Version 1.2d1 (ANSI/NISO Z39.96-2015)
                                   (DAL/BTU) v1.2d1  (2017-12-31)
 
     NISO JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
  
 35. JATS "1.2d1" and "v1.2d1 20170631" became
     JATS "1.2d1" and "v1.2d1 20171231". 

    =============================================================
     JATS Version 1.2d1              (DAL/BTU) v1.2d1  (2017-06-31)

     NISO JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
 
 34. X - both <inline-graphic> and <private-char> added to 
     elements allowed in <x>.

 33. INLINE FORMULA - Added accessibility elements <alt-text> and
     <long-desc> to the model of inline formula, by adding the 
     access.class PE.

 32. ARTICLE META MODEL
     - ARTICLE VERSION - Added new element <article-version>
       inside <article-metadata> to hold one version number for  
       the article. <article-version> element may repeat inside
       <article-version-alternatives> to hold version numbers 
       from different systems.
 
     - NO DATE - <pub-date> (optional repeatable) is now
       followed by the optional new element 
       <pub-date-not-available>. 
 
     - PUBLICATION HISTORY - Added new element based on BITS,
       <pub-history>, to hold <event>s as <history> holds
       <date>s. New internal elements: <event>, <event-desc>
       
 31. JATS became version "1.2d1" and "v1.2d1 20170631" 

    =============================================================
     JATS Version 1.1                (DAL/BTU) v1.1  (2015-12-15)

     JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
     JATS 1.1 was approved in late 2015, so the formal public 
     identifiers were changed in the modules and the catalogs.
     No model or attribute changes were made at this time. 
 
 30. JATS became version "1.1" and "v1.1 20151215"
     BITS remained version "2.0" but becomes "v2.0 20151225"

     =============================================================
     JATS Version 1.1d3             (DAL/BTU) v1.1d3  (2015-03-01)
   
     The changes in this release are in response to NISO 
     License and Indicators Recommended Practice.

     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the NISO membership to be changed. This
     Committee Draft 1.1d3 will be sent to the NISO voting
     membership, to become (if approved) NISO JATS 1.1.
     
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee.

 29. JATS became version "1.1d3" and "v1.1d3 20150301"

     =============================================================
     JATS Version 1.1d2              (DAL/BTU) v1.1d2 (2014-09-30)
   
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the NISO membership to be changed. This
     Committee Draft 1.1d2 will be sent to the NISO voting
     membership, to become (if approved) NISO JATS 1.1.
     
     This catalog represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee.
 
 28.  RUBY MODEL CHANGED TO MATCH HTML5
      - changed the Ruby content model to allow only a single
        Ruby annotation on a single Ruby base:
            (rb, (rt | (rp, rt, rp)) )
        The Ruby textual annotation may take a pair of
        Ruby parentheses, but these are optional.

 27. ATTRIBUTES FOR PUB-ID
   - Added the linking attributes (optional version) to <pub-id> , 
     so that a <pub-id> that is a DOI, for example, can carry the 
     linking attributes and be made into a live link. In this use
     of <pub-id>, the element is acting both as an identifier
     and as a link.
   - Added new attribute @assigning-authority to <pub-id>, to
     carry new values such as "CrossRef" and to take some of the
     semantic burden off the '@pub-id-type' attribute, when
     values are organizations such as "Genbank" or "PDB".

 26. <OBJECT-ID> INSIDE <TRANS-ABSTRACT>
     Allowed <object-id> at the beginning of <trans-abstract>, 
     so that abstracts could be given DOIs.

 25. <VOLUME> INSIDE <ARTICLE-META>
   - Allowed <volume> to repeat inside <article-meta>, the types to 
     be distinguished using @content-type
   - Added new optional element <volume-issue-group> inside 
     <article-meta) following all volume and issue elements
     to hold volume-issue pairs (or n-tuples) when a second
     and subsequent <volume> has its own related issue
     numbers.
 
 24. JATS became version "1.1d2" and "v1.1d2 ********//EN"
   
     =============================================================
     NISO JATS Version 1.1d1           (DAL/BTU) v1.1 (2013-11-15)
   
     ANSI/NISO Z39.96-2012 (pre-release for V1.0 BITS; Version 1.1d1) 

     NISO JATS Standing Committee met and answered the requests and
     suggestions from the NISO request forms.

     Details concerning ANSI/NISO Z39.96-2012 JATS-based DTDs,  
     XSDs, RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.3d2/

 23. NEW <era> ELEMENT
     Added <era> through date-parts.class to:
      - <conf-date>
      - <date-in-citation>
      - <pub-date>
      - <string-date>
      - access-date (deprecated
      - time-stamp (deprecated)

 22. INSTITUTIONS TO STANDARDS ORGANIZATIONS
     Added the elements <institution-wrap> to the following elements
     through the institution-wrap.class:
      - <copyright-holder>
      - <std-organization>

 21. RUBY ANNOTATIONS - Overrode the default JATS inclusions, to
     change the more restricted face-markup class to the 
     Archive-specific all Phrase. Elements change:
      - <rb> Ruby Base (through %rb-elements;)

 20. GLOBAL ATTRIBUTES - Added the new parameter entity 
         %jats-common-atts;
     to every element in this module. This PE adds (for now) the
     @id attribute and the @xml:base attribute to every element,
     whether metadata or narrative.
     Since the @id in this parameter entity is optional, a second
     parameter entity jats-common-atts-id-required was also added.
     The two are kept in sync with the jats-base-atts parameter 
     entity.

 19. ABSTRACTS AND KEYWORDS ON NEW STRUCTURES
     - Added <abstract> (through %abstract.class;) and <kwd-group> 
       (through %kwd-group.class;) to the following elements:
        - disp-formula (through %disp-formula-elements;)

     - Changed "abstract*" to "(%abstract.class;)*"
       and "kwd-group*" to "(%kwd-group.class;)*" since those
       classes now exist. Elements should have a limited number of
       ways to be invoked.
        - article-meta (through %article-meta-model;)
   
     =============================================================
     NISO JATS Version 1.0             (DAL/BTU) v1.0 (2012-xx-xx)
   
     ANSI/NISO Z39.96-2012 (Version 1.0) 

     Details concerning ANSI/NISO Z39.96-2012 JATS-based DTDs,  
     XSDs, RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.3d2/

 18. RELATED OBJECT - Added <related-object> everywhere
     <related-article> was used, including inside 
     <article-meta> using article-meta-model.

 17. CONTRIBUTOR MODEL - Added "contrib-id.class" to the 
     <contrib> model, to hold identifiers for people,
     such as publishers-specific IDs,  and ORCIDs.
   
 16. VERSE-LINE ELEMENTS - Added the PE %verse-line-elements to
     this module because the default expanded, and Archiving did
     not need to expand as it already was expanded.
   
 14. DATE ATTRIBUTES - Added attributes to <date> through @date-atts: 
       a) @iso-8601-date - ISO 8601 standard date format
          of the publication date. Format  YYYY-MM-DDThh:mm:ss 
       b) @calendar - Name of the calendar used (@calendar) in
          naming this publication date (such as "Gregorian", 
          "Japanese" (Emperor years), or "Islamic").
       c) Added @publication-format, so that both publication
          dates and history dates can separate the format of the
          publication from the version/lifecycle event. Thus
          both an "article" and a "manuscript" might be received.
   
 13. STANDARDS ORGANIZATION - Added std-organization-elements
     to increase what was allowed in the names of standards bodies.

 12. XREF REF TYPES - #9 below says "XREF ATTRIBUTES - Removed 
     the %xref-atts PE from this module since the regular version 
     (in xlink3.ent(?)) was equally inclusive. But it is NOT. 
     Brought back %xref-atts;
   
 11. ISSN-L ELEMENTS - Added <x> TO <issn-l> via 
     %issn-l-elements;

     =============================================================
     Version 0.4                       (DAL/BTU) v0.4 (2011-01-31)
   
     This Tag Set is in the process of becoming a NISO standard.
     The version numbers are starting over from 0.4", as a Trial
     Use Draft, to be made into "Version 1.0" when the Tag Suite 
     becomes a NISO standard. Thus, the version number that would
     have been "NLM Version 3.1 Draft" has become NISO JATS
     "Version 0.4".

     Details on NISO Trial Use Draft Version 0.4 are available at
           http://jats.nlm.nih.gov/JATS-0.4.
   
 10. Updated the DTD-version attribute to "0.4" 
   
  9. Updated the public identifier to "v0.4 20110131", 
     modified the formal public identifier to include "JATS (Z39.96)",
     and the filename as delivered to include "JATS" and the
     new version number "0".
           http://jats.nlm.nih.gov/0.4.

  7. XREF ATTRIBUTES - Removed the %xref-atts PE from this module
     since the regular version (in xlink3.ent(?)) was equally 
     inclusive. See #12 above.

  6. ISSUE - Allowed to repeat inside <article-meta>

  5. AFFILIATION ALTERNATIVES - Added the element <aff-alternatives>
     to <article-meta> through %article meta-model;. This element
     will hold multiple <aff>s that are a representation of a
     single affiliation, for example, the name of an institution
     in two languages or two scripts.

  4. PERSON-GROUP - Became a mixed-content model, so the parameter
     entity %person-group-model: was changed to
     %person-group-elements;, which will be mixed with #PCDATA
     as defined in references.ent. The PE person-group-model has
     been retained in references.ent for compatibility, but has been
     set to the mixed model using person-group-elements.

  3. @SPECIFIC-USE and @XML:LANG - Added the @specific-use and
     @xml:lang to %display-atts;. Also added these attributes
     to the following over-rides:
      - article-id through article-id-atts (@specific-user only)
      - award-id through award-id-atts (both)
      - date through date-atts (@specific-use only)
      - funding-statement through funding-statement-atts (both)
      - pub-id through pub-id-atts (@specific-use only)
      - trans-title through pub-id-atts (@specific-use only;
         @xml:lang already)
      - xref through xref-atts (both)

  2. STRING-DATE - Added <string-date> back to the following
     elements using %citation-additions.class;. It was accidentally
     deleted during the 3.0 revision of -references.class:
      - product         (%product-elements;)
      - related-article (%related-article-elements;)
      - related-object  (%related-object-elements;)
      - funding-statement through funding-statement-atts (both)

     =============================================================
     Version 3.0                       (DAL/BTU) v3.0 (2007-10-31)

     Version 3.0 is the first non-backward-compatible release.
     In addition to the usual incremental changes, some
     elements and attributes have been renamed and/or remodeled
     to better meet user needs and to increase tag set consistency.
     All module change histories are available through the Tag Suite
     web site at http://dtd.nlm.nih.gov.

     Details on version 3.0 are available at
           http://dtd.nlm.nih.gov/3.0.

  1. Updated public identifier to "v3.0 20071031//EN"              -->

<!-- ============================================================= -->
<!-- Details concerning 
     ANSI/NISO Z39.96-2015 JATS-based DTDs, XSDs, 
     RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.1/                            -->

<!-- ============================================================= -->
<!--                    INLINE MIXES TO OVER-RIDE CONTENT MODELS   -->
<!--                    (ELEMENTS TO BE ADDED TO #PCDATA IN MODELS)-->
<!-- ============================================================= -->


<!--                    ABBREVIATION ELEMENTS                      -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the <abbrev> element                       -->
<!ENTITY % abbrev-elements
                        "| %all-phrase; | %def.class;"               >


<!--                    ACCESS DATE ELEMENTS                       -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the Access Date <access-date> element      -->
<!ENTITY % access-date-elements
                        "| %date-parts.class; | %x.class;"           >


<!--                    AFFILIATION ELEMENTS                       -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the <aff> element                          -->
<!ENTITY % aff-elements "| %address.class; | %all-phrase; |
                           %break.class; | %label.class;"            >


<!--                    ANONYMOUS ELEMENTS                         -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        an <anonymous> element                     -->
<!ENTITY % anonymous-elements
                        "| %all-phrase;"                             >


<!--                    ARTICLE TITLE ELEMENTS                     -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the <article-title> element.               -->
<!ENTITY % article-title-elements
                        "| %all-phrase; | %break.class;"             >


<!--                    CHEMICAL STRUCTURE ELEMENTS                -->
<!--                    Those elements that may mix with the data
                        characters inside a Chemical Structure
                        <chem-struct>                              -->
<!ENTITY % chem-struct-elements
                        "| %access.class; |  %all-phrase; |
                         %break.class; | %id.class; |
                         %label.class; | %list.class; |
                         %simple-display-noalt.class;"               >


<!--                    CITATION ELEMENTS                          -->
<!--                    Content for both types of citation. These
                        elements may be mixed with #PCDATA in the
                        <mixed-citation> element (in which all
                        punctuation and spacing are left intact), and
                        they also constitute the choices that can be
                        used to form the all-element-content of the
                        <element-citation> element (in which
                        punctuation and spacing are removed).
                        Design Note: All inline mixes begin with an
                        OR bar.                                    -->
<!ENTITY % citation-elements
                        "%article-link.class; | %appearance.class; |
                         %emphasis.class;  | %inline-display.class; |
                         %inline-math.class; | %label.class; |
                         %math.class; |  %phrase.class; |
                         %references.class; | %simple-link.class; |
                         %subsup.class; |  %x.class;"                >


<!--                    COLLABORATIVE (GROUP) AUTHOR ELEMENTS      -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the <collab> element. This is essentially
                        %all-phrase; plus contrib-info and break.
                        All-phase is not used because of
                        duplication clashes with the
                        -contrib-info.class;                       -->
<!ENTITY % collab-elements
                        "| %article-link.class; | %address.class; |
                         %appearance.class; | %break.class; |
                         %contrib-group.class; |
                         %contrib-info.class; |
                         %emphasis.class; | %inline-display.class; |
                         %inline-math.class; | %math.class; |
                         %phrase.class; | %subsup.class; | %x.class;">


<!--                    COMPOUND KEYWORD PART ELEMENTS             -->
<!--                    Elements to be mixed with data characters
                        inside the <compound-kwd-part> element     -->
<!ENTITY % compound-kwd-part-elements
                        "| %break.class; | %all-phrase;"             >


<!--                    CONFERENCE DATE ELEMENTS                   -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the <conf-date> element                    -->
<!ENTITY % conf-date-elements
                        "| %date-parts.class; | %face-markup.class; |
                         %x.class;"                                  >


<!--                    COMMENT ELEMENTS                           -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the Comment in a Citation <comment> element.
                        Design Note: All inline mixes begin with an
                        OR bar, but since %simple-phrase; is an
                        inline mix, the OR bar is already there.   -->
<!ENTITY % comment-elements
                        "| %all-phrase;"                             >


<!--                    COPYRIGHT HOLDER ELEMENTS                  -->
<!--                    Elements to be mixed with data characters
                        inside the content model for the
                        <copyright-holder> element.                -->
<!ENTITY % copyright-holder-elements
                        "| %institution-wrap.class; | %subsup.class; | 
                         %x.class;"                                  >


<!--                    COPYRIGHT STATEMENT ELEMENTS               -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the <copyright-statement>                  -->
<!ENTITY % copyright-statement-elements
                        "| %all-phrase;"                             >


<!--                    CORRESPONDENCE INFORMATION ELEMENTS        -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the correspondence information.            -->
<!ENTITY % corresp-elements
                        "| %address.class; | %all-phrase; |
                           %break.class; | %label.class;"            >


<!--                    COUNTRY ELEMENTS                           -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the country element.                       -->
<!ENTITY % country-elements
                        "| %x.class;"                                >


<!--                    DATE IN CITATION ELEMENTS                  -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the Date Inside Citation <date-in-citation>
                        element                                    -->
<!ENTITY % date-in-citation-elements
                        "| %date-parts.class; | %emphasis.class; |
                           %subsup.class; | %x.class;"               >

<!--                    FORMULA, DISPLAY ELEMENTS                  -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the <disp-formula>                         -->
<!ENTITY % disp-formula-elements
                        "| %all-phrase; | %abstract.class; |
                         %access.class; | %break.class; | 
                         %caption.class; |
                         %display-back-matter.class; | %id.class; | 
                         %kwd-group.class; | %subj-group.class; |
                         %label.class; |
                         %simple-display-noalt.class;"               >


<!--                    EMAIL ADDRESS ELEMENTS                     -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the <email> element                        -->
<!ENTITY % email-elements
                        "| %all-phrase;"                             >


<!--                    ET AL ELEMENTS                             -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        an <etal> element                          -->
<!ENTITY % etal-elements
                        "| %all-phrase;"                             >


<!--                    EXTERNAL LINK ELEMENTS                     -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        an <ext-link>                              -->
<!ENTITY % ext-link-elements
                        "| %all-phrase;"                             >


<!--                    FUNDING STATEMENT ELEMENTS                 -->
<!--                    Model for the <funding-statement> element  -->
<!ENTITY % funding-statement-elements
                        "| %all-phrase; | %funding.class;"           >


<!--                    HISTORY ELEMENTS                           -->
<!--                    Elements that may be mixed with data
                        characters inside the model for <history>
                        when <history> is modeled as a mixed content
                        element.                                   -->
<!ENTITY % history-elements
                        "| %all-phrase; | %break.class; |
                         %date.class;"                               >


<!--                    FORMULA, INLINE ELEMENTS                   -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the <inline-formula> element.              -->
<!ENTITY % inline-formula-elements
                        "| %access.class; | %all-phrase;"            >


<!--                    INLINE SUPPLEMENTARY MATERIAL ELEMENTS     -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the <inline-supplementary-material> element-->
<!ENTITY % inline-supplementary-material-elements
                        "| %access.class; | %all-phrase;"            >


<!--                    INSTITUTION NAME ELEMENTS                  -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the <institution> element                  -->
<!ENTITY % institution-elements
                        "| %all-phrase; | %break.class;"             >


<!--                    ISBN ELEMENTS                              -->
<!--                    Elements for use with data characters inside
                        the model for the <isbn> element           -->
<!ENTITY % isbn-elements
                        "| %x.class;"                                >


<!--                    ISSN ELEMENTS                              -->
<!--                    Elements for use with data characters inside
                        the model for the <issn> element           -->
<!ENTITY % issn-elements
                        "| %x.class;"                                >


<!--                    ISSN-L ELEMENTS                            -->
<!--                    Elements for use with data characters inside
                        the model for the <issn-l> element         -->
<!ENTITY % issn-l-elements
                        "| %x.class;"                                >


<!--                    ISSUE TITLE ELEMENTS                       -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the <issue-title> element                  -->
<!ENTITY % issue-title-elements
                        "| %all-phrase;"                             >


<!--                    KEYWORD CONTENT ELEMENTS                   -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        a keyword.                                 -->
<!ENTITY % kwd-elements
                        "| %all-phrase; | %break.class;"             >


<!--                    LABEL ELEMENTS                             -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the <label> element                        -->
<!ENTITY % label-elements
                        "| %all-phrase; | %break.class;"             >


<!--                    LINK ELEMENTS                              -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        linking element such as <xref>, <target>,
                        and <ext-link>                             -->
<!ENTITY % link-elements
                        "| %all-phrase; | %break.class;"             >


<!--                    LONG DESCRIPTION ELEMENTS                  -->
<!--                    Elements to be mixed with data characters
                        inside the <long-desc> element             -->
<!ENTITY % long-desc-elements
                        "| %x.class;"                                >


<!--                    METADATA DATA NAME ELEMENTS                -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the <meta-name> element                    -->
<!ENTITY % meta-name-elements
                        "| %all-phrase;"                             >


<!--                    METADATA DATA VALUE ELEMENTS               -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the <meta-value> element                   -->
<!ENTITY % meta-value-elements
                        "| %all-phrase;"                             >


<!--                    NAMED CONTENT ELEMENTS                     -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the <named-content> element                -->
<!ENTITY % named-content-elements
                        "| %all-phrase; |
                         %block-display-noalt.class; |
                         %block-math.class; | %list.class; |
                         %rest-of-para.class;"                       >


<!--                    PARAGRAPH ELEMENTS                         -->
<!--                    Elements that may be used within a paragraph
                        in a mixed content model with #PCDATA.
                        Design Note: There is a major overlap between
                        this parameter entity and the mix for elements
                        that are at the same level as a paragraph.
                        Inline elements appear only inside a
                        paragraph, but block elements such as quotes
                        and lists may appear either within a
                        paragraph or at the same level as a
                        paragraph. This serves a requirement in a
                        repository DTD, since some incoming material
                        will have restricted such elements to only
                        inside a paragraph, some incoming material
                        will have restricted them to only outside a
                        paragraph and some may allow them in both
                        places. Thus the DTD must allow for them to
                        be in either or both.                      -->
<!ENTITY % p-elements   "| %all-phrase; |
                         %block-display-noalt.class; |
                         %block-math.class; | %citation.class; |
                         %funding.class; | %list.class; |
                         %rest-of-para.class;"                       >


<!--                    PERSON GROUP ELEMENTS                      -->
<!--                    Elements to be mixed with #PCDATA characters
                        within the Person Group element
                        (name class include <string-name> in this
                        tag set.-->
<!ENTITY % person-group-elements
                        "| %name.class; | %person-group-info.class; |
                          %x.class;"                                 >


<!--                    PREFORMATTED TEXT ELEMENTS                 -->
<!--                    Elements that may be used, along with data
                        characters, inside the content model for the
                        <preformat> element, in which whitespace,
                        such as tabs, line feeds, and spaces will
                        be preserved.                              -->
<!ENTITY % preformat-elements
                        "| %access.class; | %all-phrase; |
                         %display-back-matter.class; "               >


<!--                    PRODUCT ELEMENTS                           -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the <product> element
                        (Note: all-phrase was replicated and not
                        used directly because the article-link.class
                        elements are repeated in -references.class
                        and therefore cause duplication.           -->
<!ENTITY % product-elements
                        "| %article-link.class; |
                         %appearance.class; | %break.class; |
                         %emphasis.class;  |
                         %inline-display.class; |
                         %inline-math.class; | %label.class; |
                         %math.class; | %phrase.class; |
                         %price.class; | %references.class; |
                         %simple-link.class; |
                         %subsup.class; | %x.class;"                 >


<!--                    PUBLISHER'S LOCATION ELEMENTS              -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the <publisher-loc> element                -->
<!ENTITY % publisher-loc-elements
                        "| %address.class; | %all-phrase; |
                         %break.class;"                              >


<!--                    RELATED ARTICLE ELEMENTS                   -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the <related-article> element.
                        (Note: all-phrase was replicated and not
                        used directly because the article-link.class
                        elements are repeated in -references.class
                        and therefore cause duplication.)          -->
<!ENTITY % related-article-elements
                        " | %article-link.class; |
                         %appearance.class; | %break.class; |
                         %emphasis.class; | %inline-display.class; |
                         %inline-math.class; | %journal-id.class; |
                         %label.class; | %math.class; |
                         %phrase.class; | %references.class; |
                         %simple-link.class; |
                         %subsup.class; | %x.class;"                 >


<!--                    RELATED OBJECT ELEMENTS                    -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the <related-object> element.
                        (Note: all-phrase was replicated and not
                        used directly because the article-link.class
                        elements are repeated in -references.class
                        and therefore cause duplication.)          -->
<!ENTITY % related-object-elements
                        "| %article-link.class; |
                         %appearance.class; | %break.class; |
                         %emphasis.class;  |
                         %inline-display.class; |
                         %inline-math.class; | %label.class; |
                         %math.class; | %phrase.class; |
                         %references.class; |
                         %simple-link.class; |
                         %subsup.class; | %x.class;"                 >


<!--                    ROLE ELEMENTS                              -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        a <role>
                        Design Note: All inline mixes begin with an
                        OR bar; since %rendition-plus; is an
                        inline mix, the OR bar is already there.   -->
<!ENTITY % role-elements
                        "| %all-phrase;"                             >

<!--                    SELF-URI ELEMENTS                          -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the <self-uri> element                     -->
<!ENTITY % self-uri-elements
                        "| %all-phrase;"                             >



<!--                    SIGNATURE BLOCK ELEMENTS                   -->
<!--                    Elements to be mixed with data characters
                        inside the content model for the
                        <sig-block> element.                       -->
<!ENTITY % sig-block-elements
                        "| %all-phrase; | %break.class; |
                         %contrib.class; |
                         %just-base-display-noalt.class; |
                         %person-group-info.class; | %sig.class;"    >


<!--                    SIGNATURE ELEMENTS                         -->
<!--                    Elements to be mixed with data characters
                        inside the content model for the
                        <sig> element.                             -->
<!ENTITY % sig-elements "| %all-phrase; | %break.class; |
                         %just-base-display-noalt.class;"            >


<!--                    SIZE ELEMENTS                              -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the size element.                          -->
<!ENTITY % size-elements
                        "| %face-markup.class; | %x.class;"          >


<!--                    SOURCE ELEMENTS                            -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the <source> element                       -->
<!ENTITY % source-elements
                        "| %all-phrase; | %break.class;"             >

<!--                    SPEAKER ELEMENTS                           -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        a speaker.                                 -->
<!ENTITY % speaker-elements
                        "| %all-phrase; | %person-name.class;"       >


<!--                    STANDARDS ORGANIZATION ELEMENTS            -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        <std-organization>.                        -->
<!ENTITY % std-organization-elements 
                        "| %all-phrase; | %institution-wrap.class; |
                         %break.class;"                              >


<!--                    STRING DATE ELEMENTS                       -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the <string-date> element                  -->
<!ENTITY % string-date-elements
                        "| %all-phrase; | %date-parts.class;"        >


<!--                    STRING NAME ELEMENTS                       -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the <string-name> element                  -->
<!ENTITY % string-name-elements
                        "| %all-phrase; | %person-name.class;"       >


<!--                    STRUCTURAL TITLE ELEMENTS                  -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the <title> element                        -->
<!ENTITY % struct-title-elements
                        "| %all-phrase; | %break.class; |
                         %citation.class;"                           >


<!--                    STYLED CONTENT ELEMENTS                    -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        the <styled-content> element               -->
<!ENTITY % styled-content-elements
                        "| %all-phrase; |
                         %block-display-noalt.class;|
                         %block-math.class; | %list.class; |
                         %rest-of-para.class;"                       >


<!--                    SUBJECT GROUPING NAME ELEMENTS             -->
<!--                    Elements that may be used, along with data
                        characters inside the content model of the
                        <subject> element                          -->
<!ENTITY % subject-elements
                        "| %all-phrase; | %break.class;"             >


<!--                    DEFINITION LIST: TERM ELEMENTS             -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        a <term>.                                  -->
<!ENTITY % term-elements
                        "| %all-phrase; | %block-math.class; |
                         %chem-struct-wrap.class; |
                         %simple-display-noalt.class;"               >


<!--                    TEXTUAL FORM ELEMENTS                      -->
<!--                    Model for the <textual-form> element.
                        Added <label>                              -->
<!ENTITY % textual-form-elements
                        "| %emphasis.class; |
                         %inline-display-noalt.class; |
                         %label.class; | %math.class; |
                         %phrase-content.class; | %subsup.class;"    >


<!--                    TIME STAMP ELEMENTS                        -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        a <time-stamp>.                            -->
<!ENTITY % time-stamp-elements
                        "| %date-parts.class; | %x.class;"           >


<!--                    TITLE ELEMENTS                             -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        title elements such as <title>, <subtitle>,
                        <trans-title>, etc.                        -->
<!ENTITY % title-elements
                        "| %all-phrase; | %break.class;"             >


<!--                    VERSE-LINE ELEMENTS                        -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        a <verse-line>
                        Design Note: All inline mixes begin with an
                        OR bar, but since %simple-text; is an inline
                        mix, the OR bar is already there.          -->
<!ENTITY % verse-line-elements
                        "%simple-text;"                              >


<!--                    X ELEMENTS                                 -->
<!--                    Elements for use inside the <x> element    -->
<!ENTITY % x-elements   "| %emphasis.class; | 
                           %inline-display-noalt.class; |
                           %phrase-content.class; | %subsup.class; |
                           %x.class;"                                >


<!-- ============================================================= -->
<!--                    RUBY MARKUP                                -->
<!-- ============================================================= -->


<!--                    RUBY BASE ELEMENTS                         -->
<!--                    Those elements that may mix with the data
                        characters inside a Ruby Base <rb> 
                        element.                                   -->
<!ENTITY % rb-elements  "| %all-phrase;"                             >


<!-- ============================================================= -->
<!--                    DUPLICATES NEEDED FOR OVER-RIDES           -->
<!--                    (models unchanged from common.ent but      -->
<!--                     needed below)                             -->
<!-- ============================================================= -->


<!--                    COMMON ATTRIBUTES                          -->
<!--                    These lists of attributes will be added to 
                        nearly every element, in both the document 
                        metadata and the document body, of the entire 
                        Tag Set:
                          - including table elements for both the 
                              XHTML-inspired and OASIS-inspired
                              table model elements
                          - excluding only <mml:math> and 
                              <xi:include> (whose namespaces JATS
                              does not control).
                           Great care should be taken to use these
                        attributes judiciously, not as a shortcut to
                        thinking through where an attribute should be
                        used. Potential use cases include adding
                        @xml:lang or some RDFa attribute to every 
                        elements.                                  -->


<!--                    JATS BASE ATTRIBUTES                       -->
<!--                    Holds all the common attributes except @id.
                        Used to keep the two common attribute lists 
                        in sync. To add a global attribute, modify
                        this list.                                 -->
<!ENTITY % jats-base-atts
            "xml:base   CDATA                             #IMPLIED"  >


<!--                    JATS COMMON ATTRIBUTES                     -->
<!--                    Holds all the common attributes, as defined in
                        the base attribute parameter entity, plus an
                        optional @id.                              -->
<!ENTITY % jats-common-atts
            "id         ID                                #IMPLIED 
             %jats-base-atts;"                                       >


<!--                    JATS COMMON ATTRIBUTES (ID REQUIRED)       -->
<!--                    Holds all the common attributes, as defined in
                        the base attribute parameter entity, plus a 
                        required @id.                              -->
<!ENTITY % jats-common-atts-id-required
            "id         ID                                #REQUIRED
             %jats-base-atts;"                                       >


<!--                    MIGHT LINK XLINK ATTRIBUTES                -->
<!--                    Used for elements which may need to link to
                        external sources or other objects within
                        the document, but may not necessarily act
                        as a link at all.  The attribute
                        "xlink:href" identifies the object to which
                        the link points.                           -->
<!ENTITY % might-link-atts
            "xmlns:xlink CDATA                             #IMPLIED
             xlink:type  (simple)                          #IMPLIED
             xlink:href  CDATA                             #IMPLIED
             xlink:role  CDATA                             #IMPLIED
             xlink:title CDATA                             #IMPLIED
             xlink:show  (embed | new | none | other | replace)
                                                           #IMPLIED
             xlink:actuate
                         (none | onLoad | onRequest | other)
                                                           #IMPLIED
             hreflang    NMTOKEN                           #IMPLIED" >



<!-- ============================================================= -->
<!--                    SECTION OPTIONAL TITLE MODEL               -->
<!-- ============================================================= -->


<!--                    CONTENT MODEL FOR AN UNTITLED SECTION      -->
<!--                    The model for a section-like structure that
                        may or may not have an initial title       -->
<!ENTITY % sec-opt-title-model
                        "(sec-meta?, label?, title?, (%para-level;)*,
                          (%sec-level;)*,
                          (%sec-back-matter-mix;)* )"                >


<!-- ============================================================= -->
<!--                    OVER-RIDES OF CONTENT MODELS (FULL MODELS) -->
<!-- ============================================================= -->


<!--                    ARTICLE METADATA MODEL                     -->
<!--                    Content model for the metadata that is
                        specific to the article.                   -->
<!ENTITY % article-meta-model
                       "(article-id*, (%article-version.class;)?,
                         article-categories?, title-group?,
                         (%contrib-group.class; |
                          %aff-alternatives.class; | %x.class;)*,
                         author-notes?,  ( (%pub-date.class;)* | 
                          pub-date-not-available?), 
                         volume*, volume-id*, volume-series?,
                         issue*, issue-id*, 
                         issue-title*, issue-title-group*, 
                         issue-sponsor*, issue-part?, 
                         volume-issue-group*, isbn*,
                         supplement?,
                         ( ( (fpage, lpage?)?, page-range?) |
                           elocation-id )?,
                         (%address-link.class; | product |
                          supplementary-material)*,
                         history?, pub-history?,
                         permissions?, self-uri*,
                         (%related-article.class;)*, 
                         (%abstract.class;)*, trans-abstract*, 
                         (%kwd-group.class;)*, 
                         funding-group*, support-group*,                      
                         conference*, counts?, 
                         custom-meta-group?)"                        >


<!--                    ADDRESS MODEL                              -->
<!--                    Content model for the <address> element    -->
<!ENTITY % address-model
                        "(%address.class; | %address-link.class; |
                          %label.class; | %x.class;)*"               >


<!--                    APPENDIX MODEL                             -->
<!--                    Content model for the <app> element. The
                        section model already contains parentheses.
                        Made initial <title> optional.             -->
<!ENTITY % app-model    "(%sec-opt-title-model;, permissions?)"      >


<!--                    AUTHOR NOTES MODEL                         -->
<!--                    Content model for an <author-notes> element.
                                                                   -->
<!ENTITY % author-notes-model
                        "(label?, title?,
                          (%corresp.class; | %fn-link.class; |
                           %just-para.class; | %x.class;)+ )"        >


<!--                    CONFERENCE MODEL                           -->
<!--                    Content model for the <conference> element -->
<!ENTITY % conference-model
                        "(%conference.class; | %x.class;)*"          >


<!--                    CONTRIBUTOR GROUP MODEL                    -->
<!--                    Content model for the <title-group> element-->
<!ENTITY % contrib-group-model
                        "(%contrib.class; | %contrib-info.class; |
                          %x.class;)+"                               >


<!--                    CONTRIBUTOR MODEL                          -->
<!--                    Content model for the <contrib> element    -->
<!ENTITY % contrib-model
                        "(%contrib-id.class; | %name.class; |  
                          %degree.class; | %contrib-info.class; |  
                          %x.class;)* "                              >


<!--                    DEFINITION LIST: DEFINITION ITEM MODEL     -->
<!--                    Content model of a <def-item>              -->
<!ENTITY % def-item-model
                        "(label?, term*, (%def.class; | %x.class;)* )"
                                                                     >

<!--                    DEFINITION LIST MODEL                      -->
<!--                    Content model for the <def-list> element   -->
<!ENTITY % def-list-model
                        "(label?, title?, term-head?, def-head?,
                          (%def-item.class; | %x.class;)*, 
                          (%def-list.class;)* )"                     >


<!--                    FOOTNOTE GROUP MODEL                       -->
<!--                    Content model for the <fn-group> element
                        Added an <x> as alternative to <fn>.       -->
<!ENTITY % fn-group-model
                        "(label?, title?,
                          (%fn-link.class; | %x.class;)+ )"          >


<!--                    HISTORY MODEL                              -->
<!--                    Content model for the <history> element    -->
<!ENTITY % history-model
                        "(#PCDATA %history-elements;)*"              >


<!--                    KEYWORD GROUP MODEL                        -->
<!--                    Content model for a <kwd-group> element    -->
<!ENTITY % kwd-group-model
                        "(label?, title?,
                          ((%kwd.class; | %x.class;)+ |
                           (%unstructured-kwd-group.class;)* ) )"    >


<!--                    LIST MODEL                                 -->
<!--                    Content model for the <list> element       -->
<!ENTITY % list-model   "((%id.class;)*, label?, title?,
                          (%list-item.class; | %x.class;)+ )"        >


<!--                    PUBLICATION DATE MODEL                     -->
<!--                    Content model for the element <pub-date>   -->
<!ENTITY % pub-date-model
                        "(%date-parts.class; | %string-date.class; |
                          %x.class;)*"                               >


<!--                    REFERENCE ITEM MODEL                       -->
<!--                    Content model for the <ref> element        -->
<!ENTITY % ref-model    "(label?, (%citation.class; | %note.class; |
                          %x.class;)+ )"                             >


<!--                    REFERENCE LIST MODEL                       -->
<!--                    Content model for the <ref-list> element   -->
<!ENTITY % ref-list-model
                        "((%id.class;)*, label?, title?, 
                          (%para-level; | %ref.class;)*,
                          (%ref-list.class;)* )"                     >
                          
                          
<!--                    RUBY WRAPPER Model                         -->
<!--                    Content model for the <ruby> element       -->
<!ENTITY % ruby-model   "(rb, (rt | (rp, rt, rp)) )"                 >


<!--                    CONTENT MODEL FOR A STRUCTURAL SECTION     -->
<!--                    The model for a Section <sec>              -->
<!ENTITY % sec-model    "(sec-meta?, label?, title?,
                          (%para-level;)*, (%sec-level;)*,
                          (%sec-back-matter-mix;)* )"                >


<!--                    TABLE WRAP FOOTER MODEL                    -->
<!--                    Content model for the <table-wrap-foot>
                        element                                    -->
<!ENTITY % table-wrap-foot-model
                        "(label?, title?,
                          (%just-para.class; |  %fn-group.class; |
                           %fn-link.class; |
                           %display-back-matter.class; |
                           %x.class;)+ )"                            >


<!--                    TRANSLATED ABSTRACT MODEL                  -->
<!--                    Content model for an <trans-abstract> element.
                                                                   -->
<!ENTITY % trans-abstract-model
                        "((%id.class;)*, %sec-opt-title-model;)"     >


<!-- ============================================================= -->
<!--                    OVER-RIDES OF ATTRIBUTE LISTS              -->
<!-- ============================================================= -->


<!--                    ARTICLE IDENTIFIER ATTRIBUTES              -->
<!--                    Attributes for the <article-id> element    -->
<!ENTITY % article-id-atts
            "%jats-common-atts;                                       
             pub-id-type                
                        CDATA                             #IMPLIED
             custom-type
                        CDATA                             #IMPLIED
             assigning-authority                
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED"  >



<!--                    DATE (HISTORICAL) ATTRIBUTES               -->
<!ENTITY % date-atts
            "%jats-common-atts;                                       
             date-type  CDATA                             #IMPLIED
             publication-format
                        CDATA                             #IMPLIED
             iso-8601-date
                        CDATA                             #IMPLIED
             calendar   CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED"  >


<!--                    FUNDING STATEMENT ATTRIBUTES               -->
<!--                    Attributes for the <funding-source> element-->
<!ENTITY % funding-statement-atts
            "%jats-common-atts;                                       
             rid        IDREFS                            #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED
             %might-link-atts;"                                      >


<!--                    INDEX-TERM ATTRIBUTES (BITS override)      -->
<!--                    Attribute list for <index-term> element.
                          The attribute @index-type names the
                        specific indexes in which this term should
                        be used. Contains the name of an index or 
                        a space separated series of index names    -->
<!ENTITY % index-term-atts
           "%jats-common-atts;
             index-type NMTOKENS                          #IMPLIED
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED
             vocab      CDATA                             #IMPLIED
             vocab-identifier
                        CDATA                             #IMPLIED
             vocab-term CDATA                             #IMPLIED
             vocab-term-identifier
                        CDATA                             #IMPLIED"  >


<!--                    SEE-ALSO ATTRIBUTES                        -->
<!--                    Attribute list for <see-also> element.     -->
<!ENTITY % see-also-atts
           "%jats-common-atts;
             rid        IDREFS                            #IMPLIED
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED
             vocab      CDATA                             #IMPLIED
             vocab-identifier
                        CDATA                             #IMPLIED
             vocab-term CDATA                             #IMPLIED
             vocab-term-identifier
                        CDATA                             #IMPLIED"  >
 

<!--                    PUBLICATION IDENTIFIER ATTRIBUTES          -->
<!--                    Attributes for the <pub-id> element        -->
<!ENTITY % pub-id-atts
            "%jats-common-atts;                                       
             pub-id-type
                        CDATA                             #IMPLIED
             assigning-authority                
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             custom-type
                        CDATA                             #IMPLIED
             %might-link-atts;"                                      >


<!--                    X(CROSS) REFERENCE ATTRIBUTES              -->
<!--                    Attribute list for cross references        -->
<!ENTITY % xref-atts
            "%jats-common-atts;                                       
             ref-type   CDATA                             #IMPLIED                                             custom-type
                        CDATA                             #IMPLIED
             alt        CDATA                             #IMPLIED
             rid        IDREFS                            #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!-- ================== End Archiving Content/ATT Over-rides ===== -->

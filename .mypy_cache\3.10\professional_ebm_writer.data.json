{".class": "MypyFile", "_fullname": "professional_ebm_writer", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "ProfessionalEBMWriter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "professional_ebm_writer.ProfessionalEBMWriter", "name": "ProfessionalEBMWriter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "professional_ebm_writer.ProfessionalEBMWriter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "professional_ebm_writer", "mro": ["professional_ebm_writer.ProfessionalEBMWriter", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "llm_manager"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "professional_ebm_writer.ProfessionalEBMWriter.__init__", "name": "__init__", "type": null}}, "_contains_chinese": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "professional_ebm_writer.ProfessionalEBMWriter._contains_chinese", "name": "_contains_chinese", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["professional_ebm_writer.ProfessionalEBMWriter", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_contains_chinese of ProfessionalEBMWriter", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_contains_english": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "text"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "professional_ebm_writer.ProfessionalEBMWriter._contains_english", "name": "_contains_english", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "text"], "arg_types": ["professional_ebm_writer.ProfessionalEBMWriter", "builtins.str"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_contains_english of ProfessionalEBMWriter", "ret_type": "builtins.bool", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_intervention": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "study"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "professional_ebm_writer.ProfessionalEBMWriter._extract_intervention", "name": "_extract_intervention", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "study"], "arg_types": ["professional_ebm_writer.ProfessionalEBMWriter", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_intervention of ProfessionalEBMWriter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_outcomes": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "study"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "professional_ebm_writer.ProfessionalEBMWriter._extract_outcomes", "name": "_extract_outcomes", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "study"], "arg_types": ["professional_ebm_writer.ProfessionalEBMWriter", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_outcomes of ProfessionalEBMWriter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_population": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "study"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "professional_ebm_writer.ProfessionalEBMWriter._extract_population", "name": "_extract_population", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "study"], "arg_types": ["professional_ebm_writer.ProfessionalEBMWriter", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_population of ProfessionalEBMWriter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_sample_size": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "study"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "professional_ebm_writer.ProfessionalEBMWriter._extract_sample_size", "name": "_extract_sample_size", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "study"], "arg_types": ["professional_ebm_writer.ProfessionalEBMWriter", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_sample_size of ProfessionalEBMWriter", "ret_type": "builtins.int", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_study_characteristics": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "studies"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "professional_ebm_writer.ProfessionalEBMWriter._extract_study_characteristics", "name": "_extract_study_characteristics", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "studies"], "arg_types": ["professional_ebm_writer.ProfessionalEBMWriter", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_study_characteristics of ProfessionalEBMWriter", "ret_type": "professional_ebm_writer.StudyCharacteristics", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_extract_study_design": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "study"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "professional_ebm_writer.ProfessionalEBMWriter._extract_study_design", "name": "_extract_study_design", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "study"], "arg_types": ["professional_ebm_writer.ProfessionalEBMWriter", {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_extract_study_design of ProfessionalEBMWriter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_format_study_designs": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "study_designs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "professional_ebm_writer.ProfessionalEBMWriter._format_study_designs", "name": "_format_study_designs", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "study_designs"], "arg_types": ["professional_ebm_writer.ProfessionalEBMWriter", {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_format_study_designs of ProfessionalEBMWriter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_main_intervention": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "characteristics"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "professional_ebm_writer.ProfessionalEBMWriter._get_main_intervention", "name": "_get_main_intervention", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "characteristics"], "arg_types": ["professional_ebm_writer.ProfessionalEBMWriter", "professional_ebm_writer.StudyCharacteristics"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_main_intervention of ProfessionalEBMWriter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_main_outcome": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "characteristics"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "professional_ebm_writer.ProfessionalEBMWriter._get_main_outcome", "name": "_get_main_outcome", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "characteristics"], "arg_types": ["professional_ebm_writer.ProfessionalEBMWriter", "professional_ebm_writer.StudyCharacteristics"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_main_outcome of ProfessionalEBMWriter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_main_population": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "characteristics"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "professional_ebm_writer.ProfessionalEBMWriter._get_main_population", "name": "_get_main_population", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "characteristics"], "arg_types": ["professional_ebm_writer.ProfessionalEBMWriter", "professional_ebm_writer.StudyCharacteristics"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_main_population of ProfessionalEBMWriter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_get_primary_study_types": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "study_designs"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "professional_ebm_writer.ProfessionalEBMWriter._get_primary_study_types", "name": "_get_primary_study_types", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "study_designs"], "arg_types": ["professional_ebm_writer.ProfessionalEBMWriter", {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_get_primary_study_types of ProfessionalEBMWriter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_regenerate_clean_content": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "prompt", "provider", "model", "is_chinese"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "professional_ebm_writer.ProfessionalEBMWriter._regenerate_clean_content", "name": "_regenerate_clean_content", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0], "arg_names": ["self", "prompt", "provider", "model", "is_chinese"], "arg_types": ["professional_ebm_writer.ProfessionalEBMWriter", "builtins.str", "builtins.str", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_regenerate_clean_content of ProfessionalEBMWriter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_introduction": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "topic", "studies", "provider", "model", "is_chinese"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "professional_ebm_writer.ProfessionalEBMWriter.generate_introduction", "name": "generate_introduction", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "topic", "studies", "provider", "model", "is_chinese"], "arg_types": ["professional_ebm_writer.ProfessionalEBMWriter", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_introduction of ProfessionalEBMWriter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_methods_section": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "topic", "studies", "provider", "model", "is_chinese"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "professional_ebm_writer.ProfessionalEBMWriter.generate_methods_section", "name": "generate_methods_section", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "topic", "studies", "provider", "model", "is_chinese"], "arg_types": ["professional_ebm_writer.ProfessionalEBMWriter", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_methods_section of ProfessionalEBMWriter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_professional_title": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "topic", "studies", "is_chinese"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "professional_ebm_writer.ProfessionalEBMWriter.generate_professional_title", "name": "generate_professional_title", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 1], "arg_names": ["self", "topic", "studies", "is_chinese"], "arg_types": ["professional_ebm_writer.ProfessionalEBMWriter", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_professional_title of ProfessionalEBMWriter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "generate_structured_abstract": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "topic", "studies", "provider", "model", "is_chinese"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "professional_ebm_writer.ProfessionalEBMWriter.generate_structured_abstract", "name": "generate_structured_abstract", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 1], "arg_names": ["self", "topic", "studies", "provider", "model", "is_chinese"], "arg_types": ["professional_ebm_writer.ProfessionalEBMWriter", "builtins.str", {".class": "Instance", "args": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", "builtins.str", "builtins.bool"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "generate_structured_abstract of ProfessionalEBMWriter", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "llm": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "professional_ebm_writer.ProfessionalEBMWriter.llm", "name": "llm", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "study_cache": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "professional_ebm_writer.ProfessionalEBMWriter.study_cache", "name": "study_cache", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "professional_ebm_writer.ProfessionalEBMWriter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "professional_ebm_writer.ProfessionalEBMWriter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "StudyCharacteristics": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "professional_ebm_writer.StudyCharacteristics", "name": "StudyCharacteristics", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "professional_ebm_writer.StudyCharacteristics", "has_param_spec_type": false, "metaclass_type": null, "metadata": {"dataclass": {"attributes": [{"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 19, "name": "total_studies", "type": "builtins.int"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 20, "name": "study_designs", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 21, "name": "populations", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 22, "name": "interventions", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 23, "name": "outcomes", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 24, "name": "years_range", "type": "builtins.str"}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 25, "name": "sample_sizes", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}, {"alias": null, "column": 4, "has_default": false, "is_in_init": true, "is_init_var": false, "is_neither_frozen_nor_nonfrozen": false, "kw_only": false, "line": 26, "name": "quality_distribution", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}], "frozen": false}, "dataclass_tag": {}}, "module_name": "professional_ebm_writer", "mro": ["professional_ebm_writer.StudyCharacteristics", "builtins.object"], "names": {".class": "SymbolTable", "__dataclass_fields__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_classvar", "is_ready"], "fullname": "professional_ebm_writer.StudyCharacteristics.__dataclass_fields__", "name": "__dataclass_fields__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "dataclasses.Field"}], "extra_attrs": null, "type_ref": "builtins.dict"}}, "plugin_generated": true}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "total_studies", "study_designs", "populations", "interventions", "outcomes", "years_range", "sample_sizes", "quality_distribution"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "professional_ebm_writer.StudyCharacteristics.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0, 0, 0, 0, 0, 0, 0], "arg_names": ["self", "total_studies", "study_designs", "populations", "interventions", "outcomes", "years_range", "sample_sizes", "quality_distribution"], "arg_types": ["professional_ebm_writer.StudyCharacteristics", "builtins.int", {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of StudyCharacteristics", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "plugin_generated": true}, "__match_args__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_ready", "allow_incompatible_override"], "fullname": "professional_ebm_writer.StudyCharacteristics.__match_args__", "name": "__match_args__", "type": {".class": "TupleType", "implicit": false, "items": [{".class": "LiteralType", "fallback": "builtins.str", "value": "total_studies"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "study_designs"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "populations"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "interventions"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "outcomes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "years_range"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "sample_sizes"}, {".class": "LiteralType", "fallback": "builtins.str", "value": "quality_distribution"}], "partial_fallback": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.tuple"}}}, "plugin_generated": true}, "__mypy-replace": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Decorator", "func": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["total_studies", "study_designs", "populations", "interventions", "outcomes", "years_range", "sample_sizes", "quality_distribution"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_static", "is_decorated"], "fullname": "professional_ebm_writer.StudyCharacteristics.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["total_studies", "study_designs", "populations", "interventions", "outcomes", "years_range", "sample_sizes", "quality_distribution"], "arg_types": ["builtins.int", {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of StudyCharacteristics", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}, "is_overload": false, "var": {".class": "Var", "flags": ["is_staticmethod", "is_ready"], "fullname": "professional_ebm_writer.StudyCharacteristics.__mypy-replace", "name": "__mypy-replace", "type": {".class": "CallableType", "arg_kinds": [5, 5, 5, 5, 5, 5, 5, 5], "arg_names": ["total_studies", "study_designs", "populations", "interventions", "outcomes", "years_range", "sample_sizes", "quality_distribution"], "arg_types": ["builtins.int", {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "builtins.str", {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__mypy-replace of StudyCharacteristics", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "plugin_generated": true}, "interventions": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "professional_ebm_writer.StudyCharacteristics.interventions", "name": "interventions", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "outcomes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "professional_ebm_writer.StudyCharacteristics.outcomes", "name": "outcomes", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "populations": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "professional_ebm_writer.StudyCharacteristics.populations", "name": "populations", "type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}}}, "quality_distribution": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "professional_ebm_writer.StudyCharacteristics.quality_distribution", "name": "quality_distribution", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "sample_sizes": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "professional_ebm_writer.StudyCharacteristics.sample_sizes", "name": "sample_sizes", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "study_designs": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "professional_ebm_writer.StudyCharacteristics.study_designs", "name": "study_designs", "type": {".class": "Instance", "args": ["builtins.str", "builtins.int"], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "total_studies": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "professional_ebm_writer.StudyCharacteristics.total_studies", "name": "total_studies", "type": "builtins.int"}}, "years_range": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_initialized_in_class", "is_ready"], "fullname": "professional_ebm_writer.StudyCharacteristics.years_range", "name": "years_range", "type": "builtins.str"}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "professional_ebm_writer.StudyCharacteristics.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "professional_ebm_writer.StudyCharacteristics", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "professional_ebm_writer.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "professional_ebm_writer.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "professional_ebm_writer.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "professional_ebm_writer.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "professional_ebm_writer.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "professional_ebm_writer.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "dataclass": {".class": "SymbolTableNode", "cross_ref": "dataclasses.dataclass", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "professional_ebm_writer.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "re": {".class": "SymbolTableNode", "cross_ref": "re", "kind": "Gdef"}}, "path": "E:\\xzyxgg\\professional_ebm_writer.py"}
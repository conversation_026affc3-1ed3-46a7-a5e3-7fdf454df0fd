# EBM循证医学内容优化使用指南

## 概述

本指南介绍如何使用新开发的EBM内容优化系统，该系统专门针对小模型2k上下文限制和真实文献数据的问题进行了优化，确保生成的循证医学报告符合专业标准。

## 主要特性

### 1. 小模型上下文优化
- **分块处理策略**：将复杂的EBM报告生成任务分解为多个小块，每个块都在2k上下文限制内
- **优化提示词**：专门设计的简洁提示词，确保在有限上下文中获得最佳结果
- **渐进式内容构建**：逐步构建报告各个部分，避免上下文溢出

### 2. 真实数据驱动
- **数据验证和清理**：确保所有输入的研究数据都经过验证和清理
- **统计信息提取**：从真实研究数据中提取准确的统计信息
- **避免模拟数据**：所有生成的内容都基于真实的文献搜索结果

### 3. 专业EBM标准
- **PRISMA 2020合规**：严格遵循PRISMA 2020指南要求
- **结构化摘要**：生成包含背景、目的、方法、结果、结论的结构化摘要
- **质量评估**：集成ROB工具和GRADE系统进行质量评估

## 核心组件

### 1. EBMContentOptimizer
主要的内容优化器，负责：
- 验证和清理研究数据
- 生成真实数据统计
- 分块生成各个专业章节
- 质量评估和合规性检查

### 2. 优化提示词库
- `ebm_prompts_optimized.py`：包含针对小模型优化的提示词
- 中英文双语支持
- 每个提示词都经过精心设计，确保在上下文限制内

### 3. 集成指南
- `ebm_integration_guide.py`：展示如何集成到现有系统
- `EnhancedEBMGenerator`：增强的EBM生成器类

## 使用方法

### 基本使用

```python
from ebm_generator import EBMGenerator
from llm_manager import LLMManager

# 初始化
llm_manager = LLMManager()
ebm_generator = EBMGenerator(llm_manager)

# 生成优化的EBM报告
result = ebm_generator.generate_optimized_ebm_report(
    topic="阿司匹林预防心血管疾病",
    articles_for_report=studies,  # 真实的研究数据列表
    provider="openai",
    model="gpt-3.5-turbo",
    is_chinese=True
)

# 检查结果
if result['success']:
    print(f"报告生成成功！")
    print(f"质量评分：{result['quality_score']:.2f}")
    print(f"总字数：{result['word_count']:,}")
    print(f"Markdown文件：{result['markdown_file']}")
    print(f"HTML文件：{result['html_file']}")
else:
    print(f"生成失败：{result['error']}")
```

### 高级使用

```python
from ebm_content_optimizer import EBMContentOptimizer

# 直接使用内容优化器
optimizer = EBMContentOptimizer(llm_manager)

# 优化报告结构
optimized_result = optimizer.optimize_ebm_report_structure(
    topic="研究主题",
    studies=validated_studies,
    provider="openai",
    model="gpt-3.5-turbo",
    is_chinese=True
)

# 获取各个章节
sections = optimized_result['sections']
statistics = optimized_result['statistics']
quality_metrics = optimized_result['quality_metrics']
compliance_check = optimized_result['compliance_check']
```

## 生成的报告结构

### 1. 标题
- 基于真实研究数据生成
- 包含PICO要素
- 符合学术标准

### 2. 结构化摘要
- **背景**：临床问题重要性和现有治疗局限性
- **目的**：使用PICO框架的研究目标
- **方法**：检索策略、质量评估、数据分析方法
- **结果**：主要发现和统计结果
- **结论**：基于证据的临床建议

### 3. 引言
- 临床背景和流行病学负担
- 理论依据和知识空白
- 研究目标和假设

### 4. 方法
- 研究方案和注册
- 纳入排除标准（PICOS框架）
- 文献检索策略
- 质量评价方法
- 数据合成和分析

### 5. 结果
- 研究筛选流程（PRISMA图）
- 纳入研究特征
- 偏倚风险评估
- 主要和次要结果
- 敏感性分析

### 6. 讨论
- 主要发现总结
- 与现有证据比较
- 优势与局限性
- 临床意义
- 研究意义

### 7. 结论
- 证据总结
- 临床建议
- 未来研究方向

### 8. 补充材料
- 详细检索策略
- 研究特征表
- 质量评估详情
- GRADE证据概要表

## 质量保证

### 1. 数据验证
- 研究数据完整性检查
- 关键字段验证
- 临床信息提取

### 2. 质量指标
- 整体质量评分
- 基于真实数据比例
- 章节完整度
- 字数统计

### 3. PRISMA合规性检查
- 标题充分性
- 结构化摘要
- 方法详细性
- 结果全面性
- 讨论平衡性
- 结论基于证据
- 补充材料完整性

## 优化特性

### 1. 针对小模型的优化
- 提示词长度控制在2k以内
- 分块处理复杂任务
- 渐进式内容构建
- 上下文高效利用

### 2. 真实数据保证
- 严格的数据验证流程
- 基于实际研究统计
- 避免虚假或模拟数据
- 透明的数据来源

### 3. 专业标准遵循
- PRISMA 2020指南
- Cochrane系统评价标准
- GRADE证据评价系统
- 学术写作规范

## 故障排除

### 常见问题

1. **上下文溢出**
   - 检查输入数据大小
   - 使用分块处理
   - 减少单次处理的研究数量

2. **数据质量问题**
   - 验证研究数据完整性
   - 检查必需字段
   - 清理无效数据

3. **生成质量不佳**
   - 检查提示词设置
   - 验证模型参数
   - 增加重试次数

### 性能优化建议

1. **批量处理**：对大量研究进行批量处理
2. **缓存机制**：利用缓存避免重复计算
3. **并行处理**：在可能的情况下使用并行处理
4. **资源监控**：监控内存和计算资源使用

## 更新日志

### v1.0.0 (2024-12-19)
- 初始版本发布
- 支持小模型2k上下文限制
- 真实数据驱动的内容生成
- PRISMA 2020合规性检查
- 中英文双语支持

## 技术支持

如有问题或建议，请参考：
1. 代码注释和文档字符串
2. 示例代码和使用案例
3. 错误日志和调试信息

---

*本指南基于最新的循证医学标准和小模型优化技术编写，确保生成的报告既专业又实用。*

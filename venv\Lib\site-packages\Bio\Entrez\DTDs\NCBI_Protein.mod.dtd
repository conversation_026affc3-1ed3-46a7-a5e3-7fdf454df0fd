<!-- ============================================
     ::DATATOOL:: Generated from "seqfeat.asn"
     ::DATATOOL:: by application DATATOOL version 1.9.0
     ::DATATOOL:: on 04/10/2008 16:04:22
     ============================================ -->

<!-- ============================================ -->
<!-- This section is mapped from module "NCBI-Protein"
================================================= -->

<!--
**********************************************************************

  NCBI Protein
  by <PERSON>, 1990
  version 0.8

**********************************************************************
-->

<!-- Elements used by other modules:
          Prot-ref -->

<!-- Elements referenced from other modules:
          Dbtag FROM NCBI-General -->
<!-- ============================================ -->

<!--
*** Prot-ref ***********************************************
*
*  Reference to a protein name
*
-->
<!ELEMENT Prot-ref (
        Prot-ref_name?, 
        Prot-ref_desc?, 
        Prot-ref_ec?, 
        Prot-ref_activity?, 
        Prot-ref_db?, 
        Prot-ref_processed?)>

<!-- protein name -->
<!ELEMENT Prot-ref_name (Prot-ref_name_E*)>


<!ELEMENT Prot-ref_name_E (#PCDATA)>

<!-- description (instead of name) -->
<!ELEMENT Prot-ref_desc (#PCDATA)>

<!-- E.C. number(s) -->
<!ELEMENT Prot-ref_ec (Prot-ref_ec_E*)>


<!ELEMENT Prot-ref_ec_E (#PCDATA)>

<!-- activities -->
<!ELEMENT Prot-ref_activity (Prot-ref_activity_E*)>


<!ELEMENT Prot-ref_activity_E (#PCDATA)>

<!-- ids in other dbases -->
<!ELEMENT Prot-ref_db (Dbtag*)>
<!-- processing status -->
<!ELEMENT Prot-ref_processed %ENUM;>
<!ATTLIST Prot-ref_processed value (
        not-set |
        preprotein |
        mature |
        signal-peptide |
        transit-peptide
        ) #REQUIRED >



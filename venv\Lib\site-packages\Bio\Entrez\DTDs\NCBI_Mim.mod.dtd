<!-- ============================================
     ::DATATOOL:: Generated from "mim.asn"
     ::DATATOOL:: by application DATATOOL version 1.8.1
     ::DATATOOL:: on 01/18/2007 23:07:18
     ============================================ -->

<!-- ============================================ -->
<!-- This section is mapped from module "NCBI-Mim"
================================================= -->

<!--
********************************************************************

  MIM data definitions
  <PERSON>, 1996.
  version 2.1

********************************************************************
-->


<!ELEMENT Mim-entries (Mim-entry*)>


<!ELEMENT Mim-set (
        Mim-set_releaseDate, 
        Mim-set_mimEntries)>

<!ELEMENT Mim-set_releaseDate (Mim-date)>

<!ELEMENT Mim-set_mimEntries (Mim-entry*)>


<!ELEMENT Mim-entry (
        Mim-entry_mimNumber, 
        Mim-entry_mimType, 
        Mim-entry_title, 
        Mim-entry_copyright?, 
        Mim-entry_symbol?, 
        Mim-entry_locus?, 
        Mim-entry_synonyms?, 
        Mim-entry_aliases?, 
        Mim-entry_included?, 
        Mim-entry_seeAlso?, 
        Mim-entry_text?, 
        Mim-entry_textfields?, 
        Mim-entry_hasSummary?, 
        Mim-entry_summary?, 
        Mim-entry_summaryAttribution?, 
        Mim-entry_summaryEditHistory?, 
        Mim-entry_summaryCreationDate?, 
        Mim-entry_allelicVariants?, 
        Mim-entry_hasSynopsis?, 
        Mim-entry_clinicalSynopsis?, 
        Mim-entry_synopsisAttribution?, 
        Mim-entry_synopsisEditHistory?, 
        Mim-entry_synopsisCreationDate?, 
        Mim-entry_editHistory?, 
        Mim-entry_creationDate?, 
        Mim-entry_references?, 
        Mim-entry_attribution?, 
        Mim-entry_numGeneMaps, 
        Mim-entry_medlineLinks?, 
        Mim-entry_proteinLinks?, 
        Mim-entry_nucleotideLinks?, 
        Mim-entry_structureLinks?, 
        Mim-entry_genomeLinks?)>

<!ELEMENT Mim-entry_mimNumber (#PCDATA)>

<!ELEMENT Mim-entry_mimType (%INTEGER;)>
<!ATTLIST Mim-entry_mimType value (
        none |
        star |
        caret |
        pound |
        plus |
        perc
        ) #IMPLIED >


<!ELEMENT Mim-entry_title (#PCDATA)>

<!ELEMENT Mim-entry_copyright (#PCDATA)>

<!ELEMENT Mim-entry_symbol (#PCDATA)>

<!ELEMENT Mim-entry_locus (#PCDATA)>

<!ELEMENT Mim-entry_synonyms (Mim-entry_synonyms_E*)>


<!ELEMENT Mim-entry_synonyms_E (#PCDATA)>

<!ELEMENT Mim-entry_aliases (Mim-entry_aliases_E*)>


<!ELEMENT Mim-entry_aliases_E (#PCDATA)>

<!ELEMENT Mim-entry_included (Mim-entry_included_E*)>


<!ELEMENT Mim-entry_included_E (#PCDATA)>

<!ELEMENT Mim-entry_seeAlso (Mim-cit*)>

<!ELEMENT Mim-entry_text (Mim-text*)>

<!ELEMENT Mim-entry_textfields (Mim-text*)>

<!ELEMENT Mim-entry_hasSummary EMPTY>
<!ATTLIST Mim-entry_hasSummary value ( true | false ) #REQUIRED >


<!ELEMENT Mim-entry_summary (Mim-text*)>

<!ELEMENT Mim-entry_summaryAttribution (Mim-edit-item*)>

<!ELEMENT Mim-entry_summaryEditHistory (Mim-edit-item*)>

<!ELEMENT Mim-entry_summaryCreationDate (Mim-edit-item)>

<!ELEMENT Mim-entry_allelicVariants (Mim-allelic-variant*)>

<!ELEMENT Mim-entry_hasSynopsis EMPTY>
<!ATTLIST Mim-entry_hasSynopsis value ( true | false ) #REQUIRED >


<!ELEMENT Mim-entry_clinicalSynopsis (Mim-index-term*)>

<!ELEMENT Mim-entry_synopsisAttribution (Mim-edit-item*)>

<!ELEMENT Mim-entry_synopsisEditHistory (Mim-edit-item*)>

<!ELEMENT Mim-entry_synopsisCreationDate (Mim-edit-item)>

<!ELEMENT Mim-entry_editHistory (Mim-edit-item*)>

<!ELEMENT Mim-entry_creationDate (Mim-edit-item)>

<!ELEMENT Mim-entry_references (Mim-reference*)>

<!ELEMENT Mim-entry_attribution (Mim-edit-item*)>

<!ELEMENT Mim-entry_numGeneMaps (%INTEGER;)>

<!ELEMENT Mim-entry_medlineLinks (Mim-link)>

<!ELEMENT Mim-entry_proteinLinks (Mim-link)>

<!ELEMENT Mim-entry_nucleotideLinks (Mim-link)>

<!ELEMENT Mim-entry_structureLinks (Mim-link)>

<!ELEMENT Mim-entry_genomeLinks (Mim-link)>


<!ELEMENT Mim-text (
        Mim-text_label, 
        Mim-text_text, 
        Mim-text_neighbors?)>

<!ELEMENT Mim-text_label (#PCDATA)>

<!ELEMENT Mim-text_text (#PCDATA)>

<!ELEMENT Mim-text_neighbors (Mim-link)>


<!ELEMENT Mim-allelic-variant (
        Mim-allelic-variant_number, 
        Mim-allelic-variant_name, 
        Mim-allelic-variant_aliases?, 
        Mim-allelic-variant_mutation?, 
        Mim-allelic-variant_description?, 
        Mim-allelic-variant_snpLinks?)>

<!ELEMENT Mim-allelic-variant_number (#PCDATA)>

<!ELEMENT Mim-allelic-variant_name (#PCDATA)>

<!ELEMENT Mim-allelic-variant_aliases (Mim-allelic-variant_aliases_E*)>


<!ELEMENT Mim-allelic-variant_aliases_E (#PCDATA)>

<!ELEMENT Mim-allelic-variant_mutation (Mim-text*)>

<!ELEMENT Mim-allelic-variant_description (Mim-text*)>

<!ELEMENT Mim-allelic-variant_snpLinks (Mim-link)>


<!ELEMENT Mim-link (
        Mim-link_num, 
        Mim-link_uids, 
        Mim-link_numRelevant?)>

<!ELEMENT Mim-link_num (%INTEGER;)>

<!ELEMENT Mim-link_uids (#PCDATA)>

<!ELEMENT Mim-link_numRelevant (%INTEGER;)>


<!ELEMENT Mim-author (
        Mim-author_name, 
        Mim-author_index)>

<!ELEMENT Mim-author_name (#PCDATA)>

<!ELEMENT Mim-author_index (%INTEGER;)>


<!ELEMENT Mim-cit (
        Mim-cit_number, 
        Mim-cit_author, 
        Mim-cit_others, 
        Mim-cit_year)>

<!ELEMENT Mim-cit_number (%INTEGER;)>

<!ELEMENT Mim-cit_author (#PCDATA)>

<!ELEMENT Mim-cit_others (#PCDATA)>

<!ELEMENT Mim-cit_year (%INTEGER;)>


<!ELEMENT Mim-reference (
        Mim-reference_number, 
        Mim-reference_origNumber?, 
        Mim-reference_type?, 
        Mim-reference_authors, 
        Mim-reference_primaryAuthor, 
        Mim-reference_otherAuthors, 
        Mim-reference_citationTitle, 
        Mim-reference_citationType?, 
        Mim-reference_bookTitle?, 
        Mim-reference_editors?, 
        Mim-reference_volume?, 
        Mim-reference_edition?, 
        Mim-reference_journal?, 
        Mim-reference_series?, 
        Mim-reference_publisher?, 
        Mim-reference_place?, 
        Mim-reference_commNote?, 
        Mim-reference_pubDate, 
        Mim-reference_pages?, 
        Mim-reference_miscInfo?, 
        Mim-reference_pubmedUID?, 
        Mim-reference_ambiguous, 
        Mim-reference_noLink?)>

<!ELEMENT Mim-reference_number (%INTEGER;)>

<!ELEMENT Mim-reference_origNumber (%INTEGER;)>

<!ELEMENT Mim-reference_type %ENUM;>
<!ATTLIST Mim-reference_type value (
        not-set |
        citation |
        book |
        personal-communication |
        book-citation
        ) #REQUIRED >


<!ELEMENT Mim-reference_authors (Mim-author*)>

<!ELEMENT Mim-reference_primaryAuthor (#PCDATA)>

<!ELEMENT Mim-reference_otherAuthors (#PCDATA)>

<!ELEMENT Mim-reference_citationTitle (#PCDATA)>

<!ELEMENT Mim-reference_citationType (%INTEGER;)>

<!ELEMENT Mim-reference_bookTitle (#PCDATA)>

<!ELEMENT Mim-reference_editors (Mim-author*)>

<!ELEMENT Mim-reference_volume (#PCDATA)>

<!ELEMENT Mim-reference_edition (#PCDATA)>

<!ELEMENT Mim-reference_journal (#PCDATA)>

<!ELEMENT Mim-reference_series (#PCDATA)>

<!ELEMENT Mim-reference_publisher (#PCDATA)>

<!ELEMENT Mim-reference_place (#PCDATA)>

<!ELEMENT Mim-reference_commNote (#PCDATA)>

<!ELEMENT Mim-reference_pubDate (Mim-date)>

<!ELEMENT Mim-reference_pages (Mim-page*)>

<!ELEMENT Mim-reference_miscInfo (#PCDATA)>

<!ELEMENT Mim-reference_pubmedUID (%INTEGER;)>

<!ELEMENT Mim-reference_ambiguous EMPTY>
<!ATTLIST Mim-reference_ambiguous value ( true | false ) #REQUIRED >


<!ELEMENT Mim-reference_noLink EMPTY>
<!ATTLIST Mim-reference_noLink value ( true | false ) #REQUIRED >



<!ELEMENT Mim-index-term (
        Mim-index-term_key, 
        Mim-index-term_terms)>

<!ELEMENT Mim-index-term_key (#PCDATA)>

<!ELEMENT Mim-index-term_terms (Mim-index-term_terms_E*)>


<!ELEMENT Mim-index-term_terms_E (#PCDATA)>


<!ELEMENT Mim-edit-item (
        Mim-edit-item_author, 
        Mim-edit-item_modDate)>

<!ELEMENT Mim-edit-item_author (#PCDATA)>

<!ELEMENT Mim-edit-item_modDate (Mim-date)>


<!ELEMENT Mim-date (
        Mim-date_year, 
        Mim-date_month?, 
        Mim-date_day?)>

<!ELEMENT Mim-date_year (%INTEGER;)>

<!ELEMENT Mim-date_month (%INTEGER;)>

<!ELEMENT Mim-date_day (%INTEGER;)>


<!ELEMENT Mim-page (
        Mim-page_from, 
        Mim-page_to?)>

<!ELEMENT Mim-page_from (#PCDATA)>

<!ELEMENT Mim-page_to (#PCDATA)>


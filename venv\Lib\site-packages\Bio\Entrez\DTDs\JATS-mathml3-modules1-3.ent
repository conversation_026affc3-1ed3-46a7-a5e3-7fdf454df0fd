<!-- ============================================================= -->
<!--  MODULE:    JATS MathML 3.0 Modules                           -->
<!--  VERSION:   ANSI/NISO JATS Version 1.3 (Z39.96-2021)          -->
<!--  DATE:      June 2021                                         -->
<!--                                                               -->
<!-- ============================================================= -->

<!-- ============================================================= -->
<!--                    PUBLIC DOCUMENT TYPE DEFINITION            -->
<!--                        TYPICAL INVOCATION                     -->
<!--
"-//NLM//DTD JATS (Z39.96) JATS MathML 3.0 Modules v1.3 20210610//EN"
     Delivered as file "JATS-mathml3-modules1-3.ent"               -->
<!-- ============================================================= -->

<!-- ============================================================= -->
<!-- SYSTEM:     JATS DTD Suite                                    -->
<!--                                                               -->
<!-- PURPOSE:    To name any modules needed for MathML 3.0 (since  -->
<!--             NISO JATS V1.0 was MathML 2.0, and the two are    -->
<!--             not backwards compatible.                         -->
<!--                                                               -->
<!-- CONTAINS:   Modules declarations for MathML 3.0               -->
<!--                                                               -->
<!-- TAG SET SPONSOR                                               -->
<!--             National Center for Biotechnology                 -->
<!--                Information (NCBI)                             -->
<!--             National Library of Medicine (NLM)                -->
<!--                                                               -->
<!-- CREATED FOR:                                                  -->
<!--             This DTD was created from the JATS DTD Suite.     -->
<!--             Digital archives and publishers may use the       -->
<!--             DTD as is for markup of journal literature or     -->
<!--             related material for archiving and transferring   -->
<!--             such material between archives or create a        -->
<!--             custom XML DTD from the Suite for                 -->
<!--             these purposes.                                   -->
<!--                                                               -->
<!--             This DTD is in the public domain. An organization -->
<!--             that wishes to create its own DTD from the suite  -->
<!--             may do so without permission from NLM.            -->
<!--                                                               -->
<!--             The suite has been set up to be extended using a  -->
<!--             new DTD file and a new DTD-specific customization -->
<!--             module to redefine the many Parameter Entities.   -->
<!--             Do not modify the suite directly or redistribute  -->
<!--             modified versions of the suite.                   -->
<!--                                                               -->
<!--             In the interest of maintaining consistency and    -->
<!--             clarity for potential users, NLM requests:        -->
<!--                                                               -->
<!--             1. If you create a DTD from the JATS DTD Suite    -->
<!--                and intend to stay compatible with the suite,  -->
<!--                then please include the following statement    -->
<!--                as a comment in all of your DTD modules:       -->
<!--                   "Created from, and fully compatible with,   -->
<!--                    the ANSI/NISO Z39.96 Journal Article Tag   -->
<!--                    Suite (JATS)."                             -->
<!--                                                               -->
<!--             2. If you alter one or more modules of the suite, -->
<!--                then please rename your version and all its    -->
<!--                modules to avoid any confusion with the        -->
<!--                original suite. Also, please include the       -->
<!--                following statement as a comment in all your   -->
<!--                DTD modules:                                   -->
<!--                   "Based in part on, but not fully compatible -->
<!--                    with, the ANSI/NISO Z39.96 Journal Article -->
<!--                    Tag Suite (JATS)."                         -->
<!--                                                               -->
<!-- ORIGINAL CREATION DATE:                                       -->
<!--             November 2013                                     -->
<!--                                                               -->
<!-- CREATED BY: Mulberry Technologies, Inc. for the NISO Z39.96   -->
<!--             Working Group. Mulberry Technologies was          -->
<!--             supported by National Center for Biotechnology    -->
<!--             Information (NCBI), a center of the US National   -->
<!--             Library of Medicine (NLM).                        -->
<!--                                                               -->
<!--             The Journal Publishing DTD is built from the      -->
<!--             Archiving and Interchange DTD Suite.              -->
<!--                                                               -->
<!--             Suggestions for refinements and enhancements to   -->
<!--             this DTD should be sent in email to:              -->
<!--                 <EMAIL>                         -->
<!-- ============================================================= -->


<!-- ============================================================= -->
<!--                    DTD VERSION/CHANGE HISTORY                 -->
<!-- ============================================================= -->
<!--
Version  Reason/Occasion                   (who) vx.x (yyyy-mm-dd)
   ==============================================================
     JATS Version 1.3 (ANSI/NISO Z39.96-2021)
                                  (DAL/BTU) v1.3 (2021-06-10)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by both the NISO and ANSI memberships 
     to be changed. This version of the NISO JATS was approved 
     by ANSI vote on June 10, 2021, so it supersedes all Committee 
     Drafts as well as the draft version JATS 1.3 2021 (the version
     submitted for vote), becoming NISO JATS v1.3 20210610 
     (ANSI/NISO Z39.96-2019).

     The file names and formal public identifiers have been 
     changed in the modules and the catalogs.

 12. JATS 'v1.3d2 20201130' becomes 'v1.3 20210610' 
     (final ANSI vote date)
     'ANSI/NISO Z39.96-2019' becomes 'ANSI/NISO Z39.96-2021'

   ==============================================================
     JATS Version 1.3d2 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.3d2 (2020-11-30)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by the NISO and ANSI memberships 
     to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     of the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting; it is for Standing Committee review and
     testing. The formal public identifiers have been changed 
     in the modules and the catalogs.
 
 11. JATS "v1.3d2 20201130" becomes 'v1.3 20210610' (second 
     committee draft following the final ANSI/NISO 1.2 vote)

   ==============================================================
     JATS Version 1.3d1 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.3.d1 (2019-08-31)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by the NISO and ANSI memberships 
     to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     of the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
  
 10. JATS "1.2" becomes "v1.3d1 20190831" (first committee
     draft following the final ANSI/NISO vote) 

   ==============================================================
     JATS Version 1.2 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.2     (2019-02-08)
    
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the both the NISO and ANSI membership 
     to be changed. This version of the NISO JATS was approved 
     by ANSI vote on Feb 08, 2019, so it supersedes all Committee 
     Drafts as well as the draft version JATS 1.2 2018 (the version
     submitted for vote), becoming NISO JATS v1.2 20190208 
     (ANSI/NISO Z39.96-2021).
  
  9. JATS "1.2" becomes "v1.2 20190208" (final ANSI vote date)
     "ANSI/NISO Z39.96-2015" becomes "ANSI/NISO Z39.96-2019"
     
   ==============================================================
     JATS Version 1.2 (ANSI/NISO Z39.96-2018)
     
                                   (DAL/BTU) v1.2     (2018-11-30)
    
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the both the NISO and ANSI membership 
     to be changed. This version of the NISO JATS was approved 
     by ANSI and NISO vote, so it supersedes all Committee Drafts
     and becoming NISO JATS 1.2 (ANSI/NISO Z39.96-2018).
  
  8. 
     JATS "1.2d2" and "v1.2d2 20180401" became
     JATS "1.2" and "v1.2 20181130" 

    =============================================================
     
     JATS Version 1.2d2              (DAL/BTU) v1.2d2  (2018-04-01)

     JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
     BITS is under continuous maintenance, and is modified at the
     discretion of the working group.
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.

  7. BITS "2.0" and "v2.0 20151225" remain unchanged
      
   
     JATS "1.2d1" and "v1.2d1 20171231" became
     JATS "1.2d2" and "v1.2d2 20180401". 

     No module names were changed.
 
    =============================================================
     JATS Version 1.2d1 (ANSI/NISO Z39.96-2015)
                                   (DAL/BTU) v1.2d1  (2017-12-31)
 
     NISO JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
  
  6. JATS "1.2d1" and "v1.2d1 20170631" became
     JATS "1.2d1" and "v1.2d1 20171231". 

    =============================================================
     JATS Version 1.2d1              (DAL/BTU) v1.2d1  (2017-06-31)

     NISO JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
    
  5. JATS became version "1.2d1" and "v1.2d1 20170631" 
 
    =============================================================
     JATS Version 1.1                (DAL/BTU) v1.1  (2015-12-15)

     JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
     JATS 1.1 was approved in late 2015, so the formal public 
     identifiers were changed in the modules and the catalogs.
     No model or attribute changes were made at this time. 
 
  4. JATS became version "1.1" and "v1.1 20151215"
     BITS remained version "2.0" but becomes "v2.0 20151225"

     =============================================================
     JATS Version 1.1d3             (DAL/BTU) v1.1d3  (2015-03-01)
   
     The changes in this release are in response to NISO 
     License and Indicators Recommended Practice.

     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the NISO membership to be changed. This
     Committee Draft 1.1d3 will be sent to the NISO voting
     membership, to become (if approved) NISO JATS 1.1.
     
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee.

  3. JATS became version "1.1d3" and "v1.1d3 20150301"
  
     =============================================================
     JATS Version 1.1d2              (DAL/BTU) v1.1d2 (2014-09-30)
   
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the NISO membership to be changed. This
     Committee Draft 1.1d2 will be sent to the NISO voting
     membership, to become (if approved) NISO JATS 1.1.
     
     This catalog represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee.

   2. JATS became version "1.1d2" and "v1.1d2 20140930//EN"
  
     =============================================================
     NISO JATS Version 1.0             (DAL/BTU) v1.0 (2012-xx-xx)
   
     ANSI/NISO Z39.96-2012 (Version 1.0) 

     Details concerning ANSI/NISO Z39.96-2012 concerning 
     ANSI/NISO Z39.96-2012 JATS-based DTDs, XSDs, 
     RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.3d2/
   
  1. OASIS TABLE NAMESPACE MODIFICATIONS - Added a new module
     that sets up the namespace URI and namespace prefix for
     the OASIS tables models.
                                                                   -->

<!-- ============================================================= -->
<!-- Details concerning 
     ANSI/NISO Z39.96-2015 JATS-based DTDs, XSDs, 
     RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.1/                            -->

<!-- ============================================================= -->
<!--                     MATHML 3.0 MODULES                        -->
<!-- ============================================================= -->


<!--                    MATHML 3.0 QUALIFIED NAMES                 -->
<!ENTITY % mathml-qname.mod
                        PUBLIC
"-//W3C//ENTITIES MathML 3.0 Qualified Names 1.0//EN"
"mathml3-qname1.mod"                                                 >


<!--                    MATHML 3.0 DTD                             -->
<!ENTITY % mathml.dtd   PUBLIC
"-//W3C//DTD MathML 3.0//EN" 
"mathml3.dtd"                                                        >


<!--                    MATHML 3.0 EXTRA ENTITIES                  -->
<!ENTITY % ent-mmlextra
                        PUBLIC
"-//W3C//ENTITIES Extra for MathML 3.0//EN" 
"mathml/mmlextra.ent"                                                >


<!--                    MATHML 3.0 ALIASES                         -->
<!ENTITY % ent-mmlalias
                        PUBLIC
"-//W3C//ENTITIES Aliases for MathML 3.0//EN" 
"mathml/mmlalias.ent"                                                >


<!-- =================== End MathML 3.0 Modules ================== -->

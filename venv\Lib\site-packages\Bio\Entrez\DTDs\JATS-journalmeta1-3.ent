<!-- ============================================================= -->
<!--  MODULE:    Journal Metadata Elements                         -->
<!--  VERSION:   ANSI/NISO JATS Version 1.3 (Z39.96-2021)          -->
<!--  DATE:      June 2021                                         -->
<!--                                                               -->
<!-- ============================================================= -->

<!-- ============================================================= -->
<!--                    PUBLIC DOCUMENT TYPE DEFINITION            -->
<!--                        TYPICAL INVOCATION                     -->
<!--
"-//NLM//DTD JATS (Z39.96) JATS DTD Suite Journal Metadata Elements v1.3 20210610//EN"
     Delivered as file "JATS-journalmeta1-3.ent"                   -->
<!-- ============================================================= -->

<!-- ============================================================= -->
<!-- SYSTEM:     JATS DTD Suite                                    -->
<!--                                                               -->
<!-- PURPOSE:    Names all elements used to describe the journal   -->
<!--             in which the journal article is published.        -->
<!--                                                               -->
<!-- TAG SET SPONSOR                                               -->
<!--             National Center for Biotechnology                 -->
<!--                Information (NCBI)                             -->
<!--             National Library of Medicine (NLM)                -->
<!--                                                               -->
<!-- CREATED FOR:                                                  -->
<!--             This module was created for the JATS DTD Suite.   -->
<!--             Digital archives and publishers may use the       -->
<!--             DTD as is for markup of journal literature or     -->
<!--             related material for archiving and transferring   -->
<!--             such material between archives or create a        -->
<!--             custom XML DTD from the Suite for                 -->
<!--             these purposes.                                   -->
<!--                                                               -->
<!--             This DTD is in the public domain. An organization -->
<!--             that wishes to create its own DTD from the suite  -->
<!--             may do so without permission from NLM.            -->
<!--                                                               -->
<!--             The suite has been set up to be extended using a  -->
<!--             new DTD file and a new DTD-specific customization -->
<!--             module to redefine the many Parameter Entities.   -->
<!--             Do not modify the suite directly or redistribute  -->
<!--             modified versions of the suite.                   -->
<!--                                                               -->
<!--             In the interest of maintaining consistency and    -->
<!--             clarity for potential users, NLM requests:        -->
<!--                                                               -->
<!--             1. If you create a DTD from the JATS DTD Suite    -->
<!--                and intend to stay compatible with the suite,  -->
<!--                then please include the following statement    -->
<!--                as a comment in all of your DTD modules:       -->
<!--                   "Created from, and fully compatible with,   -->
<!--                    the ANSI/NISO Z39.96 Journal Article Tag   -->
<!--                    Suite (JATS)."                             -->
<!--                                                               -->
<!--             2. If you alter one or more modules of the suite, -->
<!--                then please rename your version and all its    -->
<!--                modules to avoid any confusion with the        -->
<!--                original suite. Also, please include the       -->
<!--                following statement as a comment in all your   -->
<!--                DTD modules:                                   -->
<!--                   "Based in part on, but not fully compatible -->
<!--                    with, the ANSI/NISO Z39.96 Journal Article -->
<!--                    Tag Suite (JATS)."                         -->
<!--                                                               -->
<!-- ORIGINAL CREATION DATE:                                       -->
<!--             December 2002                                     -->
<!--                                                               -->
<!-- CREATED BY: Mulberry Technologies, Inc. for the NISO Z39.96   -->
<!--             Working Group. Mulberry Technologies was          -->
<!--             supported by National Center for Biotechnology    -->
<!--             Information (NCBI), a center of the US National   -->
<!--             Library of Medicine (NLM).                        -->
<!--                                                               -->
<!--             This module is part of the JATS DTD Suite. The    -->
<!--             Suite is a continuation of work done by NCBI,     -->
<!--             Mulberry Technologies, and Inera, Inc. on the NLM -->
<!--             Journal Archiving and Interchange DTD Suite, which-->
<!--             was originally released in December, 2002.        -->
<!--                                                               -->
<!--             NLM thanks the Harvard University Libraries, both -->
<!--             for proposing that a draft archiving NLM DTD for  -->
<!--             life sciences journals be extended to accommodate -->
<!--             journals in all disciplines and for sponsoring    -->
<!--             Inera Inc.'s collaboration with other DTD         -->
<!--             authors in completing NLM Version 1.0. The        -->
<!--             Andrew W. Mellon Foundation provided support for  -->
<!--             these important contributions.                    -->
<!--                                                               -->
<!--             Suggestions for refinements and enhancements to   -->
<!--             this DTD should be sent in email to:              -->
<!--                 <EMAIL>                         -->
<!-- ============================================================= -->


<!-- ============================================================= -->
<!--                    DTD VERSION/CHANGE HISTORY                 -->
<!-- ============================================================= -->
<!--
Version  Reason/Occasion                   (who) vx.x (yyyy-mm-dd)
   ==============================================================
     JATS Version 1.3 (ANSI/NISO Z39.96-2021)
                                  (DAL/BTU) v1.3 (2021-06-10)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by both the NISO and ANSI memberships 
     to be changed. This version of the NISO JATS was approved 
     by ANSI vote on June 10, 2021, so it supersedes all Committee 
     Drafts as well as the draft version JATS 1.3 2021 (the version
     submitted for vote), becoming NISO JATS v1.3 20210610 
     (ANSI/NISO Z39.96-2019).

     The file names and formal public identifiers have been 
     changed in the modules and the catalogs.

 21. JATS 'v1.3d2 20201130' becomes 'v1.3 20210610' 
     (final ANSI vote date)
     'ANSI/NISO Z39.96-2019' becomes 'ANSI/NISO Z39.96-2021'

  ==============================================================
     JATS Version 1.3d2 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.3d2 (2020-11-30)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by the NISO and ANSI memberships 
     to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     of the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting; it is for Standing Committee review and
     testing. The formal public identifiers have been changed 
     in the modules and the catalogs.
 
 20. JATS "v1.3d2 20201130" becomes 'v1.3 20210610' (second 
     committee draft following the final ANSI/NISO 1.2 vote)

   ==============================================================
     JATS Version 1.3d1 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.3.d1 (2019-08-31)
    
     ANSI NISO JATS is a continuing maintenance NISO Standard, 
     which requires voting by the NISO and ANSI memberships 
     to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     of the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
  
 19. JATS "1.2" becomes "v1.3d1 20190831" (first committee
     draft following the final ANSI/NISO vote) 

   ==============================================================
     JATS Version 1.2 (ANSI/NISO Z39.96-2021)
                                   (DAL/BTU) v1.2     (2019-02-08)
    
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the both the NISO and ANSI membership 
     to be changed. This version of the NISO JATS was approved 
     by ANSI vote on Feb 08, 2019, so it supersedes all Committee 
     Drafts as well as the draft version JATS 1.2 2018 (the version
     submitted for vote), becoming NISO JATS v1.2 20190208 
     (ANSI/NISO Z39.96-2021).
  
 18. JATS "1.2" becomes "v1.2 20190208" (final ANSI vote date)
     "ANSI/NISO Z39.96-2015" becomes "ANSI/NISO Z39.96-2019"
     
   ==============================================================
     JATS Version 1.2 (ANSI/NISO Z39.96-2018)
                                   (DAL/BTU) v1.2     (2018-11-30)
    
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the both the NISO and ANSI membership 
     to be changed. This version of the NISO JATS was approved 
     by ANSI and NISO vote, so it supersedes all Committee Drafts
     and becoming NISO JATS 1.2 (ANSI/NISO Z39.96-2018).
  
 17. JATS "1.2d2" and "v1.2d2 20180401" became
     JATS "1.2" and "v1.2 20181130" 

    =============================================================
     JATS Version 1.2d2              (DAL/BTU) v1.2d2  (2018-04-01)

     JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
     BITS is under continuous maintenance, and is modified at the
     discretion of the working group.
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
 
 16. BITS "2.0" and "v2.0 20151225" remain unchanged
   
     JATS "1.2d1" and "v1.2d1 20171231" became
     JATS "1.2d2" and "v1.2d2 20180401". 

     No module names were changed.
 
    =============================================================
     JATS Version 1.2d1 (ANSI/NISO Z39.96-2015)
                                   (DAL/BTU) v1.2d1  (2017-12-31)
 
     NISO JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
  
 15. JATS "1.2d1" and "v1.2d1 20170631" became
     JATS "1.2d1" and "v1.2d1 20171231". 

    =============================================================
     JATS Version 1.2d1              (DAL/BTU) v1.2d1  (2017-06-31)

     NISO JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
      
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee. This draft has not yet been given public review 
     or voting. The formal public identifiers were changed in the 
     modules and the catalogs.
 
 14. JATS became version "1.2d1" and "v1.2d1 20170631" 

     =============================================================
     JATS Version 1.1                (DAL/BTU) v1.1  (2015-12-15)

     JATS is a continuing maintenance NISO Standard, which
     requires voting by the ANSI and NISO memberships to be changed. 
     JATS 1.1 was approved in late 2015, so the formal public 
     identifiers were changed in the modules and the catalogs.
     No model or attribute changes were made at this time. 
 
 13. JATS became version "1.1" and "v1.1 20151215"
     BITS remained version "2.0" but becomes "v2.0 20151225"

     =============================================================
     JATS Version 1.1d3             (DAL/BTU) v1.1d3  (2015-03-01)
   
     The changes in this release are in response to NISO 
     License and Indicators Recommended Practice.

     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the NISO membership to be changed. This
     Committee Draft 1.1d3 will be sent to the NISO voting
     membership, to become (if approved) NISO JATS 1.1.
     
     This draft DTD represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee.

 12. JATS became version "1.1d3" and "v1.1d3 20150301"
  
     =============================================================
     JATS Version 1.1d2              (DAL/BTU) v1.1d2 (2014-09-30)
   
     NISO JATS is a continuing maintenance NISO Standard, which 
     requires voting by the NISO membership to be changed. This
     Committee Draft 1.1d2 will be sent to the NISO voting
     membership, to become (if approved) NISO JATS 1.1.
     
     This catalog represents an interim version of the
     non-normative JATS DTD Suite, as an indication to JATS users
     the decisions that have been made by the JATS Standing
     Committee.

  11. JATS became version "1.1d2" and "v1.1d2 20140930//EN"
 
     =============================================================
     NISO JATS Version 1.1d1           (DAL/BTU) v1.1 (2013-11-15)
   
     ANSI/NISO Z39.96-2012 (pre-release for V1.0 BITS; Version 1.1d1) 

     NISO JATS Standing Committee met and answered the requests and
     suggestions from the NISO request forms.

     Details concerning ANSI/NISO Z39.96-2012 JATS-based DTDs,  
     XSDs, RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.3d2/

 10. GLOBAL ATTRIBUTES - Added the new parameter entity 
         %jats-common-atts;
     to every element in this module. This PE adds (for now) the
     @id attribute and the @xml:base attribute to every element,
     whether metadata or narrative.
     Since the @id in this parameter entity is optional, a second
     parameter entity jats-common-atts-id-required was also added.
     The two are kept in sync with the jats-base-atts parameter 
     entity.
     This added a new attribute list to:
      - journal-meta
   
     =============================================================
     NISO JATS Version 1.0             (DAL/BTU) v1.0 (2012-xx-xx)
   
     ANSI/NISO Z39.96-2012 (Version 1.0) 

     Details concerning ANSI/NISO Z39.96-2012 JATS-based DTDs,  
     XSDs, RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.3d2/
   
  9. JOURNAL METADATA MODEL
     - Added <issn-l> as an optional element to <journal-meta>
     - Added contributors and affiliations to journal metadata through
       contrib-group.class (which resolves to <contrib-group>) and 
       aff-alternatives.class (which resolves to <aff> or
       <aff-alternatives>), using the journal-meta-model.

     =============================================================
     Version 0.4                       (DAL/BTU) v0.4 (2011-01-31)
   
     This Tag Set is in the process of becoming a NISO standard.
     The version numbers are starting over from 0.4", as a Trial
     Use Draft, to be made into "Version 1.0" when the Tag Suite 
     becomes a NISO standard. Thus, the version number that would
     have been "NLM Version 3.1 Draft" has become NISO JATS
     "Version 0.4".

     Details on NISO Trial Use Draft Version 0.4 are available at
           http://jats.nlm.nih.gov/JATS-0.4.
   
  8. Updated the DTD-version attribute to "0.4" 
   
  7. Updated the public identifier to "v0.4 20110131", 
     modified the formal public identifier to include "JATS (Z39.96)",
     and the filename as delivered to include "JATS" and the
     new version number "0".

  6. I18N - Changed the default for @xml:lang from "en" to
     #IMPLIED, so that the @xml:lang would inherit properly and
     not need to be over-ridden in
      - <abbrev-journal-title> through %abbrev-journal-title-atts;
      - <journal-title> through %journal-title-atts;
      - <journal-subtitle> through %journal-subtitle-atts;

  5. JOURNAL METADATA- Added <notes> to repeat within <journal-meta>
     (through %journal-meta-model; and added @xml:lang and
     @specific-use to the attributes of <notes> through %notes-atts;

  4. @SPECIFIC-USE and @XML:LANG - Added the @specific-use and
     @xml:lang to the following elements:
      - abbrev-journal-title through abbrev-journal-title-atts
          (@specific-use-only; @xml:lang already)
      - journal-title through journal-title-atts
          (@specific-use-only;
      - journal-subtitle through journal-subtitle-atts
          (@specific-use-only)

  3. JOURNAL TITLE ELEMENTS - Removed the dependency which had both
     <journal-title> and <journal-subtitle> modeled with the same
     parameter entity %journal-title-elements;. Created new PE for
     %journal-subtitle-elements; but set it (as the default) to
     %journal-title-elements; so that no customization would break.

  2. SELF URI - Added <self-uri> to <journal-meta> (directly
     following <notes>) so that an XML document can point to the
     journal home page.

     =============================================================
     Version 3.0                       (DAL/BTU) v3.0 (2007-10-31)

     Version 3.0 is the first non-backward-compatible release.
     In addition to the usual incremental changes, some
     elements and attributes have been renamed and/or remodeled
     to better meet user needs and to increase tag set consistency.
     All module change histories are available through the Tag Suite
     web site at http://dtd.nlm.nih.gov.

     Details on version 3.0 are available at
           http://dtd.nlm.nih.gov/3.0.

  2. Changed default @xml:lang from "EN" from "en" to match latest
     RFC 4646/W3C/IANA Subtag Registry recommendations

  1. Updated public identifier to "v3.0 20071031//EN"              -->

<!-- ============================================================= -->
<!-- Details concerning 
     ANSI/NISO Z39.96-2015 JATS-based DTDs, XSDs, 
     RNGs and supporting documentation are available at
           http://jats.nlm.nih.gov/1.1/                            -->

<!-- ============================================================= -->
<!--                    DEFAULTS FOR ATTRIBUTE LISTS               -->
<!-- ============================================================= -->

<!--                    ABBREVIATED JOURNAL TITLE ATTRIBUTES       -->
<!--                    Attribute list for Abbreviated Journal Title
                        <abbrev-journal-title> element             -->
<!ENTITY % abbrev-journal-title-atts
            "%jats-common-atts;                                       
             abbrev-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    JOURNAL METADATA ATTRIBUTES                -->
<!--                    Attributes for the <journal-meta>
                        element                                    -->
<!ENTITY % journal-meta-atts
            "%jats-common-atts;"                                     >


<!--                    JOURNAL TITLE ATTRIBUTES                   -->
<!--                    Attributes for the <journal-title>
                        element                                    -->
<!ENTITY % journal-title-atts
            "%jats-common-atts;                                       
             content-type
                        CDATA                             #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang   NMTOKEN                           #IMPLIED"  >


<!--                    JOURNAL TITLE GROUP ATTRIBUTES             -->
<!--                    Attributes for the <journal-title-group>
                        element                                    -->
<!ENTITY % journal-title-group-atts
            "%jats-common-atts;                                       
             content-type
                        CDATA                              #IMPLIED" >


<!--                    JOURNAL SUBTITLE GROUP ATTRIBUTES          -->
<!--                    Attributes for the <journal-subtitle>
                        element                                    -->
<!ENTITY % journal-subtitle-atts
            "%jats-common-atts;                                       
             content-type
                        CDATA                              #IMPLIED
             specific-use
                        CDATA                             #IMPLIED
             xml:lang  NMTOKEN                            #IMPLIED"  >


<!-- ============================================================= -->
<!--                    JOURNAL METADATA                           -->
<!-- ============================================================= -->


<!--                    JOURNAL METADATA MODEL                     -->
<!--                    Content model for the journal metadata
                        element <journal-meta>                     -->
<!ENTITY % journal-meta-model
                        "(journal-id*, journal-title-group*,
                          (%contrib-group.class; |
                          %aff-alternatives.class;)*,
                          issn*, issn-l?, isbn*, 
                          publisher?, notes*, self-uri*,
                          custom-meta-group?)"                       >


<!--ELEM   journal-id   Defined in %common.ent;                    -->
<!--ELEM   issn         Defined in %common.ent;                    -->
<!--ELEM   issn-l       Defined in %common.ent;                    -->
<!--ELEM   publisher    Defined in %common.ent;                    -->
<!--ELEM   notes        Defined in %common.ent;                    -->
<!--ELEM   custom-meta-group
                        Defined in %common.ent;                    -->


<!--                    JOURNAL METADATA                           -->
<!--                    Metadata that identifies the journal in which
                        the article was published
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=journal-meta
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=journal-meta
                                                                   -->
<!ELEMENT  journal-meta %journal-meta-model;                         >
<!ATTLIST  journal-meta
             %journal-meta-atts;                                     >


<!-- ============================================================= -->
<!--                    JOURNAL TITLE ELEMENTS                     -->
<!-- ============================================================= -->


<!--                    JOURNAL TITLE GROUP MODEL                  -->
<!--                    Content model for the element
                        <journal-title-group>                      -->
<!ENTITY % journal-title-group-model
                        "(journal-title*, journal-subtitle*,
                          trans-title-group*, abbrev-journal-title*)">


<!--ELEM   trans-title-group
                        Defined in %common.ent;                    -->


<!--                    JOURNAL TITLE GROUP                        -->
<!--                    A container element to hold the title and
                        its subtitle (if any) for the journal in which
                        the article was published.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=journal-title-group
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=journal-title-group
                                                                   -->
<!ELEMENT  journal-title-group
                        %journal-title-group-model;                  >
<!ATTLIST  journal-title-group
             %journal-title-group-atts;                              >


<!--                    JOURNAL TITLE ELEMENTS                     -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        a <journal-title>
                        Design Note: This PE begins with an OR
                        bar because %just-rendition; begins with an
                        OR bar.                                    -->
<!ENTITY % journal-title-elements
                        "%just-rendition;"                           >


<!--                    JOURNAL TITLE (FULL)                       -->
<!--                    The title of the journal in which the
                        article is published.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=journal-title
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=journal-title
                                                                   -->
<!ELEMENT  journal-title
                        (#PCDATA %journal-title-elements;)*          >
<!ATTLIST  journal-title
             %journal-title-atts;                                    >


<!--                    JOURNAL SUBTITLE ELEMENTS                  -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        a <journal-subtitle>                       -->
<!ENTITY % journal-subtitle-elements
                        "%journal-title-elements;"                   >


<!--                    JOURNAL SUBTITLE                           -->
<!--                    The subtitle of the journal in which the
                        article is published.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=journal-subtitle
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=journal-subtitle
                                                                   -->
<!ELEMENT  journal-subtitle
                        (#PCDATA %journal-subtitle-elements;)*       >
<!ATTLIST  journal-subtitle
             %journal-subtitle-atts;                                 >


<!--                    ABBREVIATED JOURNAL TITLE ELEMENTS         -->
<!--                    The elements that can be included along with
                        data characters inside the content model of
                        a <abbrev-journal-title>
                        Design Note: This PE begins with an OR
                        bar because %just-rendition; begins with an
                        OR bar.                                    -->
<!ENTITY % abbrev-journal-title-elements
                        "%just-rendition;"                           >


<!--                    ABBREVIATED JOURNAL TITLE                  -->
<!--                    A short form of the title of the journal
                        in which the article is published.
                        Details at:
                        http://jats.nlm.nih.gov/archiving/tag-library/1.3/index.html?elem=abbrev-journal-title
                        http://jats.nlm.nih.gov/publishing/tag-library/1.3/index.html?elem=abbrev-journal-title
                                                                   -->
<!ELEMENT  abbrev-journal-title
                        (#PCDATA %abbrev-journal-title-elements;)*   >
<!ATTLIST  abbrev-journal-title
             %abbrev-journal-title-atts;                             >


<!-- ================== End Journal Metadata Elements  =========== -->

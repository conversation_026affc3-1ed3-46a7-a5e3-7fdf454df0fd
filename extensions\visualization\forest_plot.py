"""
森林图实现
用于展示效应量及其置信区间
"""
from typing import Dict, Any, List, Optional
import numpy as np
import plotly.graph_objects as go
from .base_plot import BasePlot

class ForestPlot(BasePlot):
    """
    森林图类，用于展示多个研究的效应量及其置信区间
    """
    
    def _create_figure(self) -> go.Figure:
        """
        创建森林图
        
        Returns:
            go.Figure: 森林图对象
        """
        fig = go.Figure()
        
        # 提取数据
        studies = self.data.get('studies', [])
        if not studies:
            return self._create_empty_plot()
            
        # 添加每个研究的效应量
        self._add_studies(fig, studies)
        
        # 添加汇总效应量（如果存在）
        if 'summary' in self.data:
            self._add_summary_effect(fig, self.data['summary'])
        
        # 更新布局
        self._update_layout(fig)
        
        return fig
    
    def _add_studies(self, fig: go.Figure, studies: List[Dict[str, Any]]) -> None:
        """
        添加各个研究到图表
        
        Args:
            fig: Plotly图表对象
            studies: 研究数据列表
        """
        # 准备数据
        y_labels = []
        effect_sizes = []
        ci_lower = []
        ci_upper = []
        sample_sizes = []
        weights = []
        
        for study in studies:
            y_labels.append(study.get('label', ''))
            effect_sizes.append(study.get('effect_size', 0))
            ci_lower.append(study.get('ci_lower', 0))
            ci_upper.append(study.get('ci_upper', 0))
            sample_sizes.append(study.get('sample_size', 0))
            weights.append(study.get('weight', 0))
        
        # 添加效应量点
        fig.add_trace(go.Scatter(
            x=effect_sizes,
            y=y_labels,
            mode='markers',
            name='效应量',
            marker=dict(
                color='#1f77b4',
                size=[min(50, 10 + w * 2) for w in weights],  # 根据权重调整点的大小
                line=dict(width=1, color='DarkSlateGrey')
            ),
            customdata=np.stack((sample_sizes, weights), axis=-1),
            hovertemplate=(
                "<b>%{y}</b><br>"
                "效应量: %{x:.2f}<br>"
                "95% CI: [%{customdata[0]:.2f}, %{customdata[1]:.2f}]<br>"
                "样本量: %{customdata[0]}<br>"
                "权重: %{customdata[1]:.1f}%<br>"
                "<extra></extra>"
            )
        ))
        
        # 添加置信区间线
        for i, (effect, lower, upper) in enumerate(zip(effect_sizes, ci_lower, ci_upper)):
            fig.add_shape(
                type='line',
                x0=lower,
                x1=upper,
                y0=i,
                y1=i,
                line=dict(color='#1f77b4', width=2)
            )
            
            # 添加垂直线段
            fig.add_shape(
                type='line',
                x0=lower,
                x1=lower,
                y0=i-0.1,
                y1=i+0.1,
                line=dict(color='#1f77b4', width=2)
            )
            fig.add_shape(
                type='line',
                x0=upper,
                x1=upper,
                y0=i-0.1,
                y1=i+0.1,
                line=dict(color='#1f77b4', width=2)
            )
    
    def _add_summary_effect(self, fig: go.Figure, summary: Dict[str, Any]) -> None:
        """
        添加汇总效应量到图表
        
        Args:
            fig: Plotly图表对象
            summary: 汇总效应量数据
        """
        effect = summary.get('effect_size', 0)
        lower = summary.get('ci_lower', 0)
        upper = summary.get('ci_upper', 0)
        
        # 添加汇总效应量线
        fig.add_shape(
            type='line',
            x0=effect,
            x1=effect,
            y0=0,
            y1=len(fig.data[0].y) - 0.5 if fig.data else 0,
            line=dict(color='red', width=2, dash='dash'),
            layer='below'
        )
        
        # 添加汇总效应量点
        fig.add_trace(go.Scatter(
            x=[effect],
            y=['汇总效应量'],
            mode='markers',
            name='汇总效应量',
            marker=dict(
                color='red',
                size=12,
                symbol='diamond'
            ),
            hovertemplate=(
                "<b>汇总效应量</b><br>"
                f"效应量: {effect:.2f}<br>"
                f"95% CI: [{lower:.2f}, {upper:.2f}]<br>"
                f"<extra></extra>"
            )
        ))
        
        # 添加汇总效应量的置信区间
        fig.add_shape(
            type='line',
            x0=lower,
            x1=upper,
            y0=len(fig.data[0].y) - 1,
            y1=len(fig.data[0].y) - 1,
            line=dict(color='red', width=3)
        )
    
    def _update_layout(self, fig: go.Figure) -> None:
        """
        更新图表布局
        
        Args:
            fig: Plotly图表对象
        """
        # 添加参考线（效应量为0或1，取决于是否使用比值比）
        use_odds_ratio = self.data.get('use_odds_ratio', False)
        ref_value = 1 if use_odds_ratio else 0
        
        fig.add_shape(
            type='line',
            x0=ref_value,
            x1=ref_value,
            y0=-1,
            y1=len(fig.data[0].y) if fig.data else 0,
            line=dict(color='black', width=1, dash='dot'),
            layer='below'
        )
        
        # 更新布局
        fig.update_layout(
            title=dict(
                text='<b>森林图</b>',
                x=0.5,
                xanchor='center',
                y=0.95,
                yanchor='top'
            ),
            xaxis=dict(
                title='<b>效应量 (95% 置信区间)</b>',
                showgrid=True,
                zeroline=False,
                showline=True,
                linewidth=1,
                linecolor='black',
                mirror=True
            ),
            yaxis=dict(
                autorange='reversed',
                showgrid=False,
                showline=False,
                linewidth=1,
                linecolor='black',
                mirror=True
            ),
            showlegend=False,
            hoverlabel=dict(
                bgcolor='white',
                font_size=12,
                font_family='Arial'
            ),
            margin=dict(l=200, r=50, t=80, b=50),
            height=max(400, 50 * len(fig.data[0].y) if fig.data else 400),
            plot_bgcolor='white'
        )
    
    def _create_empty_plot(self) -> go.Figure:
        """
        创建空图表
        
        Returns:
            go.Figure: 空图表对象
        """
        fig = go.Figure()
        fig.add_annotation(
            text="没有可用的研究数据",
            xref="paper",
            yref="paper",
            x=0.5,
            y=0.5,
            showarrow=False,
            font=dict(size=16)
        )
        fig.update_layout(
            xaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            yaxis=dict(showgrid=False, zeroline=False, showticklabels=False),
            plot_bgcolor='white'
        )
        return fig
